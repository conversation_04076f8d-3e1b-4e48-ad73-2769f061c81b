# =============================================================================
# 东北黑土区土壤剖面RDA冗余分析 - 主控制脚本
# 
# 功能：
# 1. 配置参数和路径
# 2. 调用各个分析模块
# 3. 生成高质量论文图表
# =============================================================================

# 清理环境
rm(list = ls())
gc()

# =============================================================================
# 配置参数区域
# =============================================================================

# --- 文件路径配置 ---
vif_file_path <- "E:/05 Python/Devway/01 硕士论文/00 原始数据/嫩江/嫩江_VIF_筛选变化量结果/分组VIF_筛选结果.xlsx"
change_file_path <- "E:/05 Python/Devway/01 硕士论文/00 原始数据/嫩江/嫩江_模型预测数据/嫩江_变化量.xlsx"
output_dir <- "E:/05 Python/Devway/01 硕士论文/00 原始数据/嫩江/嫩江RDA冗余分析"

# --- 分析配置开关 ---
use_county_covariate <- FALSE  # 是否将县域作为协变量
county_column_name <- "City"   # 县域列名
use_stratified_rda <- FALSE   # 是否进行分层RDA（改为整体分析以增加样本数）

# 缺失值处理配置
missing_threshold <- 0.3       # 缺失值阈值（30%）
numeric_missing_method <- "mean"
categorical_missing_method <- "mode"

# RDA显著性检验参数
permutation_tests <- 999

# 深度分层定义（基于实际深度中点值：5, 20, 45, 80）
depth_layers <- list(
  "0-10cm" = c(5),      # 深度中点5对应0-10cm
  "10-30cm" = c(20),    # 深度中点20对应10-30cm
  "30-60cm" = c(45),    # 深度中点45对应30-60cm
  "60-100cm" = c(80)    # 深度中点80对应60-100cm
)

# 响应变量定义
response_vars <- c("△pH", "△SOM", "△TN", "△TP", "△物理粘粒")

# 图表配置
plot_config <- list(
  width = 12,
  height = 10,
  dpi = 300,
  font_size = 14,
  title_size = 16,
  axis_size = 12,
  legend_size = 11,
  point_size = 2.5,
  arrow_size = 1.2,
  colors = c("#2E86AB", "#A23B72", "#F18F01", "#C73E1D", "#6A994E", "#577590")
)

# =============================================================================
# 加载依赖脚本
# =============================================================================

cat("=== 加载RDA分析模块 ===\n")

# 检查并加载必要的R包
source("06 RDA冗余分析/02_load_packages.R")

# 加载数据处理函数
source("06 RDA冗余分析/03_data_processing.R")

# 加载RDA分析函数
source("06 RDA冗余分析/04_rda_analysis.R")

# 加载高质量可视化函数
source("06 RDA冗余分析/05_publication_plots.R")

# 加载结果导出函数
source("06 RDA冗余分析/06_export_results.R")

# =============================================================================
# 主执行流程
# =============================================================================

main_analysis <- function() {
  cat("\n🚀 === 开始RDA冗余分析 ===\n")
  
  # 1. 配置验证和环境准备
  validate_config()
  create_output_dir(output_dir)
  
  # 2. 数据加载和预处理
  cat("\n📊 === 数据加载和预处理 ===\n")
  vif_data <- read_excel(vif_file_path, sheet = "筛选后数据")
  change_data <- read_excel(change_file_path)
  
  processed_result <- preprocess_data_with_transitions(vif_data, change_data, response_vars, create_transitions = TRUE)
  processed_data <- processed_result$data
  explanatory_matrix <- processed_result$explanatory_matrix
  transition_patterns <- processed_result$transition_patterns

  # 显示转移模式摘要
  if(!is.null(transition_patterns)) {
    cat("\n📈 转移模式摘要:\n")
    cat("   土地利用转移类型数:", nrow(transition_patterns$lu_matrix), "×", ncol(transition_patterns$lu_matrix), "\n")
    cat("   土壤类型转移类型数:", nrow(transition_patterns$st_matrix), "×", ncol(transition_patterns$st_matrix), "\n")
  }
  
  # 3. 执行RDA分析
  if(use_stratified_rda) {
    cat("\n📈 === 执行分层RDA分析 ===\n")
    rda_results <- perform_stratified_rda(processed_result, depth_layers, use_county_covariate)
    analysis_type <- "stratified"
  } else {
    cat("\n📈 === 执行整体RDA分析 ===\n")
    rda_results <- perform_overall_rda(processed_result, use_county_covariate)
    analysis_type <- "overall"
  }
  
  # 4. 生成高质量图表
  cat("\n🎨 === 生成图表 ===\n")
  create_publication_plots(rda_results, processed_result, output_dir, analysis_type)

  # 5. 导出详细结果
  cat("\n💾 === 导出分析结果 ===\n")
  export_rda_results(rda_results, processed_result, output_dir, analysis_type)
  
  # 6. 分析完成
  cat("\n📋 === 分析完成 ===\n")
  cat("✅ RDA分析成功完成\n")
  cat("✅ 解释度:", ifelse(analysis_type == "overall",
                      paste0(round(rda_results$explained_variance, 2), "%"),
                      "见各层结果"), "\n")
  
  cat("\n🎉 === RDA分析完成 ===\n")
  cat("📁 所有结果已保存至:", output_dir, "\n")
  
  return(rda_results)
}

# =============================================================================
# 脚本执行
# =============================================================================

cat("\n=== RDA冗余分析系统已加载 ===\n")
cat("📊 当前配置:\n")
cat("   - 分层RDA:", ifelse(use_stratified_rda, "启用", "禁用"), "\n")
cat("   - 县域协变量:", ifelse(use_county_covariate, "启用", "禁用"), "\n")
cat("   - 响应变量:", paste(response_vars, collapse = ", "), "\n")
cat("   - 输出目录:", output_dir, "\n")

# 自动执行分析
cat("\n🚀 开始自动执行分析...\n")
results <- main_analysis()
