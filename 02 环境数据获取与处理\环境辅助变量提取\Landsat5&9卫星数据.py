# -*- coding: utf-8 -*-
"""
================================================================================
          长时序、多传感器遥感指数批量计算与导出 (Google Earth Engine)
================================================================================

核心功能:
- **跨年份处理**: 支持处理指定年份列表 (例如 [1980, 2023]) 的数据。
- **智能传感器选择**: 自动根据年份选择最适用的Landsat传感器 (L5/L8/L9)。
- **年份智能匹配**: 若指定年份无数据 (如1980年)，自动查找并使用最接近的、
  有完整数据的年份 (如1985年) 作为替代，并在日志中明确提示。
- **动态波段映射**: 自动为不同传感器匹配正确的波段名称进行计算，确保长时序分析的一致性。
- **全指数计算**: 批量计算十余种常用的植被、水体、土壤、建成区及颜色相关指数。
- **统一预处理**: 对所有影像应用标准化的缩放因子和云掩码流程 (Landsat Collection 2)。
- **自动化导出**: 将所有计算出的指数和基础波段，按年份分别创建子文件夹后，导出至Google Drive。
  地表温度(LST)会自动转换为摄氏度(°C)后再导出。

使用前提:
1. 已正确配置Google Earth Engine Python API环境。
2. 在GEE Assets中拥有一个有效的矢量边界 (ROI_PATH)。
3. GEE项目ID (GEE_PROJECT_ID) 具有访问权限。
"""

import ee
import time

# --- 1. 核心参数配置 ---
# GEE项目ID
GEE_PROJECT_ID = 'ee-aethxz247'

# 定义目标年份
# 脚本会自动查找每个年份最接近的可用数据。
YEARS = [1980, 2023]

# 定义研究区域 (Region of Interest, ROI)
# 请确保此路径在您的GEE Assets中有效。
ROI_PATH = 'projects/ee-aethxz247/assets/DSS'

# Google Drive 中用于保存结果的根文件夹名称
OUTPUT_FOLDER = 'GEE_MultiYear_Indices_Exports'

# 导出影像的空间分辨率 (米)
EXPORT_SCALE = 250

# 导出的目标坐标参考系 (CRS)
# 例如 'EPSG:4326' (WGS 84) 或 'EPSG:4550' (CGCS2000 / Gauss-Krüger zone 19)。
# 请根据您的研究区确认最佳选择。
EXPORT_CRS = 'EPSG:4550'

# 不同Landsat传感器的波段映射表
# 确保在计算指数时使用正确的波段名称。
BAND_MAPPING = {
    'L9': {  # Landsat 9 (2022年至今)
        'blue': 'SR_B2', 'green': 'SR_B3', 'red': 'SR_B4',
        'nir': 'SR_B5', 'swir1': 'SR_B6', 'swir2': 'SR_B7',
        'lst': 'ST_B10',
        'collection_id': 'LANDSAT/LC09/C02/T1_L2'
    },
    'L8': {  # Landsat 8 (2013年至2021年)
        'blue': 'SR_B2', 'green': 'SR_B3', 'red': 'SR_B4',
        'nir': 'SR_B5', 'swir1': 'SR_B6', 'swir2': 'SR_B7',
        'lst': 'ST_B10',
        'collection_id': 'LANDSAT/LC08/C02/T1_L2'
    },
    'L5': {  # Landsat 5 (1984年至2012年)
        'blue': 'SR_B1', 'green': 'SR_B2', 'red': 'SR_B3',
        'nir': 'SR_B4', 'swir1': 'SR_B5', 'swir2': 'SR_B7',
        'lst': 'ST_B6',
        'collection_id': 'LANDSAT/LT05/C02/T1_L2'
    }
}
# 备注: 缨帽变换(Tasseled Cap)已被移除。由于Landsat 5没有官方发布的SR产品缨帽变换系数，
# 为保证长时序数据的绝对可比性，此功能已从脚本中排除。


# --- 2. GEE 初始化与研究区加载 ---
try:
    ee.Initialize(project=GEE_PROJECT_ID, opt_url='https://earthengine-highvolume.googleapis.com')
    print(f"GEE初始化成功！使用项目: {GEE_PROJECT_ID}")
except Exception as e:
    print(f"GEE初始化失败。错误: {e}")
    print("请检查网络连接、GEE项目ID是否正确，或尝试 'earthengine authenticate' 命令重新认证。")
    exit()

try:
    roi = ee.FeatureCollection(ROI_PATH).geometry().bounds()
    roi.area(maxError=1).getInfo()  # 尝试获取ROI信息以验证其有效性
    print(f"研究区域(ROI)加载成功: '{ROI_PATH}'")
except Exception as e:
    print(f"错误: 无法加载研究区域(ROI)。请确认路径 '{ROI_PATH}' 是否正确，以及您是否有权访问。")
    print(f"GEE返回的错误信息: {e}")
    exit()


# --- 3. 核心功能函数 ---

def find_closest_available_year(year):
    """
    根据目标年份，查找最接近的有完整全年Landsat数据的年份。

    数据可用性参考:
    - 1985-2012: Landsat 5 (1984年3月发射，首个完整数据年为1985年)
    - 2013-2021: Landsat 8
    - 2022-至今: Landsat 9

    Args:
        year (int): 目标年份。

    Returns:
        int: 最接近的、有数据的年份。
    """
    if year >= 1985:
        # 1985年及以后，数据连续且完整，直接返回原年份。
        return year
    else:
        # 1985年以前，最近的、有完整数据的年份是1985年。
        print(f"  --> 警告: {year}年无完整可用数据，自动使用最接近的1985年数据作为替代。")
        return 1985

def apply_scaling_factors(image):
    """
    对Landsat Collection 2 Level 2影像应用官方指定的缩放因子和偏移量。
    此函数对Landsat 5/8/9的SR和ST产品均适用。
    - 光学波段 (SR): multiply(0.0000275).add(-0.2)
    - 热红外波段 (ST): multiply(0.00341802).add(149.0) -> 单位: 开尔文(K)
    """
    opticalBands = image.select('SR_B[1-9]').multiply(0.0000275).add(-0.2)  # 仅匹配1-9号光学波段, 排除热红外
    thermalBands = image.select('ST_B.*').multiply(0.00341802).add(149.0)
    return image.addBands(opticalBands, None, True).addBands(thermalBands, None, True)

def mask_landsat_clouds(image):
    """
    利用QA_PIXEL和QA_RADSAT波段，为Landsat C2 L2影像创建云、阴影和饱和像素的掩码。
    此QA掩码逻辑对Landsat 4-9均适用。
    """
    qa_pixel = image.select('QA_PIXEL')
    # class 0: clear; class 1: dilated cloud; class 2: cirrus; class 3: cloud; class 4: cloud shadow
    qa_mask = qa_pixel.bitwiseAnd(int('11111', 2)).eq(0)
    saturation_mask = image.select('QA_RADSAT').eq(0)
    return image.updateMask(qa_mask).updateMask(saturation_mask)

def calculate_all_indices(image, bands):
    """
    基于输入的影像和波段映射，计算所有光谱指数。
    所有指数均可在Landsat 5, 8, 9上计算，因为它们共享必需的基础波段。

    Args:
        image (ee.Image): 经过预处理的单张影像。
        bands (dict): 对应传感器的波段名称映射字典。

    Returns:
        ee.Image: 包含所有新计算指数波段的原始影像。
    """
    blue = image.select(bands['blue'])
    green = image.select(bands['green'])
    red = image.select(bands['red'])
    nir = image.select(bands['nir'])
    swir1 = image.select(bands['swir1'])
    swir2 = image.select(bands['swir2'])

    # --- 植被指数 (无单位, -1到+1) ---
    ndvi = image.normalizedDifference([bands['nir'], bands['red']]).rename('NDVI')
    rvi  = nir.divide(red).rename('RVI')
    savi = image.expression('(NIR - RED) * (1 + L) / (NIR + RED + L)',
                            {'NIR': nir, 'RED': red, 'L': 0.5}).rename('SAVI')
    evi  = image.expression('2.5 * ((NIR - RED) / (NIR + 6 * RED - 7.5 * BLUE + 1))',
                            {'NIR': nir, 'RED': red, 'BLUE': blue}).rename('EVI')
    msavi = image.expression('(2 * NIR + 1 - sqrt(pow(2 * NIR + 1, 2) - 8 * (NIR - RED))) / 2',
                             {'NIR': nir, 'RED': red}).rename('MSAVI')
    gndvi = image.normalizedDifference([bands['nir'], bands['green']]).rename('GNDVI')

    # --- 土壤、建成区与水体指数 (无单位, -1到+1) ---
    bsi = image.expression('((SWIR1 + RED) - (NIR + BLUE)) / ((SWIR1 + RED) + (NIR + BLUE))',
                          {'SWIR1': swir1, 'RED': red, 'NIR': nir, 'BLUE': blue}).rename('BSI')
    ibi_num = image.expression('(2*SWIR1/(SWIR1+NIR)) - ((NIR/(NIR+RED)) + (GREEN/(GREEN+SWIR1)))',
                               {'SWIR1': swir1, 'NIR': nir, 'RED': red, 'GREEN': green})
    ibi_den = image.expression('(2*SWIR1/(SWIR1+NIR)) + ((NIR/(NIR+RED)) + (GREEN/(GREEN+SWIR1)))',
                               {'SWIR1': swir1, 'NIR': nir, 'RED': red, 'GREEN': green})
    ibi = ibi_num.divide(ibi_den).rename('IBI')
    ndwi = image.normalizedDifference([bands['green'], bands['nir']]).rename('NDWI')
    mndwi = image.normalizedDifference([bands['green'], bands['swir1']]).rename('MNDWI')
    ndbi = image.normalizedDifference([bands['swir1'], bands['nir']]).rename('NDBI')

    # --- 颜色相关指数 ---
    si = image.normalizedDifference([bands['red'], bands['blue']]).rename('Saturation_Index')
    hi_num = green.subtract(blue).multiply(ee.Number(3).sqrt())
    hi_den = red.multiply(2).subtract(green).subtract(blue)
    hi = ee.Image.atan2(hi_num, hi_den).rename('Hue_Index')  # 色调指数 (弧度)
    ci = image.normalizedDifference([bands['red'], bands['green']]).rename('Coloration_Index')
    ri = red.pow(2).divide(green.multiply(blue).add(1e-6)).rename('Redness_Index')

    # --- 整合所有波段 ---
    all_new_bands = [
        ndvi, rvi, savi, evi, bsi, ibi, ndwi, msavi, gndvi, mndwi, ndbi,
        si, hi, ci, ri
    ]
    return image.addBands(all_new_bands)

def export_to_drive(image, description, folder_year, file_year, folder):
    """
    统一的导出函数。可区分"文件夹年份"(目标年份) 与 "文件年份"(实际影像年份)。

    Args:
        image (ee.Image): 待导出的影像。
        description (str): 指数或波段名称，用作文件名前缀的一部分。
        folder_year (int): 用于创建Google Drive子文件夹的年份 (目标年份)。
        file_year (int): 用于命名文件的年份 (实际使用的影像年份)。
        folder (str): Google Drive中的根文件夹名。
    """
    task_description = f'{description}_{file_year}'
    folder_path = f"{folder}/{folder_year}"  # 按目标年份创建子文件夹

    task = ee.batch.Export.image.toDrive(
        image=image.toFloat(),
        description=task_description,
        folder=folder_path,
        fileNamePrefix=task_description,
        region=roi,
        scale=EXPORT_SCALE,
        crs=EXPORT_CRS,
        maxPixels=1e13,
        fileFormat='GeoTIFF'
    )
    task.start()
    print(f"  --> GEE任务 '{task_description}' 已启动，导出至 Drive 文件夹: '{folder_path}'。")


# --- 4. 主执行流程 ---
def main():
    """主函数，编排所有数据处理和导出任务。"""
    script_start_time = time.time()
    print(f"--- 开始处理目标年份: {YEARS} ---")

    for target_year in YEARS:
        # 步骤 0: 确定实际使用的数据年份
        year = find_closest_available_year(target_year)
        print(f"\n--- 正在处理目标年份 {target_year} (使用 {year} 年的数据) ---")
        
        # 步骤 1: 根据年份智能选择传感器和波段映射
        if year >= 2022:
            sensor = 'L9'
        elif year >= 2013:
            sensor = 'L8'
        else:
            sensor = 'L5'
        
        bands = BAND_MAPPING[sensor]
        collection_id = bands['collection_id']
        print(f"  --> 数据源: {sensor} ({collection_id})")

        # 步骤 2: 加载并预处理全年的Landsat影像集
        print("  [1] 加载并预处理全年影像...")
        start_date = f'{year}-01-01'
        end_date = f'{year}-12-31'
        
        landsat_collection = (ee.ImageCollection(collection_id)
                              .filterBounds(roi)
                              .filterDate(start_date, end_date)
                              .map(apply_scaling_factors)
                              .map(mask_landsat_clouds))
        
        # 使用中位数合成，能更好地去除离群值（如未被掩膜的云、噪声）
        landsat_composite = landsat_collection.median().clip(roi)
        print("      ...全年无云中位数合成影像创建完成。")

        # 步骤 3: 计算所有光谱指数
        print("  [2] 计算所有光谱指数...")
        indices_image = calculate_all_indices(landsat_composite, bands)
        print("      ...所有指数计算完成。")
        
        # 步骤 4: 准备并启动所有导出任务
        print("  [3] 启动所有导出任务...")
        
        # 导出基础光谱波段 (LST将转换为摄氏度)
        base_bands_to_export = {
            'Blue': bands['blue'], 'Green': bands['green'], 'Red': bands['red'],
            'NIR': bands['nir'], 'SWIR1': bands['swir1'], 'SWIR2': bands['swir2'],
            'LST': bands['lst']
        }
        for name, band_name in base_bands_to_export.items():
            img_to_export = landsat_composite.select(band_name)
            # --- 新增: LST 转为摄氏度并重命名 ---
            if name == 'LST':
                img_to_export = img_to_export.subtract(273.15).rename('LST_Celsius')
                export_name = 'LST_Celsius'
            else:
                export_name = name
            export_to_drive(img_to_export, export_name, target_year, year, OUTPUT_FOLDER)

        # 导出所有计算出的光谱指数
        indices_list = [
            'NDVI', 'RVI', 'SAVI', 'EVI', 'NDWI', 'IBI', 'BSI',
            'MSAVI', 'GNDVI', 'MNDWI', 'NDBI',
            'Saturation_Index', 'Hue_Index', 'Coloration_Index', 'Redness_Index'
        ]
        
        for index_name in indices_list:
            export_to_drive(indices_image.select(index_name), index_name, target_year, year, OUTPUT_FOLDER)

    print(f"\n✅ 所有年份的所有导出任务均已成功启动！")
    print(f"请访问 https://code.earthengine.google.com/tasks 页面查看并手动运行它们。")
    script_end_time = time.time()
    print(f"\n脚本启动流程完成，总耗时: {script_end_time - script_start_time:.2f} 秒。")


# --- 5. 脚本入口 ---
if __name__ == '__main__':
    main() 