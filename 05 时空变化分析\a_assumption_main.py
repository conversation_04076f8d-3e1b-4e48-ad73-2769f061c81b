# ---- 正态检验主程序 ----
"""
正态性和方差齐性检验主程序
与方差分析使用相同的分组逻辑，确保检验一致性
"""

import pandas as pd
import numpy as np
from b_assumption_tests import test_assumptions, batch_test
from C_data_loader import preprocess_data, init_config
from B_config import INDICATORS, COUNTIES, YEARS, DEPTHS, ALPHA, ANALYSIS_TYPES, ANALYSIS_SCOPE, COLUMNS

def check_assumptions_by_analysis_type(data, available_indicators, analysis_type, filter_counties=None):
    """
    根据分析类型执行相应的正态性检验
    确保与方差分析的分组逻辑完全一致
    """
    if analysis_type not in ANALYSIS_TYPES:
        print(f"未知分析类型: {analysis_type}")
        return {}
    
    config = ANALYSIS_TYPES[analysis_type]
    group_by = config['group_by']
    split_by = config['split_by'] 
    filter_by = config['filter_by']
    
    print(f"\n{'='*50}")
    print(f"执行 {config['name']} 的正态性检验")
    print(f"分组变量: {group_by}")
    if split_by:
        print(f"分层变量: {split_by}")
    if filter_by:
        print(f"筛选变量: {filter_by}")
    print(f"{'='*50}")
    
    results = {}
    
    # 获取实际的列名
    county_col = COLUMNS['county']
    year_col = COLUMNS['year']
    depth_col = COLUMNS['depth']
    
    # 根据分析类型进行不同的检验
    if analysis_type == 'spatial':
        # 县城对比：按年份分层，以县城为分组
        for year in YEARS:
            year_data = data[data[year_col] == year]
            if filter_counties:
                year_data = year_data[year_data[county_col].isin(filter_counties)]
            
            for indicator_name in available_indicators.keys():
                if indicator_name in year_data.columns:
                    key = f"{year}_{indicator_name}"
                    try:
                        result = test_assumptions(
                            year_data, indicator_name, county_col, ALPHA
                        )
                        results[key] = result
                        # 显示详细检验结果
                        if 'recommendation' in result:
                            rec = result['recommendation']
                            method = "参数检验" if rec['use_parametric'] else "非参数检验"

                            # 获取检验统计信息
                            sw_overall = result['normality_tests']['overall']['shapiro_wilk']
                            levene_result = result['homogeneity_tests']['levene']

                            print(f"  {year}-{indicator_name}:")
                            print(f"    正态性: p={sw_overall['p_value']:.4f}, 正态={sw_overall['is_normal']}")
                            print(f"    方差齐性: p={levene_result['p_value']:.4f}, 齐性={levene_result['equal_variances']}")
                            print(f"    建议: {method}")
                    except Exception as e:
                        print(f"  {year}-{indicator_name}: 检验出错")
                        # 创建一个默认的非参数结果
                        results[key] = {
                            'recommendation': {
                                'use_parametric': False,
                                'use_nonparametric': True,
                                'suggested_tests': ['Kruskal-Wallis H检验']
                            }
                        }
    
    elif analysis_type == 'temporal':
        # 时间对比：按县城分层，以年份为分组
        target_counties = filter_counties if filter_counties else COUNTIES
        for county in target_counties:
            county_data = data[data[county_col] == county]
            
            for indicator_name in available_indicators.keys():
                if indicator_name in county_data.columns:
                    key = f"{county}_{indicator_name}"
                    try:
                        result = test_assumptions(
                            county_data, indicator_name, year_col, ALPHA
                        )
                        results[key] = result
                        if 'recommendation' in result:
                            rec = result['recommendation']
                            method = "参数检验" if rec['use_parametric'] else "非参数检验"

                            # 获取检验统计信息
                            sw_overall = result['normality_tests']['overall']['shapiro_wilk']
                            levene_result = result['homogeneity_tests']['levene']

                            print(f"  {county}-{indicator_name}:")
                            print(f"    正态性: p={sw_overall['p_value']:.4f}, 正态={sw_overall['is_normal']}")
                            print(f"    方差齐性: p={levene_result['p_value']:.4f}, 齐性={levene_result['equal_variances']}")
                            print(f"    建议: {method}")
                    except Exception as e:
                        print(f"  {county}-{indicator_name}: 检验出错")
                        results[key] = {
                            'recommendation': {
                                'use_parametric': False,
                                'use_nonparametric': True,
                                'suggested_tests': ['Kruskal-Wallis H检验']
                            }
                        }
    
    elif analysis_type == 'depth_time':
        # 深度时间对比：按县城和深度分层，以年份为分组
        target_counties = filter_counties if filter_counties else COUNTIES
        for county in target_counties:
            county_data = data[data[county_col] == county]
            for depth in DEPTHS:
                depth_data = county_data[county_data[depth_col] == depth]
                
                if len(depth_data) == 0:
                    continue
                
                for indicator_name in available_indicators.keys():
                    if indicator_name in depth_data.columns:
                        key = f"{county}_{indicator_name}_{depth}"
                        try:
                            result = test_assumptions(
                                depth_data, indicator_name, year_col, ALPHA
                            )
                            results[key] = result
                            if 'recommendation' in result:
                                rec = result['recommendation']
                                method = "参数检验" if rec['use_parametric'] else "非参数检验"

                                # 获取检验统计信息
                                sw_overall = result['normality_tests']['overall']['shapiro_wilk']
                                levene_result = result['homogeneity_tests']['levene']

                                print(f"  {county}-{depth}-{indicator_name}:")
                                print(f"    正态性: p={sw_overall['p_value']:.4f}, 正态={sw_overall['is_normal']}")
                                print(f"    方差齐性: p={levene_result['p_value']:.4f}, 齐性={levene_result['equal_variances']}")
                                print(f"    建议: {method}")
                        except Exception as e:
                            print(f"  {county}-{depth}-{indicator_name}: 检验出错")
                            results[key] = {
                                'recommendation': {
                                    'use_parametric': False,
                                    'use_nonparametric': True,
                                    'suggested_tests': ['Kruskal-Wallis H检验']
                                }
                            }
    
    elif analysis_type == 'depth_profile':
        # 深度剖面对比：按县城和年份分层，以深度为分组
        target_counties = filter_counties if filter_counties else COUNTIES
        for county in target_counties:
            county_data = data[data[county_col] == county]
            for year in YEARS:
                year_county_data = county_data[county_data[year_col] == year]
                
                if len(year_county_data) == 0:
                    continue
                
                for indicator_name in available_indicators.keys():
                    if indicator_name in year_county_data.columns:
                        key = f"{year}_{county}_{indicator_name}_by_depth"
                        try:
                            result = test_assumptions(
                                year_county_data, indicator_name, depth_col, ALPHA
                            )
                            results[key] = result
                            if 'recommendation' in result:
                                rec = result['recommendation']
                                method = "参数检验" if rec['use_parametric'] else "非参数检验"

                                # 获取检验统计信息
                                sw_overall = result['normality_tests']['overall']['shapiro_wilk']
                                levene_result = result['homogeneity_tests']['levene']

                                print(f"  {year}-{county}-{indicator_name}:")
                                print(f"    正态性: p={sw_overall['p_value']:.4f}, 正态={sw_overall['is_normal']}")
                                print(f"    方差齐性: p={levene_result['p_value']:.4f}, 齐性={levene_result['equal_variances']}")
                                print(f"    建议: {method}")
                        except Exception as e:
                            print(f"  {year}-{county}-{indicator_name}: 检验出错")
                            results[key] = {
                                'recommendation': {
                                    'use_parametric': False,
                                    'use_nonparametric': True,
                                    'suggested_tests': ['Kruskal-Wallis H检验']
                                }
                            }
    
    return results

def main():
    """主函数，执行正态性和方差齐性检验"""
    print("="*60)
    print("土壤数据正态性和方差齐性检验程序")
    print("与方差分析分组逻辑完全一致")
    print("="*60)
    
    # 初始化数据配置和加载数据
    print("\n正在初始化配置和加载数据...")
    if not init_config():
        print("数据配置初始化失败，程序退出")
        return

    # 重新导入更新后的配置
    from B_config import COUNTIES, YEARS, DEPTHS, INDICATORS

    # 确定分析范围
    if ANALYSIS_SCOPE == "all":
        target_counties = COUNTIES
    else:
        target_counties = ANALYSIS_SCOPE

    try:
        processed_data, available_indicators = preprocess_data()

        # 从processed_data中提取完整的数据框用于检验
        combined_data_list = []
        for year in YEARS:
            for county in COUNTIES:
                if year in processed_data and county in processed_data[year]:
                    first_indicator = list(available_indicators.keys())[0]
                    if first_indicator in processed_data[year][county]:
                        df = processed_data[year][county][first_indicator].copy()
                        combined_data_list.append(df)

        if combined_data_list:
            data = pd.concat(combined_data_list, ignore_index=True)
            print(f"数据加载完成: {data.shape[0]}行数据, {len(available_indicators)}个指标")
            print(f"包含: {len(COUNTIES)}个县城, {len(YEARS)}个年份, {len(DEPTHS)}个深度层")
        else:
            print("没有找到有效数据")
            return

    except Exception as e:
        print(f"数据加载失败: {e}")
        return
    
    # 执行各种分析类型的正态性检验
    all_results = {}

    print(f"\n开始执行正态性和方差齐性检验 (α = {ALPHA})...")
    print("="*60)
    
    for analysis_type, analysis_config in ANALYSIS_TYPES.items():
        # 检查是否是空间对比且分析范围不是全部（跳过空间对比）
        if analysis_type == 'spatial' and ANALYSIS_SCOPE != "all":
            print(f"\n跳过 {analysis_config['name']} 检验（仅在全面分析模式下执行）")
            continue
        
        # 对目标县城应用筛选
        filter_counties = target_counties if analysis_type != 'spatial' else COUNTIES
        
        # 执行检验
        try:
            type_results = check_assumptions_by_analysis_type(
                data, available_indicators, analysis_type, filter_counties
            )
            all_results.update(type_results)
        except Exception as e:
            print(f"{analysis_config['name']} 检验过程中出错: {e}")
    
    # 汇总统计结果
    print(f"\n4. 检验结果汇总:")
    print(f"{'='*60}")
    
    total_tests = len(all_results)
    parametric_count = 0
    nonparametric_count = 0
    
    analysis_summary = {}
    
    for key, result in all_results.items():
        if 'recommendation' in result:
            rec = result['recommendation']
            is_parametric = rec['use_parametric']
            
            if is_parametric:
                parametric_count += 1
            else:
                nonparametric_count += 1
            
            # 按分析类型分组统计
            analysis_type = None
            for atype in ANALYSIS_TYPES.keys():
                if atype in ['spatial', 'temporal'] and atype in key:
                    analysis_type = atype
                    break
                elif 'by_depth' in key:
                    analysis_type = 'depth_profile'
                    break
                elif any(depth in key for depth in DEPTHS):
                    analysis_type = 'depth_time'
                    break
            
            if analysis_type:
                if analysis_type not in analysis_summary:
                    analysis_summary[analysis_type] = {'parametric': 0, 'nonparametric': 0}
                
                if is_parametric:
                    analysis_summary[analysis_type]['parametric'] += 1
                else:
                    analysis_summary[analysis_type]['nonparametric'] += 1
    
    print(f"总体统计:")
    print(f"  - 总检验次数: {total_tests}")
    print(f"  - 建议参数检验: {parametric_count} 次")
    print(f"  - 建议非参数检验: {nonparametric_count} 次")
    if total_tests > 0:
        print(f"  - 非参数检验比例: {nonparametric_count/total_tests*100:.1f}%")
    else:
        print(f"  - 非参数检验比例: 无数据")
    
    print(f"\n按分析类型统计:")
    for atype, counts in analysis_summary.items():
        total = counts['parametric'] + counts['nonparametric']
        nonparam_pct = counts['nonparametric'] / total * 100 if total > 0 else 0
        print(f"  {ANALYSIS_TYPES[atype]['name']}:")
        print(f"    - 参数检验: {counts['parametric']} 次")
        print(f"    - 非参数检验: {counts['nonparametric']} 次")
        print(f"    - 非参数比例: {nonparam_pct:.1f}%")
    
    print(f"\n{'='*60}")
    print("检验程序执行完毕！")
    print("建议: 由于大部分数据可能不满足正态性和方差齐性假设，")
    print("      推荐在方差分析中使用非参数检验方法（如 Kruskal-Wallis H 检验）")
    print(f"{'='*60}")

if __name__ == "__main__":
    main()