# 多年份、多传感器遥感指数批量计算与导出脚本

本项目提供一个高效、智能的Python脚本，用于通过Google Earth Engine (GEE) 平台，批量计算、合成和导出跨越长时间序列（例如1980年和2023年）的多种遥感指数。脚本的核心优势在于其能够 **自动为目标年份选择最合适的卫星数据源和最接近的数据年份**，并只计算在所有时期均可比的指标，确保了长时序对比分析的科学性和便捷性。

## 主要功能与核心优势

- **✨ 智能传感器与年份适配**:
    - **自动选择卫星**: 脚本能够根据目标年份，自动选择当时最先进、数据质量最高的Landsat卫星（L9, L8, 或 L5）。
    - **自动匹配年份**: 如果您指定的年份没有可用的卫星数据（如1985年以前），脚本会自动使用**最接近的、有完整全年数据的年份**（例如，指定1980年会使用1985年的数据）进行分析，并输出提示。
- **🌍 全年数据处理**: 脚本获取并处理每个目标年份 **一整年** 的影像，通过去云和均值合成，生成一个能够代表该年度整体状况的稳健影像。
- **🔬 绝对的指数可比性**: 严格筛选并只纳入那些在`Landsat 5, 8, 9`上均可计算的指数和波段，确保时间序列分析的有效性和一致性。
- **⚙️ 动态波段匹配与缩放**: 内置了波段映射机制，自动为不同传感器的同名波段匹配正确的波段ID和官方推荐的缩放因子，保证了计算的准确性。

## 如何使用

### 1. 环境配置

在运行脚本前，请确保您已经安装了必要的Python库。

```bash
pip install earthengine-api google-api-python-client
```

同时，您需要完成Google Earth Engine的本地授权。如果尚未授权，请运行以下命令并按照提示操作：

```bash
earthengine authenticate
```

### 2. 参数配置

打开 `RSEI_and_spectral_indices_GEE.py` 文件，根据您的需求修改顶部的 **"核心参数配置"**区域：

- `GEE_PROJECT_ID`: 您的Google Earth Engine项目ID。
- `YEARS`: 您希望处理的目标年份列表。例如 `[1980, 2023]`。脚本会自动处理数据缺失的年份。
- `ROI_PATH`: 指向您在GEE Assets中存储的研究区边界的路径。
- `OUTPUT_FOLDER`: 在您Google Drive中用于保存导出结果的文件夹名称。
- `EXPORT_SCALE`: 导出影像的分辨率（单位：米）。
- `EXPORT_CRS`: 导出影像的目标坐标参考系（例如 'EPSG:4550'）。

### 3. 运行脚本

配置完成后，通过命令行直接运行脚本：

```bash
python RSEI_and_spectral_indices_GEE.py
```

脚本会在终端输出每个年份的处理进度，包括使用了哪个传感器，并自动启动所有GEE的导出任务。

### 4. 查看和执行任务

脚本运行后，它仅仅是 **启动** 了GEE云端的计算任务。您需要访问 [Google Earth Engine Tasks 页面](https://code.earthengine.google.com/tasks) 来查看这些任务，并手动点击 **"Run"** 按钮来真正执行它们。任务完成后，所有结果文件将自动保存到您在Google Drive中指定的文件夹内。

## 产出说明

脚本将为每个年份的每个指数和基础波段生成一个独立的 `GeoTIFF` 文件。文件名和文件夹将以您在 `YEARS` 列表中指定的 **目标年份** 命名。例如，即使为1980年的请求使用了1985年的数据，输出文件也会被标记为 `_1980`，以保持与您原始请求的一致性。

### 导出的基础波段和单位

| 波段名称 | 描述 | 单位 |
| :--- | :--- | :--- |
| `Blue` | 蓝光波段 | 反射率 (无单位) |
| `Green` | 绿光波段 | 反射率 (无单位) |
| `Red` | 红光波段 | 反射率 (无单位) |
| `NIR` | 近红外波段 | 反射率 (无单位) |
| `SWIR1` | 短波红外1 | 反射率 (无单位) |
| `SWIR2` | 短波红外2 | 反射率 (无单位) |
| `LST` | 地表温度 | 开尔文 (K) |

### 导出的可比光谱指数和单位

| 指数名称 | 全称 | 单位 | 适用年份 |
| :--- | :--- | :--- | :--- |
| **NDVI** | 归一化植被指数 | 无单位 | 所有年份 |
| **RVI** | 比值植被指数 | 无单位 | 所有年份 |
| **SAVI** | 土壤调节植被指数 | 无单位 | 所有年份 |
| **EVI** | 增强型植被指数 | 无单位 | 所有年份 |
| **GNDVI** | 绿光归一化植被指数 | 无单位 | 所有年份 |
| **MSAVI** | 修正土壤调整植被指数 | 无单位 | 所有年份 |
| **BSI** | 裸土指数 | 无单位 | 所有年份 |
| **IBI** | 建成区指数 | 无单位 | 所有年份 |
| **NDBI** | 归一化差异建筑指数 | 无单位 | 所有年份 |
| **NDWI** | 归一化水体指数 | 无单位 | 所有年份 |
| **MNDWI** | 修正归一化水体指数 | 无单位 | 所有年份 |
| **Saturation_Index** | 饱和度指数 | 无单位 | 所有年份 |
| **Hue_Index** | 色调指数 | 无单位 | 所有年份 |
| **Coloration_Index**| 色度指数 | 无单位 | 所有年份 |
| **Redness_Index** | 红色指数 | 无单位 | 所有年份 |