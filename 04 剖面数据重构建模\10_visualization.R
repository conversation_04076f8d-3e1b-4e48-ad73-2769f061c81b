# =============================================================================
# 高质量缺失值填补可视化模块
#
# 功能：
# 1. 期刊级模型性能对比图
# 2. 观测值vs预测值散点图（基于CV结果）
# 3. 高质量采样点空间分布图
# 4. 方法选择统计图
#
# 兼容两种模式：
# - combine_counties: true (合并县城模式)
# - combine_counties: false (分县城模式)
# =============================================================================

# --- 加载必要的包 ---
load_visualization_packages <- function() {
  required_packages <- c("ggplot2", "dplyr", "sf", "viridis", "gridExtra", "scales",
                         "ggpubr", "cowplot", "RColorBrewer", "extrafont")

  for(pkg in required_packages) {
    if(!require(pkg, character.only = TRUE, quietly = TRUE)) {
      install.packages(pkg, dependencies = TRUE)
      library(pkg, character.only = TRUE)
    }
  }

  # 设置期刊级主题
  theme_set(theme_publication())
}

# --- 期刊级主题 ---
theme_publication <- function(base_size = 12, base_family = "") {
  theme_bw(base_size = base_size, base_family = base_family) +
    theme(
      # 整体设置
      plot.title = element_text(size = rel(1.2), face = "bold", hjust = 0.5, margin = margin(b = 15)),
      plot.subtitle = element_text(size = rel(1.0), hjust = 0.5, margin = margin(b = 10)),
      plot.caption = element_text(size = rel(0.8), hjust = 1, margin = margin(t = 10)),

      # 坐标轴
      axis.title = element_text(size = rel(1.1), face = "bold"),
      axis.text = element_text(size = rel(0.9), color = "black"),
      axis.ticks = element_line(color = "black", size = 0.5),
      axis.line = element_line(color = "black", size = 0.5),

      # 面板
      panel.background = element_rect(fill = "white", color = NA),
      panel.border = element_rect(color = "black", fill = NA, size = 0.8),
      panel.grid.major = element_line(color = "grey90", size = 0.3),
      panel.grid.minor = element_blank(),

      # 图例
      legend.background = element_rect(fill = "white", color = "black", size = 0.5),
      legend.title = element_text(size = rel(1.0), face = "bold"),
      legend.text = element_text(size = rel(0.9)),
      legend.key = element_rect(fill = "white", color = NA),
      legend.position = "bottom",

      # 分面
      strip.background = element_rect(fill = "grey95", color = "black", size = 0.5),
      strip.text = element_text(size = rel(0.9), face = "bold", color = "black"),

      # 边距
      plot.margin = margin(t = 20, r = 20, b = 20, l = 20)
    )
}

# --- 创建可视化输出目录 ---
create_visualization_directories <- function(config) {
  base_dir <- config$data_paths$output_directory
  viz_dir <- file.path(base_dir, "可视化结果")
  
  if(!dir.exists(viz_dir)) {
    dir.create(viz_dir, recursive = TRUE)
    cat("创建可视化目录:", viz_dir, "\n")
  }
  
  return(viz_dir)
}

# --- 1. 期刊级模型性能对比图 ---
create_performance_comparison_plot <- function(gap_filling_log, config, output_dir) {

  cat("生成期刊级模型性能对比图...\n")

  # 从Excel文件读取性能数据（更可靠）
  report_path <- file.path(config$data_paths$output_directory, config$gap_filling$report_filename)

  if(!file.exists(report_path)) {
    cat("性能报告文件不存在，跳过性能对比图\n")
    return(NULL)
  }

  # 读取详细填补记录
  performance_data <- readxl::read_excel(report_path, sheet = "详细填补记录")

  # 筛选有效数据
  valid_data <- performance_data[!is.na(performance_data$Best_R2) & !is.na(performance_data$Best_RMSE), ]

  if(nrow(valid_data) == 0) {
    cat("无有效性能数据，跳过性能对比图\n")
    return(NULL)
  }

  # 清理方法名称
  valid_data$Method_Clean <- gsub("_.*", "", valid_data$Best_Method)
  valid_data$Method_Clean[valid_data$Method_Clean == "ordinary_kriging"] <- "OK"
  valid_data$Method_Clean[valid_data$Method_Clean == "regression_kriging"] <- "RK"
  valid_data$Method_Clean[valid_data$Method_Clean == "random_forest"] <- "RF"

  # 设置专业配色
  method_colors <- c("OK" = "#2E86AB", "RK" = "#A23B72", "RF" = "#F18F01")

  # 创建综合性能对比图
  p1 <- ggplot(valid_data, aes(x = Method_Clean, y = Best_R2, fill = Method_Clean)) +
    geom_violin(alpha = 0.6, trim = FALSE) +
    geom_boxplot(width = 0.2, alpha = 0.8, outlier.shape = NA) +
    geom_jitter(width = 0.15, alpha = 0.7, size = 2, color = "white", stroke = 0.5) +
    scale_fill_manual(values = method_colors, name = "方法") +
    scale_y_continuous(limits = c(0, 1), breaks = seq(0, 1, 0.2), labels = scales::percent_format(accuracy = 1)) +
    labs(
      title = "模型性能对比：决定系数 (R²)",
      subtitle = paste("基于", nrow(valid_data), "个填补任务的交叉验证结果"),
      x = "建模方法",
      y = "R² (%)",
      caption = paste("数据来源:", config$gap_filling$report_filename)
    ) +
    theme_publication() +
    theme(legend.position = "none")

  # RMSE对比图
  p2 <- ggplot(valid_data, aes(x = Method_Clean, y = Best_RMSE, fill = Method_Clean)) +
    geom_violin(alpha = 0.6, trim = FALSE) +
    geom_boxplot(width = 0.2, alpha = 0.8, outlier.shape = NA) +
    geom_jitter(width = 0.15, alpha = 0.7, size = 2, color = "white", stroke = 0.5) +
    scale_fill_manual(values = method_colors, name = "方法") +
    labs(
      title = "模型性能对比：均方根误差 (RMSE)",
      subtitle = paste("基于", nrow(valid_data), "个填补任务的交叉验证结果"),
      x = "建模方法",
      y = "RMSE",
      caption = paste("数据来源:", config$gap_filling$report_filename)
    ) +
    theme_publication() +
    theme(legend.position = "none")

  # 创建组合图
  combined_plot <- cowplot::plot_grid(p1, p2, ncol = 2, align = "hv",
                                     labels = c("A", "B"), label_size = 14, label_fontface = "bold")

  # 保存高质量图表
  combined_path <- file.path(output_dir, "模型性能对比_期刊级.png")
  r2_path <- file.path(output_dir, "模型性能_R2_单独.png")
  rmse_path <- file.path(output_dir, "模型性能_RMSE_单独.png")

  # 保存组合图（期刊投稿用）
  ggsave(combined_path, combined_plot, width = 12, height = 6, dpi = 600, bg = "white")

  # 保存单独图表
  ggsave(r2_path, p1, width = 8, height = 6, dpi = 600, bg = "white")
  ggsave(rmse_path, p2, width = 8, height = 6, dpi = 600, bg = "white")

  cat("期刊级性能对比图已保存:\n")
  cat("  组合图:", combined_path, "\n")
  cat("  R²图:", r2_path, "\n")
  cat("  RMSE图:", rmse_path, "\n")

  return(list(combined = combined_plot, r2 = p1, rmse = p2))
}

# --- 2. 真实观测值vs预测值散点图 ---
create_obs_vs_pred_plot <- function(gap_filling_log, config, output_dir) {

  cat("生成真实观测值vs预测值散点图...\n")

  if(length(gap_filling_log) == 0) {
    cat("无填补记录，跳过散点图\n")
    return(NULL)
  }

  # 提取所有CV预测结果
  all_cv_data <- list()

  for(task_name in names(gap_filling_log)) {
    log_entry <- gap_filling_log[[task_name]]

    # 检查是否有CV预测结果
    if(!is.null(log_entry$cv_predictions) && is.data.frame(log_entry$cv_predictions)) {
      cv_data <- log_entry$cv_predictions
      cv_data$soil_property <- log_entry$soil_property
      cv_data$county <- log_entry$county
      cv_data$year <- log_entry$year
      cv_data$layer <- log_entry$layer_name
      cv_data$method <- log_entry$best_method
      cv_data$task_name <- task_name

      all_cv_data[[task_name]] <- cv_data
    }
  }

  if(length(all_cv_data) == 0) {
    cat("无CV预测结果，跳过散点图\n")
    return(NULL)
  }

  # 合并所有CV数据
  combined_cv_data <- do.call(rbind, all_cv_data)

  # 为每个土壤属性创建散点图
  scatter_plots <- list()

  for(soil_prop in unique(combined_cv_data$soil_property)) {

    # 筛选该属性的数据
    prop_data <- combined_cv_data[combined_cv_data$soil_property == soil_prop, ]

    if(nrow(prop_data) == 0) next

    # 计算统计指标
    r2_actual <- cor(prop_data$observed, prop_data$predicted, use = "complete.obs")^2
    rmse_actual <- sqrt(mean((prop_data$observed - prop_data$predicted)^2, na.rm = TRUE))
    mae_actual <- mean(abs(prop_data$observed - prop_data$predicted), na.rm = TRUE)

    # 清理方法名称
    prop_data$method_clean <- gsub("_.*", "", prop_data$method)
    prop_data$method_clean[prop_data$method_clean == "ordinary_kriging"] <- "OK"
    prop_data$method_clean[prop_data$method_clean == "regression_kriging"] <- "RK"
    prop_data$method_clean[prop_data$method_clean == "random_forest"] <- "RF"

    # 设置配色
    method_colors <- c("OK" = "#2E86AB", "RK" = "#A23B72", "RF" = "#F18F01")

    # 计算数据范围
    data_range <- range(c(prop_data$observed, prop_data$predicted), na.rm = TRUE)

    # 创建散点图
    p <- ggplot(prop_data, aes(x = observed, y = predicted)) +
      geom_point(aes(color = method_clean), alpha = 0.7, size = 2.5, stroke = 0.5) +
      geom_smooth(method = "lm", se = TRUE, color = "red", linetype = "dashed", size = 1, alpha = 0.3) +
      geom_abline(intercept = 0, slope = 1, color = "black", linetype = "solid", size = 1) +
      scale_color_manual(values = method_colors, name = "方法") +
      scale_x_continuous(limits = data_range, expand = c(0.05, 0.05)) +
      scale_y_continuous(limits = data_range, expand = c(0.05, 0.05)) +
      labs(
        title = paste(soil_prop, "交叉验证预测精度"),
        subtitle = paste0("R² = ", round(r2_actual, 3), ", RMSE = ", round(rmse_actual, 3), ", MAE = ", round(mae_actual, 3)),
        x = paste("观测值", soil_prop),
        y = paste("预测值", soil_prop),
        caption = paste("样本数:", nrow(prop_data), "| 1:1线(黑色), 拟合线(红色)")
      ) +
      theme_publication() +
      theme(
        legend.position = c(0.15, 0.85),
        legend.background = element_rect(fill = "white", color = "black", size = 0.5)
      ) +
      coord_fixed(ratio = 1)

    scatter_plots[[soil_prop]] <- p

    # 保存单独的散点图
    filename <- paste0("CV预测精度_", soil_prop, ".png")
    filepath <- file.path(output_dir, filename)
    ggsave(filepath, p, width = 8, height = 8, dpi = 600, bg = "white")

    cat("CV散点图已保存:", filename, "\n")
  }

  # 创建组合散点图
  if(length(scatter_plots) > 1) {
    combined_scatter <- cowplot::plot_grid(plotlist = scatter_plots, ncol = 2, align = "hv",
                                          labels = LETTERS[1:length(scatter_plots)],
                                          label_size = 14, label_fontface = "bold")

    combined_path <- file.path(output_dir, "CV预测精度_组合图.png")
    ggsave(combined_path, combined_scatter, width = 12, height = 10, dpi = 600, bg = "white")

    cat("CV组合散点图已保存:", combined_path, "\n")

    return(list(individual = scatter_plots, combined = combined_scatter))
  }

  return(scatter_plots)
}

# --- 2. 方法使用统计图 ---
create_method_usage_plot <- function(gap_filling_log, config, output_dir) {

  cat("生成方法使用统计图...\n")

  if(length(gap_filling_log) == 0) {
    cat("无填补记录，跳过方法使用统计图\n")
    return(NULL)
  }

  # 提取方法使用数据
  method_data <- do.call(rbind, lapply(names(gap_filling_log), function(task_name) {
    log_entry <- gap_filling_log[[task_name]]

    if(!is.null(log_entry$best_method)) {
      method_clean <- gsub("_.*", "", log_entry$best_method)
      method_clean[method_clean == "ordinary_kriging"] <- "OK"
      method_clean[method_clean == "regression_kriging"] <- "RK"
      method_clean[method_clean == "random_forest"] <- "RF"

      data.frame(
        county = log_entry$county,
        year = log_entry$year,
        soil_property = log_entry$soil_property,
        layer = log_entry$layer_name,
        method = method_clean,
        filled_count = if(!is.null(log_entry$filled_count)) log_entry$filled_count else 0,
        stringsAsFactors = FALSE
      )
    } else {
      NULL
    }
  }))

  if(is.null(method_data) || nrow(method_data) == 0) {
    cat("无有效方法数据，跳过方法使用统计图\n")
    return(NULL)
  }

  # 方法使用频率统计
  method_counts <- table(method_data$method)
  method_df <- data.frame(
    method = names(method_counts),
    count = as.numeric(method_counts),
    percentage = round(as.numeric(method_counts) / sum(method_counts) * 100, 1)
  )

  # 创建饼图
  p1 <- ggplot(method_df, aes(x = "", y = count, fill = method)) +
    geom_bar(stat = "identity", width = 1) +
    coord_polar("y", start = 0) +
    scale_fill_viridis_d(name = "方法") +
    labs(
      title = "最优方法使用频率分布",
      subtitle = paste("总任务数:", sum(method_df$count)),
      caption = paste("数据来源:", length(gap_filling_log), "个填补任务")
    ) +
    theme_void() +
    theme(
      plot.title = element_text(size = 14, face = "bold", hjust = 0.5),
      plot.subtitle = element_text(size = 12, hjust = 0.5),
      legend.position = "bottom"
    ) +
    geom_text(aes(label = paste0(percentage, "%")),
              position = position_stack(vjust = 0.5), size = 4, fontface = "bold")

  # 创建柱状图
  p2 <- ggplot(method_df, aes(x = reorder(method, count), y = count, fill = method)) +
    geom_col(alpha = 0.8) +
    geom_text(aes(label = paste0(count, " (", percentage, "%)")),
              hjust = -0.1, size = 4) +
    scale_fill_viridis_d(name = "方法") +
    coord_flip() +
    labs(
      title = "最优方法选择统计",
      subtitle = "各方法被选为最佳的次数",
      x = "建模方法",
      y = "使用次数",
      caption = paste("总任务数:", sum(method_df$count))
    ) +
    theme_minimal() +
    theme(
      plot.title = element_text(size = 14, face = "bold", hjust = 0.5),
      plot.subtitle = element_text(size = 12, hjust = 0.5),
      legend.position = "none"
    )

  # 保存图表
  pie_path <- file.path(output_dir, "方法使用频率_饼图.png")
  bar_path <- file.path(output_dir, "方法使用频率_柱状图.png")

  ggsave(pie_path, p1, width = 8, height = 8, dpi = 300)
  ggsave(bar_path, p2, width = 10, height = 6, dpi = 300)

  cat("方法使用统计图已保存:\n")
  cat("  饼图:", pie_path, "\n")
  cat("  柱状图:", bar_path, "\n")

  return(list(pie_plot = p1, bar_plot = p2))
}

# --- 3. 采样点空间分布图 ---
create_spatial_distribution_plot <- function(filled_data, original_data, config, output_dir) {
  
  cat("生成采样点空间分布图...\n")
  
  # 检查必要的坐标列
  coord_cols <- c("Longitude", "Latitude")
  if(!all(coord_cols %in% names(original_data))) {
    cat("缺少坐标信息，跳过空间分布图\n")
    return(NULL)
  }
  
  # 为每个土壤属性和年份创建分布图
  for(year in config$years) {
    for(soil_property in config$target_soil_properties) {
      
      # 筛选数据
      year_data <- original_data[original_data$year == year, ]
      if(nrow(year_data) == 0) next
      
      # 检查该属性是否存在
      if(!soil_property %in% names(year_data)) next
      
      # 创建缺失值标记
      year_data$has_missing <- is.na(year_data[[soil_property]])
      year_data$fill_status <- ifelse(year_data$has_missing, "缺失值", "观测值")
      
      # 根据模式调整标题和颜色
      if(config$combine_counties) {
        plot_title <- paste(soil_property, "-", year, "年 (合并县城模式)")
        color_var <- "fill_status"
      } else {
        plot_title <- paste(soil_property, "-", year, "年 (分县城模式)")
        color_var <- "City"
      }
      
      # 创建空间分布图
      p <- ggplot(year_data, aes(x = Longitude, y = Latitude)) +
        geom_point(aes_string(color = color_var, shape = "fill_status"), 
                   size = 2.5, alpha = 0.8) +
        scale_shape_manual(values = c("观测值" = 16, "缺失值" = 4), name = "数据状态") +
        labs(
          title = plot_title,
          subtitle = paste("采样点空间分布 (总计", nrow(year_data), "个点)"),
          x = "经度 (°E)",
          y = "纬度 (°N)",
          caption = paste("缺失值:", sum(year_data$has_missing), "个")
        ) +
        theme_minimal() +
        theme(
          plot.title = element_text(size = 14, face = "bold", hjust = 0.5),
          plot.subtitle = element_text(size = 12, hjust = 0.5),
          legend.position = "bottom",
          axis.text = element_text(size = 10),
          panel.grid.minor = element_blank()
        ) +
        coord_fixed(ratio = 1.3)  # 调整坐标比例
      
      # 根据模式调整颜色方案
      if(config$combine_counties) {
        p <- p + scale_color_manual(values = c("观测值" = "#2E86AB", "缺失值" = "#F18F01"), 
                                   name = "数据状态")
      } else {
        p <- p + scale_color_viridis_d(name = "县城")
      }
      
      # 保存图表
      filename <- paste0("空间分布_", soil_property, "_", year, "年.png")
      filepath <- file.path(output_dir, filename)
      
      ggsave(filepath, p, width = 10, height = 8, dpi = 300)
      cat("空间分布图已保存:", filepath, "\n")
    }
  }
  
  return(TRUE)
}

# --- 主可视化函数 ---
generate_gap_filling_visualizations <- function(filled_data, gap_filling_log, original_data, config) {
  
  cat("\n=== 生成缺失值填补可视化图表 ===\n")
  
  # 加载可视化包
  load_visualization_packages()
  
  # 创建输出目录
  output_dir <- create_visualization_directories(config)
  
  # 生成各类图表
  tryCatch({
    # 1. 期刊级模型性能对比图
    performance_plots <- create_performance_comparison_plot(gap_filling_log, config, output_dir)

    # 2. 真实观测值vs预测值散点图（基于CV结果）
    scatter_plots <- create_obs_vs_pred_plot(gap_filling_log, config, output_dir)

    # 3. 方法使用统计图
    method_plots <- create_method_usage_plot(gap_filling_log, config, output_dir)

    # 4. 采样点空间分布图
    spatial_plots <- create_spatial_distribution_plot(filled_data, original_data, config, output_dir)

    cat("✅ 高质量可视化图表生成完成！\n")
    cat("📁 图表保存位置:", output_dir, "\n")

    # 生成图表汇总信息
    create_visualization_summary(output_dir, config)

  }, error = function(e) {
    cat("❌ 可视化生成过程中出现错误:", e$message, "\n")
    cat("详细错误信息:", toString(e), "\n")
  })
  
  return(output_dir)
}

# --- 生成图表汇总信息 ---
create_visualization_summary <- function(output_dir, config) {

  # 检查生成的图表文件
  png_files <- list.files(output_dir, pattern = "\\.png$", full.names = FALSE)

  if(length(png_files) == 0) {
    cat("未找到生成的图表文件\n")
    return(NULL)
  }

  # 创建汇总信息
  summary_text <- paste0(
    "# 缺失值填补可视化结果汇总\n\n",
    "## 生成时间\n",
    format(Sys.time(), "%Y-%m-%d %H:%M:%S"), "\n\n",
    "## 配置信息\n",
    "- 分析模式: ", ifelse(config$combine_counties, "合并县城模式", "分县城模式"), "\n",
    "- 处理县城: ", paste(config$counties, collapse = ", "), "\n",
    "- 处理年份: ", paste(config$years, collapse = ", "), "\n",
    "- 土壤属性: ", paste(config$target_soil_properties, collapse = ", "), "\n\n",
    "## 生成的图表文件\n",
    paste(paste0("- ", png_files), collapse = "\n"), "\n\n",
    "## 图表说明\n",
    "1. **期刊级模型性能对比图**: 高质量的三种方法(OK/RK/RF)性能对比，包含小提琴图+箱线图\n",
    "2. **真实CV预测精度散点图**: 基于交叉验证的真实观测值vs预测值图表，包含1:1线和拟合线\n",
    "3. **方法使用统计图**: 显示各方法被选为最优的频率分布（饼图+柱状图）\n",
    "4. **空间分布图**: 展示采样点的空间分布和缺失值分布情况\n\n",
    "## 图表特点\n",
    "- **期刊投稿级质量**: 600 DPI高分辨率，专业配色方案\n",
    "- **统计严谨性**: 基于交叉验证结果的真实性能评估\n",
    "- **视觉美观性**: 现代化设计，符合国际期刊标准\n",
    "- **信息完整性**: 包含统计指标、样本数量、数据来源等关键信息\n\n",
    "## 使用建议\n",
    "- **性能对比图**: 用于论文方法部分，展示方法选择的科学性\n",
    "- **散点图**: 用于结果验证部分，证明模型预测精度\n",
    "- **方法统计图**: 用于讨论部分，说明不同方法的适用性\n",
    "- **空间分布图**: 用于数据描述部分，展示研究区域和数据覆盖\n"
  )

  # 保存汇总信息
  summary_path <- file.path(output_dir, "图表汇总说明.txt")
  writeLines(summary_text, summary_path, useBytes = TRUE)

  cat("图表汇总信息已保存:", summary_path, "\n")

  return(summary_path)
}
