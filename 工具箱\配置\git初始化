命令行初始化
git config --global user.name "Aeth247"
git config --global user.email <EMAIL>
设置代理-用于发布
git config --global http.proxy http://127.0.0.1:8888
git config --global https.proxy https://127.0.0.1:8888
git命令
git log 查看日志
git status 查看状态
git add . 添加所有文件
git commit -m "提交信息" 提交
git push 发布
git pull 拉取
git checkout 切换分支
git branch 查看分支
git branch -d 删除分支
git branch -m 重命名分支
git log --oneline --graph --all

# 1. 把历史全部重置到最初的空点（保留工作区文件）
git reset --soft $(git rev-list --max-parents=0 HEAD)
git add -A
git commit -m "最新7.16"

git 回滚
1.已提交,没有push
1)git reset--soft 撤销commit
2)git reset--mixed 撤销commit和add两个动作
2.已提交，并且push
1)git reset--hard 撤销并舍弃版本号之后的提交记录。使用需要谨慎。
2)git revert 撤销。但是保留了提交记录。

# 撤销本地最新 commit（保留改动）
git reset --soft HEAD~1
# 撤销本地最新 commit（不保留改动）
git reset --hard HEAD~1

# 撤销已推送的某条 commit（生成反向 commit）
git revert <hash>

# 彻底抹掉已推送的历史
git reset --hard <new-tip>
git push --force-with-lease
git rebase --continue
git rebase -i HEAD~5

git add .
git commit -m "提交信息"
git log
git status
git checkout 分支名
git checkout -b 分支名 创建并立即切换到新分支
git branch -d 分支名
git branch -m 分支名 就地改名
git branch -m 分支名 新分支名 改名
git add . ; git commit -m "最新"
git commit -am "最新"  把“已跟踪”的修改直接 add + commit
git rm -r --cached "00 原始数据"