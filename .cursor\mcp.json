{"mcpServers": {"mcp-feedback-enhanced": {"command": "uvx", "args": ["mcp-feedback-enhanced@latest"], "env": {"MCP_LANGUAGE": "zh-CN", "MCP_DESKTOP_MODE": "false"}, "disabled": false}, "寸止": {"command": "D:\\Desktop\\寸止\\寸止.exe", "args": [], "env": {}, "disabled": false}, "shrimp-task-manager": {"command": "npx", "args": ["-y", "mcp-shrimp-task-manager"], "env": {"DATA_DIR": "E:\\05 Python\\Devway\\01 硕士论文\\data", "TEMPLATES_USE": "zh", "ENABLE_GUI": "false"}, "disabled": false, "autoApprove": []}, "playwright": {"command": "npx", "args": ["-y", "@playwright/mcp@latest"], "env": {"PLAYWRIGHT_HEADLESS": "true"}, "disabled": false, "autoApprove": ["browser_wait_for", "browser_click", "browser_navigate", "browser_evaluate", "browser_snapshot", "browser_type", "browser_close", "browser_tab_select", "browser_take_screenshot"]}, "server-filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "E:\\03 文件夹\\微信聊天助手\\", "E:\\05 Python\\Devway\\01 硕士论文\\"], "env": {}, "disabled": false}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"], "env": {}, "disabled": false}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking@latest"], "env": {}, "disabled": false, "autoApprove": ["sequentialthinking"]}, "vibedev-specs": {"command": "npx", "args": ["vibedev-specs-mcp@latest"], "env": {}, "disabled": false}}}