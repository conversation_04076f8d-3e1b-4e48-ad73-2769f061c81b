# ---- Excel导出模块 ----
"""
将方差分析结果导出到Excel文件中
优化版本：更清晰的报告结构和更好的可读性
"""

import os
import pandas as pd
from datetime import datetime
from B_config import EXCEL_DIR, COUNTIES, YEARS, DEPTHS, P_ADJUST, ALPHA, ANALYSIS_TYPES, INDICATORS

def export_results(results, output_path=None, plot_types=None, available_indicators_dict=None, generate_pvalue_sheet=True, target_counties=None):
    """
    将方差分析结果导出到Excel
    
    Args:
        results (dict): 方差分析结果
        output_path (str): 输出路径
        plot_types (dict): 图表类型开关 {类型名: 布尔值}
        available_indicators_dict (dict): 实际可用的指标 {友好名: 列名}
        generate_pvalue_sheet (bool): 是否生成p值对比表格
        target_counties (list): 目标县城列表
    """
    if output_path is None:
        output_path = EXCEL_DIR
    
    # 确保输出目录存在
    os.makedirs(output_path, exist_ok=True)
    
    # 输出文件路径
    excel_path = os.path.join(output_path, '方差分析结果.xlsx')
    
    # 如果没有提供target_counties，则使用配置中的所有县城
    if target_counties is None:
        target_counties = COUNTIES
    
    # 如果没有提供可用的指标字典，使用配置中的
    if available_indicators_dict is None:
        from B_config import INDICATORS as available_indicators_dict
    
    # 预处理结果数据，按分析类型分组
    parsed_results = _parse_results_by_type(results, available_indicators_dict)
    
    # 创建Excel写入器
    with pd.ExcelWriter(excel_path, engine='xlsxwriter') as writer:
        workbook = writer.book
        
        # 定义统一格式
        formats = _create_formats(workbook)
        
        # 1. 创建分析概要表
        create_summary_sheet(writer, parsed_results, available_indicators_dict, target_counties, formats)
        
        # 2. 为每种分析类型创建独立的结果表
        create_individual_analysis_sheets(writer, parsed_results, formats)

        # 3. 创建P-Value矩阵表
        if generate_pvalue_sheet:
            create_pvalue_matrices_sheet(writer, parsed_results, formats)
        
        # 4. 创建统计方法说明
        create_methodology_sheet(writer, formats)
    
    print(f"方差分析结果已导出到: {excel_path}")

def _parse_results_by_type(results, available_indicators_dict):
    """
    将结果按分析类型分组并解析
    
    Args:
        results (dict): 原始分析结果 - 现在是扁平结构 {result_key: result_data}
        available_indicators_dict (dict): 可用指标字典
    
    Returns:
        dict: 按分析类型分组的结果
    """
    parsed = {}
    
    # 处理扁平结构：{result_key: result_data}
    for analysis_type, config in ANALYSIS_TYPES.items():
        parsed[analysis_type] = {
            'name': config['name'],
            'results': {},
            'significant_count': 0,
            'total_count': 0
        }
        
        # 筛选属于该分析类型的结果
        for key, result in results.items():
            if _matches_analysis_type(key, analysis_type):
                parsed[analysis_type]['results'][key] = result
                parsed[analysis_type]['total_count'] += 1
                if result.get('significant_corrected', False):
                    parsed[analysis_type]['significant_count'] += 1
    
    return parsed

def _create_formats(workbook):
    """
    创建Excel格式定义
    
    Args:
        workbook: xlsxwriter workbook对象
    
    Returns:
        dict: 格式字典
    """
    return {
        'header': workbook.add_format({
            'bold': True,
            'font_size': 12,
            'bg_color': '#E6E6FA',
            'border': 1,
            'align': 'center',
            'valign': 'vcenter'
        }),
        'data': workbook.add_format({
            'font_size': 10,
            'border': 1,
            'align': 'left',
            'valign': 'vcenter'
        }),
        'significant': workbook.add_format({
            'font_size': 10,
            'bg_color': '#FFE4E1',
            'border': 1,
            'align': 'left',
            'valign': 'vcenter'
        }),
        'number': workbook.add_format({
            'font_size': 10,
            'border': 1,
            'align': 'center',
            'valign': 'vcenter',
            'num_format': '0.000000'
        })
    }

def create_summary_sheet(writer, parsed_results, available_indicators_dict, target_counties, formats):
    """创建分析概要表"""
    summary_data = []
    
    # 分析基本信息
    summary_data.append(['分析日期', datetime.now().strftime('%Y-%m-%d %H:%M:%S')])
    summary_data.append(['分析县城', ', '.join(target_counties)])
    summary_data.append(['分析年份', ', '.join(map(str, YEARS))])
    summary_data.append(['分析深度', ', '.join(DEPTHS)])
    summary_data.append(['分析指标', ', '.join(available_indicators_dict.keys())])
    summary_data.append(['统计显著性水平', f'α = {ALPHA}'])
    summary_data.append(['多重比较校正方法', P_ADJUST])
    summary_data.append([''])  # 空行
    
    # 分析类型统计
    summary_data.append(['分析类型统计', ''])
    for analysis_type, type_data in parsed_results.items():
        summary_data.append([
            type_data['name'],
            f'总计: {type_data["total_count"]}, 显著: {type_data["significant_count"]}, 非显著: {type_data["total_count"] - type_data["significant_count"]}'
        ])
    
    summary_df = pd.DataFrame(summary_data, columns=['项目', '值'])
    summary_df.to_excel(writer, sheet_name='分析概要', index=False)
    
    # 设置格式
    worksheet = writer.sheets['分析概要']
    worksheet.set_column('A:A', 20, formats['data'])
    worksheet.set_column('B:B', 40, formats['data'])
    
    # 设置标题行格式
    for i in range(len(summary_data)):
        if i == 0 or summary_data[i][0] in ['分析类型统计']:
            worksheet.set_row(i + 1, None, formats['header'])

def create_individual_analysis_sheets(writer, parsed_results, formats):
    """为每种分析类型创建独立的结果表"""
    for analysis_type, type_data in parsed_results.items():
        if not type_data['results']:  # 跳过没有结果的分析类型
            continue
            
        # 构建该分析类型的数据
        analysis_data = []
        for key, result in type_data['results'].items():
            row = _build_unified_result_row(key, result, analysis_type, type_data['name'])
            analysis_data.append(row)
        
        if analysis_data:
            columns = [
                '分析类型', '指标', '分组信息', '检验方法', '统计量值',
                '原始p值', '校正后p值', '显著性', '效应量', '组别数', '显著性标记'
            ]
            
            # 使用分析类型名称作为sheet名
            sheet_name = type_data['name']
            
            # 创建DataFrame并导出
            df = pd.DataFrame(analysis_data, columns=columns)
            df.to_excel(writer, sheet_name=sheet_name, index=False)
            
            # 设置格式
            worksheet = writer.sheets[sheet_name]
            _format_worksheet(worksheet, columns, formats, analysis_data)

def create_unified_results_sheet(writer, parsed_results, formats):
    """创建统一结果表（所有分析类型在一个表中）"""
    all_data = []
    
    for analysis_type, type_data in parsed_results.items():
        for key, result in type_data['results'].items():
            row = _build_unified_result_row(key, result, analysis_type, type_data['name'])
            all_data.append(row)
    
    if all_data:
        columns = [
            '分析类型', '指标', '分组信息', '检验方法', '统计量',
            '原始p值', '校正后p值', '显著性', '效应量', '组别数', '显著性标记'
        ]
        unified_df = pd.DataFrame(all_data, columns=columns)
        unified_df.to_excel(writer, sheet_name='统一结果表', index=False)
        
        # 设置格式
        worksheet = writer.sheets['统一结果表']
        _format_worksheet(worksheet, columns, formats, all_data)

def create_significant_summary_sheet(writer, parsed_results, formats):
    """创建显著性汇总表（仅显示显著结果）"""
    significant_data = []
    
    for analysis_type, type_data in parsed_results.items():
        for key, result in type_data['results'].items():
            if result.get('significant_corrected', False):
                row = _build_unified_result_row(key, result, analysis_type, type_data['name'])
                significant_data.append(row)
    
    if significant_data:
        columns = [
            '分析类型', '指标', '分组信息', '检验方法', '统计量',
            '原始p值', '校正后p值', '显著性', '效应量', '组别数', '显著性标记'
        ]
        significant_df = pd.DataFrame(significant_data, columns=columns)
        significant_df.to_excel(writer, sheet_name='显著性汇总', index=False)
        
        # 设置格式
        worksheet = writer.sheets['显著性汇总']
        _format_worksheet(worksheet, columns, formats, significant_data, highlight_all=True)
    else:
        # 创建空表说明无显著结果
        empty_df = pd.DataFrame([['本次分析中未发现显著性差异']], columns=['说明'])
        empty_df.to_excel(writer, sheet_name='显著性汇总', index=False)

def create_pvalue_comparison_sheet(writer, parsed_results, formats):
    """创建p值对比表"""
    pvalue_data = []
    
    for analysis_type, type_data in parsed_results.items():
        for key, result in type_data['results'].items():
            row = [
                type_data['name'],
                _extract_indicator_from_key(key),
                _extract_group_info(key, analysis_type),
                f"{result.get('p_value', 'N/A'):.6f}" if result.get('p_value') is not None else 'N/A',
                f"{result.get('p_value_corrected', 'N/A'):.6f}" if result.get('p_value_corrected') is not None else 'N/A',
                '是' if result.get('significant_corrected', False) else '否',
                ', '.join([f"{k}:{v}" for k, v in result.get('labels', {}).items()])
            ]
            pvalue_data.append(row)
    
    if pvalue_data:
        columns = ['分析类型', '指标', '分组信息', '原始p值', '校正后p值', '是否显著', '显著性标记']
        pvalue_df = pd.DataFrame(pvalue_data, columns=columns)
        pvalue_df.to_excel(writer, sheet_name='p值对比表', index=False)
        
        # 设置格式
        worksheet = writer.sheets['p值对比表']
        _format_worksheet(worksheet, columns, formats, pvalue_data)

def create_pvalue_matrices_sheet(writer, parsed_results, formats):
    """为所有多组比较创建一个包含p-value矩阵的新工作表。"""
    worksheet = writer.book.add_worksheet('P-Value矩阵 (Post-Hoc)')
    worksheet.set_column('A:A', 45)  # 加宽第一列以容纳标题
    row_cursor = 0

    for analysis_type, content in parsed_results.items():
        results = content.get('results', {})
        if not results:
            continue

        # 筛选出需要p-value矩阵的结果
        matrices_to_write = []
        for key, result_data in results.items():
            if result_data.get('p_value_matrix') is not None and not result_data['p_value_matrix'].empty:
                matrices_to_write.append((key, result_data))
        
        if not matrices_to_write:
            continue

        # 为每个分析类型写入一个大标题
        worksheet.merge_range(row_cursor, 0, row_cursor, 8, content['name'], formats.get('title', None))
        row_cursor += 2

        for key, result_data in matrices_to_write:
            p_matrix = result_data['p_value_matrix']
            # 写入矩阵的详细标题
            title = f"P-Value Matrix: {_extract_group_info(key, analysis_type)} - {_extract_indicator_from_key(key)}"
            worksheet.write(row_cursor, 0, title, formats.get('header', None))
            row_cursor += 1

            # 写入矩阵的列标题
            headers = ['Group'] + list(p_matrix.columns)
            for col, header in enumerate(headers):
                worksheet.write(row_cursor, col, header, formats.get('p_value_header', None))
            row_cursor += 1

            # 写入矩阵数据
            for r_idx, (idx, p_series) in enumerate(p_matrix.iterrows()):
                worksheet.write(row_cursor + r_idx, 0, idx, formats.get('p_value_header', None))
                for c_idx, val in enumerate(p_series):
                    cell_format = formats.get('significant', None) if pd.notna(val) and val < ALPHA else formats.get('number', None)
                    worksheet.write(row_cursor + r_idx, c_idx + 1, val if pd.notna(val) else '-', cell_format)
            
            row_cursor += len(p_matrix) + 2  # 增加间距

def create_methodology_sheet(writer, formats):
    """创建统计方法说明表"""
    methodology_data = [
        ['统计方法说明', ''],
        ['', ''],
        ['分析流程', ''],
        ['1. 数据预处理', '处理缺失值，按深度和指标分组'],
        ['2. 假设检验', '使用Kruskal-Wallis H检验（非参数方法）'],
        ['3. 多重比较校正', f'使用{P_ADJUST}方法控制假发现率'],
        ['4. 事后检验', '对显著结果执行Dunn检验'],
        ['5. 显著性标记', '生成紧凑字母显示(CLD)'],
        ['', ''],
        ['解释说明', ''],
        ['显著性水平', f'α = {ALPHA}'],
        ['显著性标记', '相同字母表示组间无显著差异，不同字母表示有显著差异'],
        ['p值含义', 'p < 0.05表示统计学显著'],
        ['校正后p值', '控制多重比较导致的I类错误'],
        ['', ''],
        ['数据质量', ''],
        ['缺失值处理', '每个指标独立处理，保留完整观测值'],
        ['样本量', '每个分组的有效样本量在结果中体现'],
        ['异常值', '使用箱线图标识，但不剔除']
    ]
    
    methodology_df = pd.DataFrame(methodology_data, columns=['项目', '说明'])
    methodology_df.to_excel(writer, sheet_name='统计方法说明', index=False)
    
    # 设置格式
    worksheet = writer.sheets['统计方法说明']
    worksheet.set_column('A:A', 20, formats['data'])
    worksheet.set_column('B:B', 50, formats['data'])

def _matches_analysis_type(key, analysis_type):
    """判断结果键是否匹配指定的分析类型"""
    if analysis_type == 'spatial':
        return key.startswith('spatial_')
    elif analysis_type == 'temporal':
        return key.startswith('temporal_')
    elif analysis_type == 'depth_time':
        return key.startswith('depth_time_')
    elif analysis_type == 'depth_profile':
        return key.startswith('depth_profile_')
    return False

def _build_unified_result_row(key, result, analysis_type, analysis_name):
    """构建统一结果行数据"""
    # 根据检验方法格式化统计量
    test_method = result.get('test_method', 'Kruskal-Wallis H')
    statistic = result.get('statistic')
    
    if statistic is not None:
        if 'Mann-Whitney' in test_method:
            stat_str = f"U = {statistic:.4f}"
        elif 'Kruskal-Wallis' in test_method:
            stat_str = f"H = {statistic:.4f}"
        else:
            stat_str = f"{statistic:.4f}"
    else:
        stat_str = 'N/A'
    
    return [
        analysis_name,
        _extract_indicator_from_key(key),
        _extract_group_info(key, analysis_type),
        test_method,
        stat_str,
        _format_p_value(result.get('p_value')),
        _format_p_value(result.get('p_value_corrected')),
        '是' if result.get('significant_corrected', False) else '否',
        f"{result.get('effect_sizes', {}).get('eta_squared', 'N/A'):.4f}" if result.get('effect_sizes', {}).get('eta_squared') is not None else 'N/A',
        len(result.get('groups', [])),
        ', '.join([f"{k}:{v}" for k, v in result.get('labels', {}).items()])
    ]

def _extract_indicator_from_key(key):
    """从结果键中提取指标名"""
    # 尝试从配置的指标中匹配
    for indicator_name in INDICATORS.keys():
        if indicator_name in key:
            return indicator_name
    # 如果没匹配到，返回键本身的一部分
    parts = key.split('_')
    return parts[-1] if parts else '未知指标'

def _format_worksheet(worksheet, columns, formats, data, highlight_all=False):
    """统一的工作表格式设置"""
    # 设置列宽和格式
    for col_num, col_name in enumerate(columns):
        if col_name in ['显著性标记', '分组信息']:
            worksheet.set_column(col_num, col_num, 25, formats['data'])
        elif col_name in ['原始p值', '校正后p值', '统计量', '效应量']:
            worksheet.set_column(col_num, col_num, 12, formats['number'])
        else:
            worksheet.set_column(col_num, col_num, 15, formats['data'])
    
    # 设置标题行格式
    for col_num in range(len(columns)):
        worksheet.write(0, col_num, columns[col_num], formats['header'])
    
    # 高亮显著结果或全部高亮
    if highlight_all:
        for row_num, row_data in enumerate(data, start=1):
            for col_num in range(len(columns)):
                worksheet.write(row_num, col_num, row_data[col_num], formats['significant'])
    else:
        for row_num, row_data in enumerate(data, start=1):
            is_significant = '是' in str(row_data) and '是否显著' in columns
            if is_significant and len(row_data) > 7:
                sig_index = next((i for i, col in enumerate(columns) if '是否显著' in col), -1)
                if sig_index >= 0 and row_data[sig_index] == '是':
                    for col_num in range(len(columns)):
                        worksheet.write(row_num, col_num, row_data[col_num], formats['significant'])

def _format_p_value(p_value):
    """格式化p值显示"""
    if p_value is None:
        return 'N/A'
    
    if p_value < 1e-6:
        return f"{p_value:.2e}"  # 科学计数法
    elif p_value < 0.001:
        return f"{p_value:.6f}"
    else:
        return f"{p_value:.4f}"

def _extract_group_info(key, analysis_type):
    """从结果键中提取分组信息"""
    if analysis_type == 'spatial':
        for year in YEARS:
            if str(year) in key:
                return f"{year}年"
    elif analysis_type == 'temporal':
        for county in COUNTIES:
            if county in key:
                return f"{county}县"
    elif analysis_type == 'depth_time':
        for depth in DEPTHS:
            if depth in key:
                for county in COUNTIES:
                    if county in key:
                        return f"{county}县-{depth}cm"
    elif analysis_type == 'depth_profile':
        for year in YEARS:
            if str(year) in key:
                for county in COUNTIES:
                    if county in key:
                        return f"{year}年-{county}县"
    return '未知'