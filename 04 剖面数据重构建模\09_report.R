# =============================================================================
# 报告生成模块
# 
# 功能：
# 1. 缺失值填补报告生成
# 2. 分层保底策略
# 3. Excel报告格式化
# 4. 统计汇总
# =============================================================================

# --- 统一的保底策略数据操作函数 ---
create_fallback_context <- function(data, county, year, soil_property, layer_name, config) {
  # 获取深度层配置
  depth_layers <- config$general_options$depth_layers
  target_depth_config <- NULL
  for(depth_config in depth_layers) {
    if(depth_config$name == layer_name) {
      target_depth_config <- depth_config
      break
    }
  }

  if(is.null(target_depth_config)) {
    stop(paste("未找到深度层配置:", layer_name))
  }

  # 创建上下文对象
  ctx <- list(
    # 配置信息
    cols = list(
      county = config$column_names$county,
      year = config$column_names$year,
      depth = config$column_names$depth,
      profile = config$column_names$profile_id
    ),
    target_depth = target_depth_config$center,
    target_county = county,
    target_year = year,
    soil_property = soil_property,

    # 筛选目标数据的函数
    filter_target = function(data) {
      data[data[[ctx$cols$county]] == ctx$target_county &
           data[[ctx$cols$year]] == ctx$target_year &
           data[[ctx$cols$depth]] == ctx$target_depth, ]
    },

    # 计算缺失值数量的函数
    count_missing = function(data) {
      target_data <- ctx$filter_target(data)
      sum(is.na(target_data[[ctx$soil_property]]))
    }
  )

  return(ctx)
}

# --- 生成填补报告 ---
generate_gap_filling_report <- function(gap_filling_log, config) {

  cat("生成缺失值填补报告...\n")

  if(length(gap_filling_log) == 0) {
    cat("无填补记录\n")
    return()
  }

  # 转换为数据框，显示最佳方法的完整评估指标和参数
  log_df <- do.call(rbind, lapply(gap_filling_log, function(x) {
    # 根据方法类型选择显示的参数
    params_str <- ""
    if(!is.null(x$best_method)) {
      if(x$best_method == "random_forest" && !is.null(x$rf_ntree)) {
        params_str <- paste0("ntree=", x$rf_ntree,
                            ", nodesize=", x$rf_nodesize,
                            ", mtry=", x$rf_mtry)
      } else if(x$best_method %in% c("ordinary_kriging", "regression_kriging") && !is.null(x$kr_nmax)) {
        params_str <- paste0("nmax=", x$kr_nmax,
                            ", nmin=", x$kr_nmin,
                            ", vgm=", x$kr_vgm_model)
      }
    }

    # 格式化方法名称，添加填充数量
    method_with_count <- ifelse(
      !is.null(x$best_method) && !is.null(x$filled_count),
      paste0(x$best_method, "_", x$filled_count),
      ifelse(is.null(x$best_method), NA, x$best_method)
    )

    # 获取样本数（优先使用直接传入的sample_size）
    sample_size <- if(is.null(x$sample_size) || is.na(x$sample_size)) NA else x$sample_size
    
    # 如果没有直接的sample_size，尝试从all_results中获取
    if(is.na(sample_size) && !is.null(x$all_results) && !is.null(x$best_method) && x$best_method %in% names(x$all_results)) {
      method_result <- x$all_results[[x$best_method]]
      if(!is.null(method_result) && !is.null(method_result$sample_size)) {
        sample_size <- method_result$sample_size
      }
    }

    # 获取选择的环境变量信息
    selected_vars <- if(!is.null(x$selected_variables)) {
      paste(x$selected_variables, collapse = "; ")
    } else {
      "无环境变量"
    }
    
    var_count <- if(!is.null(x$selected_variables)) {
      length(x$selected_variables)
    } else {
      0
    }

    # 确保所有值都有默认值，避免NULL导致的行数不匹配
    data.frame(
      County = ifelse(is.null(x$county), NA, x$county),
      Year = ifelse(is.null(x$year), NA, x$year),
      Soil_Property = ifelse(is.null(x$soil_property), NA, x$soil_property),
      Depth_Layer = ifelse(is.null(x$layer_name), NA, x$layer_name),
      Missing_Count = ifelse(!is.null(x$total_count) && !is.null(sample_size), 
                            x$total_count - sample_size,  # 使用新公式：总样本数 - 建模样本数
                            ifelse(is.null(x$missing_count), NA, x$missing_count)),  # 保留旧逻辑作为后备
      Sample_Size = sample_size,
      Var_Count = var_count,
      Selected_Variables = selected_vars,
      Best_Method = method_with_count,
      Best_Params = params_str,
      Best_R2 = round(ifelse(is.null(x$best_r2) || !is.numeric(x$best_r2), NA, x$best_r2), 3),
      Best_RMSE = round(ifelse(is.null(x$best_rmse) || !is.numeric(x$best_rmse), NA, x$best_rmse), 3),
      Best_MAE = round(ifelse(is.null(x$best_mae) || !is.numeric(x$best_mae), NA, x$best_mae), 3),
      Best_CCC = round(ifelse(is.null(x$best_ccc) || !is.numeric(x$best_ccc), NA, x$best_ccc), 3),
      Best_RPD = round(ifelse(is.null(x$best_rpd) || !is.numeric(x$best_rpd), NA, x$best_rpd), 3),
      Filled_Count = ifelse(is.null(x$filled_count), NA, x$filled_count),
      Timestamp = as.character(ifelse(is.null(x$timestamp), Sys.time(), x$timestamp)),
      stringsAsFactors = FALSE
    )
  }))

  # 保存报告为Excel格式
  output_dir <- config$data_paths$output_directory
  report_path <- file.path(output_dir, config$gap_filling$report_filename)

  # 创建工作簿
  wb <- createWorkbook()

  # 添加统一的表头样式
  headerStyle <- createStyle(
    fontSize = 12,
    fontColour = "white",
    halign = "center",
    fgFill = "#4F81BD",
    border = "TopBottom",
    borderColour = "black",
    wrapText = TRUE
  )

  # 工作表1：详细填补记录
  addWorksheet(wb, "详细填补记录")
  writeData(wb, "详细填补记录", log_df)
  addStyle(wb, "详细填补记录", headerStyle, rows = 1, cols = 1:ncol(log_df), gridExpand = TRUE)
  setColWidths(wb, "详细填补记录", cols = 1:ncol(log_df), widths = "auto")

  # 工作表2：汇总统计
  summary_data <- data.frame(
    统计项目 = c("总填补任务数", "总填补缺失值数",
                "最大R²", "最小R²", "平均R²",
                "平均RMSE", "平均MAE", "平均CCC", "平均RPD"),
    数值 = c(
      nrow(log_df),
      sum(log_df$Filled_Count),
      round(max(log_df$Best_R2, na.rm = TRUE), 3),
      round(min(log_df$Best_R2, na.rm = TRUE), 3),
      round(mean(log_df$Best_R2, na.rm = TRUE), 3),
      round(mean(log_df$Best_RMSE, na.rm = TRUE), 3),
      round(mean(log_df$Best_MAE, na.rm = TRUE), 3),
      round(mean(log_df$Best_CCC, na.rm = TRUE), 3),
      round(mean(log_df$Best_RPD, na.rm = TRUE), 3)
    ),
    备注 = c("完成的建模任务数", "成功填补的缺失值总数",
            "所有深度层中最佳表现", "所有深度层中最差表现", "所有深度层平均表现",
            "最佳方法的RMSE平均值", "最佳方法的MAE平均值", "最佳方法的CCC平均值", "最佳方法的RPD平均值"),
    stringsAsFactors = FALSE
  )

  addWorksheet(wb, "汇总统计")
  writeData(wb, "汇总统计", summary_data)
  addStyle(wb, "汇总统计", headerStyle, rows = 1, cols = 1:ncol(summary_data), gridExpand = TRUE)
  setColWidths(wb, "汇总统计", cols = 1:3, widths = "auto")

  # 工作表3：方法使用统计（去除样本数后缀）
  # 从方法名中提取基础方法名（去除_数字后缀）
  base_methods <- gsub("_\\d+$", "", log_df$Best_Method)
  method_summary <- as.data.frame(table(base_methods))
  names(method_summary) <- c("最佳方法", "使用次数")
  method_summary$使用比例 <- round(method_summary$使用次数 / sum(method_summary$使用次数) * 100, 1)

  addWorksheet(wb, "方法使用统计")
  writeData(wb, "方法使用统计", method_summary)
  addStyle(wb, "方法使用统计", headerStyle, rows = 1, cols = 1:ncol(method_summary), gridExpand = TRUE)
  setColWidths(wb, "方法使用统计", cols = 1:3, widths = "auto")

  # 工作表4：深度层统计
  if("Depth_Layer" %in% names(log_df)) {
    depth_summary <- aggregate(log_df[c("Best_R2", "Filled_Count")],
                              by = list(log_df$Depth_Layer),
                              function(x) c(mean = mean(x, na.rm = TRUE),
                                           sum = sum(x, na.rm = TRUE)))

    depth_stats <- data.frame(
      深度层 = depth_summary$Group.1,
      平均R2 = round(depth_summary$Best_R2[,"mean"], 3),
      填补数量 = depth_summary$Filled_Count[,"sum"],
      stringsAsFactors = FALSE
    )

    addWorksheet(wb, "深度层统计")
    writeData(wb, "深度层统计", depth_stats)
    addStyle(wb, "深度层统计", headerStyle, rows = 1, cols = 1:ncol(depth_stats), gridExpand = TRUE)
    setColWidths(wb, "深度层统计", cols = 1:3, widths = "auto")
  }

  # 保存Excel文件
  saveWorkbook(wb, report_path, overwrite = TRUE)
  cat("填补报告已保存至:", report_path, "\n")

  # 显示汇总统计
  cat("\n缺失值填补汇总:\n")
  cat("总填补任务:", nrow(log_df), "\n")
  cat("总填补缺失值:", sum(log_df$Filled_Count), "\n")

  method_summary <- table(base_methods)
  cat("最佳方法统计:\n")
  for(method in names(method_summary)) {
    cat("  ", method, ":", method_summary[method], "次\n")
  }

  # 按深度层统计
  if("Depth_Layer" %in% names(log_df)) {
    depth_summary <- aggregate(log_df$Best_R2, by = list(log_df$Depth_Layer), mean, na.rm = TRUE)
    names(depth_summary) <- c("Depth_Layer", "Mean_R2")

    cat("各深度层平均R²:\n")
    for(i in 1:nrow(depth_summary)) {
      cat("  ", depth_summary$Depth_Layer[i], ":", round(depth_summary$Mean_R2[i], 3), "\n")
    }
  }
}

# --- 应用分层保底策略 ---
apply_hierarchical_fallback <- function(data, county, year, soil_property, layer_name, missing_count, config) {

  # 创建统一的上下文对象
  ctx <- create_fallback_context(data, county, year, soil_property, layer_name, config)

  gap_config <- config$gap_filling
  hierarchical_strategies <- gap_config$hierarchical_fallback
  current_data <- data  # 当前数据状态，会在策略间传递

  # 记录初始缺失值数量
  initial_missing <- ctx$count_missing(current_data)

  for(strategy in hierarchical_strategies) {
    cat("尝试保底策略:", strategy, "\n")

    # 记录策略执行前的缺失值数量
    before_missing <- ctx$count_missing(current_data)

    # 每个策略基于当前数据状态执行
    result <- switch(strategy,
      "profile_interpolation" = apply_profile_interpolation(current_data, ctx),
      "neighbor_mean" = apply_neighbor_mean_fallback(current_data, ctx),
      "depth_mean" = apply_depth_mean_fallback(current_data, ctx),
      NULL
    )

    # 检查策略是否成功
    if(!is.null(result)) {
      # 计算策略执行后的缺失值数量
      after_missing <- ctx$count_missing(result)
      filled_by_strategy <- before_missing - after_missing

      if(filled_by_strategy > 0) {
        cat("保底策略", strategy, "成功填补", filled_by_strategy, "个缺失值\n")
        current_data <- result  # 更新当前数据状态

        if(after_missing == 0) {
          cat("所有缺失值已填补完成\n")
          return(list(data = current_data, method = paste("fallback:", strategy)))
        } else {
          cat("还有", after_missing, "个缺失值，继续下一个策略\n")
        }
      } else {
        cat("保底策略", strategy, "未填补任何缺失值，尝试下一个策略\n")
      }
    } else {
      cat("保底策略", strategy, "失败：不满足执行条件，尝试下一个策略\n")
    }
  }

  # 检查最终结果
  final_missing <- ctx$count_missing(current_data)
  total_filled <- initial_missing - final_missing

  if(total_filled > 0) {
    cat("保底策略总共填补", total_filled, "个缺失值，还有", final_missing, "个缺失值\n")
    return(list(data = current_data, method = "fallback:partial"))
  } else {
    cat("所有保底策略都失败\n")
    return(NULL)
  }
}

# --- 剖面插值外推策略 ---
apply_profile_interpolation <- function(data, ctx) {
  cat("尝试剖面插值外推策略...\n")

  # 找到有缺失值的剖面
  target_data <- ctx$filter_target(data)
  missing_profiles <- target_data %>%
    filter(is.na(!!sym(ctx$soil_property))) %>%
    pull(!!sym(ctx$cols$profile)) %>%
    unique()

  if(length(missing_profiles) == 0) {
    cat("没有缺失值需要填补\n")
    return(data)
  }

  updated_data <- data
  filled_count <- 0

  for(profile_id in missing_profiles) {
    cat("处理剖面:", profile_id, "\n")

    # 获取该剖面所有深度层的有效数据
    profile_data <- data %>%
      filter(
        !!sym(ctx$cols$county) == ctx$target_county,
        !!sym(ctx$cols$year) == ctx$target_year,
        !!sym(ctx$cols$profile) == profile_id,
        !is.na(!!sym(ctx$soil_property))
      ) %>%
      arrange(!!sym(ctx$cols$depth))

    if(nrow(profile_data) < 2) {
      cat("剖面", profile_id, "有效数据点不足，跳过\n")
      next
    }

    depths <- profile_data[[ctx$cols$depth]]
    values <- profile_data[[ctx$soil_property]]

    # 使用基于物理约束的PCHIP插值
    interpolated_value <- tryCatch({
      if(length(depths) >= 3) {
        # 检查数据范围和变化趋势
        if(min(values) >= 0 && max(values) > 0) {
          # 如果所有值都为正，使用对数变换确保正值特性
          if(all(values > 0)) {
            log_values <- log(values)
            spline_result <- spline(depths, log_values, xout = ctx$target_depth, method = "natural")
            interpolated_value <- exp(spline_result$y)
            cat("剖面", profile_id, "使用对数样条插值\n")
          } else {
            # 包含零值，使用标准样条插值 + 物理约束
            spline_result <- spline(depths, values, xout = ctx$target_depth, method = "natural")
            interpolated_value <- max(0.001, spline_result$y)
            if(spline_result$y < 0) {
              cat("剖面", profile_id, "样条插值产生负值，已修正为正值\n")
            }
          }
        } else {
          # 数据异常（包含负值），使用保守的线性插值
          cat("剖面", profile_id, "数据包含负值，使用线性插值\n")
          linear_result <- approx(depths, values, xout = ctx$target_depth, rule = 2)
          interpolated_value <- max(0.001, linear_result$y)
        }
        interpolated_value
      } else {
        # 数据点不足，使用线性插值
        linear_result <- approx(depths, values, xout = ctx$target_depth, rule = 2)
        max(0.001, linear_result$y)
      }
    }, error = function(e) {
      cat("剖面", profile_id, "插值失败:", e$message, "\n")
      return(NA)
    })

    if(!is.na(interpolated_value)) {
      # 更新数据（保留两位小数）
      update_mask <- updated_data[[ctx$cols$county]] == ctx$target_county &
                     updated_data[[ctx$cols$year]] == ctx$target_year &
                     updated_data[[ctx$cols$depth]] == ctx$target_depth &
                     updated_data[[ctx$cols$profile]] == profile_id &
                     is.na(updated_data[[ctx$soil_property]])

      if(any(update_mask)) {
        updated_data[update_mask, ctx$soil_property] <- round(interpolated_value, 2)
        filled_count <- filled_count + sum(update_mask)
        cat("剖面", profile_id, "插值成功，填补值:", round(interpolated_value, 2), "\n")
      }
    }
  }

  if(filled_count > 0) {
    cat("剖面插值外推策略填补了", filled_count, "个缺失值\n")
    return(updated_data)
  } else {
    cat("剖面插值外推策略失败：未填补任何缺失值\n")
    return(NULL)
  }
}

# --- 邻近层均值策略 ---
apply_neighbor_mean_fallback <- function(data, ctx) {
  cat("尝试邻近层均值策略...\n")

  # 找到有缺失值的剖面
  target_data <- ctx$filter_target(data)
  missing_profiles <- target_data %>%
    filter(is.na(!!sym(ctx$soil_property))) %>%
    pull(!!sym(ctx$cols$profile)) %>%
    unique()

  if(length(missing_profiles) == 0) {
    cat("没有缺失值需要填补\n")
    return(data)
  }

  updated_data <- data
  filled_count <- 0

  for(profile_id in missing_profiles) {
    # 获取该剖面其他深度层的有效数据
    profile_neighbor_data <- data %>%
      filter(
        !!sym(ctx$cols$county) == ctx$target_county,
        !!sym(ctx$cols$year) == ctx$target_year,
        !!sym(ctx$cols$profile) == profile_id,
        !!sym(ctx$cols$depth) != ctx$target_depth,
        !is.na(!!sym(ctx$soil_property))
      )

    if(nrow(profile_neighbor_data) == 0) {
      cat("剖面", profile_id, "无邻近层数据，跳过\n")
      next
    }

    # 计算邻近层均值
    neighbor_mean <- mean(profile_neighbor_data[[ctx$soil_property]], na.rm = TRUE)

    # 更新缺失值（保留两位小数）
    update_mask <- updated_data[[ctx$cols$county]] == ctx$target_county &
                   updated_data[[ctx$cols$year]] == ctx$target_year &
                   updated_data[[ctx$cols$depth]] == ctx$target_depth &
                   updated_data[[ctx$cols$profile]] == profile_id &
                   is.na(updated_data[[ctx$soil_property]])

    if(any(update_mask)) {
      updated_data[update_mask, ctx$soil_property] <- round(neighbor_mean, 2)
      filled_count <- filled_count + sum(update_mask)
      cat("剖面", profile_id, "邻近层均值:", round(neighbor_mean, 3), "\n")
    }
  }

  if(filled_count > 0) {
    cat("邻近层均值策略填补了", filled_count, "个缺失值\n")
    return(updated_data)
  } else {
    cat("邻近层均值策略失败：无可用的邻近层数据\n")
    return(NULL)
  }
}

# --- 同深度层均值策略 ---
apply_depth_mean_fallback <- function(data, ctx) {
  cat("尝试同深度层均值策略...\n")

  # 检查是否有缺失值需要填补
  if(ctx$count_missing(data) == 0) {
    cat("没有缺失值需要填补\n")
    return(data)
  }

  # 筛选同深度层的完整数据
  same_depth_data <- data %>%
    filter(
      !!sym(ctx$cols$depth) == ctx$target_depth,
      !is.na(!!sym(ctx$soil_property))
    )

  if(nrow(same_depth_data) == 0) {
    cat("同深度层无完整数据\n")
    return(NULL)
  }

  # 计算均值
  mean_value <- mean(same_depth_data[[ctx$soil_property]], na.rm = TRUE)
  cat("使用同深度层均值:", round(mean_value, 3), "\n")

  # 记录填补前的缺失值数量
  original_missing <- ctx$count_missing(data)

  # 更新数据（保留两位小数）
  updated_data <- data
  update_mask <- updated_data[[ctx$cols$county]] == ctx$target_county &
                 updated_data[[ctx$cols$year]] == ctx$target_year &
                 updated_data[[ctx$cols$depth]] == ctx$target_depth &
                 is.na(updated_data[[ctx$soil_property]])

  if(any(update_mask)) {
    updated_data[update_mask, ctx$soil_property] <- round(mean_value, 2)
  }

  # 计算填补数量
  remaining_missing <- ctx$count_missing(updated_data)
  filled_count <- original_missing - remaining_missing

  if(filled_count > 0) {
    cat("同深度层均值策略成功填补", filled_count, "个缺失值\n")
    return(updated_data)
  } else {
    cat("同深度层均值策略失败：未填补任何缺失值\n")
    return(NULL)
  }
}

# --- 创建填补日志条目 ---
create_filling_log_entry <- function(county, year, soil_property, layer_name,
                                    missing_count, filled_count, best_method, best_r2, best_rmse,
                                    best_mae = NA, best_ccc = NA, best_rpd = NA, all_results = NULL, sample_size = NA, selected_variables = NULL, total_count = NA) {

  # 提取最佳方法的参数信息
  best_params <- list()
  if(!is.null(all_results) && !is.null(best_method) && best_method %in% names(all_results)) {
    method_result <- all_results[[best_method]]
    if(!is.null(method_result) && !is.null(method_result$tuned_params)) {
      if(best_method == "random_forest") {
        # 随机森林参数
        best_params$rf_ntree <- method_result$tuned_params$ntree
        best_params$rf_nodesize <- method_result$tuned_params$nodesize
        best_params$rf_mtry_factor <- method_result$tuned_params$mtry_factor
        if(!is.null(method_result$features)) {
          best_params$rf_mtry <- max(1, floor(method_result$tuned_params$mtry_factor * length(method_result$features)))
        }
      } else if(best_method %in% c("ordinary_kriging", "regression_kriging")) {
        # 克里金参数
        best_params$kr_nmax <- method_result$tuned_params$nmax
        best_params$kr_nmin <- method_result$tuned_params$nmin
        best_params$kr_vgm_model <- method_result$tuned_params$vgm_model
      }
    }
  }

  return(list(
    county = county,
    year = year,
    soil_property = soil_property,
    layer_name = layer_name,
    missing_count = missing_count,
    filled_count = filled_count,
    best_method = best_method,
    best_r2 = best_r2,
    best_rmse = best_rmse,
    best_mae = best_mae,
    best_ccc = best_ccc,
    best_rpd = best_rpd,
    sample_size = sample_size,  # 添加sample_size字段
    total_count = total_count,  # 添加total_count字段
    selected_variables = selected_variables,  # 添加选择的变量
    # 添加参数信息
    rf_ntree = best_params$rf_ntree,
    rf_nodesize = best_params$rf_nodesize,
    rf_mtry_factor = best_params$rf_mtry_factor,
    rf_mtry = best_params$rf_mtry,
    kr_nmax = best_params$kr_nmax,
    kr_nmin = best_params$kr_nmin,
    kr_vgm_model = best_params$kr_vgm_model,
    all_results = all_results,  # 保存所有三个方法的结果
    timestamp = Sys.time()
  ))
}

# --- 保存填补结果 ---
save_gap_filling_results <- function(filled_data, gap_filling_log, config) {

  cat("\n=== 保存填补结果 ===\n")

  # 统计填补情况
  if(length(gap_filling_log) > 0) {
    properties_processed <- unique(sapply(gap_filling_log, function(x) x$soil_property))
    counties_processed <- unique(sapply(gap_filling_log, function(x) x$county))
    years_processed <- unique(sapply(gap_filling_log, function(x) x$year))

    cat("本次处理统计:\n")
    cat("  处理的属性:", paste(properties_processed, collapse = ", "), "\n")
    cat("  处理的县城:", paste(counties_processed, collapse = ", "), "\n")
    cat("  处理的年份:", paste(years_processed, collapse = ", "), "\n")
    cat("  总填补任务:", length(gap_filling_log), "个\n")
  }
  
  output_dir <- config$data_paths$output_directory
  
  # 保存填补后的Excel文件
  tryCatch({
    # 1. 读取basic_data获取列结构
    basic_data_path <- config$data_paths$basic_data_path
    basic_template <- readxl::read_excel(basic_data_path)
    basic_columns <- names(basic_template)

    # 2. 从filled_data中选择basic_data的列
    available_cols <- intersect(basic_columns, names(filled_data))
    basic_filled_data <- filled_data[, available_cols]

    cat("筛选后有", ncol(basic_filled_data), "列\n")

    # 3. 保存基础数据填补结果
    filled_excel_path <- file.path(output_dir, config$gap_filling$filled_excel_filename)
    openxlsx::write.xlsx(basic_filled_data, filled_excel_path, overwrite = TRUE)
    cat("填补后的数据已保存至:", filled_excel_path, "\n")

  }, error = function(e) {
    cat("保存Excel文件失败:", e$message, "\n")
  })
  
  # 生成填补报告
  if(length(gap_filling_log) > 0) {
    generate_gap_filling_report(gap_filling_log, config)
  } else {
    cat("无填补记录\n")
  }
}

