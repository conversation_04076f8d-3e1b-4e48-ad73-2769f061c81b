# -*- coding: utf-8 -*-
"""
ERA5-Land 气象数据批量下载与处理脚本 (Google Earth Engine)
============================================================
本脚本通过 GEE 从 'ECMWF/ERA5_LAND/HOURLY' 数据集批量提取并处理气象数据。
================================================================================

- **土壤温度/水分 (Soil Temperature/Water Content)**: 提供4个原始深度层。
    - 层1: 0 - 7 cm
    - 层2: 7 - 28 cm
    - 层3: 28 - 100 cm
    - 层4: 100 - 289 cm
    *本脚本已包含对全部4个原始层的处理逻辑，可合成任意指定深度。*

核心功能:
- 支持处理气温、风速、辐射、降水、蒸发、土壤温湿度等多个变量。
- **结构清晰**: 采用模块化设计，将数据计算与导出任务分离，提升代码可读性。
- 对瞬时变量(如气温)直接计算年统计，对累积变量(如降水)先按日聚合再统计。
- 自动将原生土壤分层数据加权合成为用户自定义的深度剖面。
- **按年归档**: 导出结果为 GeoTIFF 格式，并按年份在 Google Drive 的指定根目录下创建子文件夹存放。

使用方法:
1. 在"核心参数配置"区域设置您的 GEE_PROJECT_ID, YEARS, ROI_PATH 等参数。
2. (可选) 在 `DEPTH_RANGES_CM` 中定义您需要的土壤深度剖面。
3. 运行脚本，处理任务将提交至您的 Google Earth Engine 账户。
4. 前往 GEE 任务页面 (https://code.earthengine.google.com/tasks) 手动运行已启动的任务。
"""

# --- 库导入 ---
import ee
import time

# --- 核心参数配置 ---
# GEE项目ID (请确保与您的项目ID一致)
GEE_PROJECT_ID = 'ee-aethxz247'

# 定义目标年份
YEARS = [1980, 2023]

# 定义研究区域 (Region of Interest, ROI)
# 脚本将复用与Landsat脚本相同的研究区路径
ROI_PATH = 'projects/ee-aethxz247/assets/DSS'

# Google Drive 中用于保存结果的文件夹名称
OUTPUT_FOLDER = 'GEE_ERA5'

# 导出影像的空间分辨率 (米)
EXPORT_SCALE = 250

# 导出的目标坐标参考系 (CRS)
EXPORT_CRS = 'EPSG:4550'

# --- 自定义深度范围 (厘米) ---
# 用户可在此自由修改深度区间，脚本将自动根据 ERA5-Land 原生分层进行加权。
DEPTH_RANGES_CM = [
    (0, 10),   # 0–10 cm
    (10, 30),  # 10–30 cm
    (30, 60),  # 30–60 cm
    (60, 100), # 60–100 cm
]

# --- 待处理变量定义 ---
# 结构说明:
#   'name': GEE 数据集中的波段名。
#   'base_name': 导出文件名的基础部分。
#   'type': 'instantaneous' (瞬时值) 或 'cumulative' (累积值)。
#   'stats': 需要计算的统计指标列表。
SOIL_STATS_TO_COMPUTE = ['mean', 'max', 'min']

SOIL_VARIABLES_CONFIG = [
    {
        # 土壤温度 (单位: K)
        'base_name': 'Soil_Temperature',
        'bands': ['soil_temperature_level_1', 'soil_temperature_level_2', 'soil_temperature_level_3', 'soil_temperature_level_4'],
        'band_prefix': 'soil_temp'
    },
    {
        # 土壤体积含水量 (单位: m^3/m^3)
        'base_name': 'Soil_Water_Content',
        'bands': ['volumetric_soil_water_layer_1', 'volumetric_soil_water_layer_2', 'volumetric_soil_water_layer_3', 'volumetric_soil_water_layer_4'],
        'band_prefix': 'soil_water'
    }
]

OTHER_VARIABLES_TO_PROCESS = [
    # --- 瞬时变量 (除土壤) ---
    # 2m气温 (单位: K)
    {'name': 'temperature_2m', 'base_name': 'Air_Temperature_2m', 'type': 'instantaneous', 'stats': ['mean', 'max', 'min']},
    # 10m风速U分量 (单位: m/s)
    {'name': 'u_component_of_wind_10m', 'base_name': 'Wind_U_Component_10m', 'type': 'instantaneous', 'stats': ['mean', 'max', 'min']},
    # 10m风速V分量 (单位: m/s)
    {'name': 'v_component_of_wind_10m', 'base_name': 'Wind_V_Component_10m', 'type': 'instantaneous', 'stats': ['mean', 'max', 'min']},

    # --- 累积变量 ---
    # 地表净太阳短波辐射 (单位: J/m^2)
    {'name': 'surface_net_solar_radiation', 'base_name': 'Surface_Net_Solar_Radiation', 'type': 'cumulative', 'stats': ['sum', 'mean', 'max', 'min']},
    # 地表净热力长波辐射 (单位: J/m^2)
    {'name': 'surface_net_thermal_radiation', 'base_name': 'Surface_Net_Thermal_Radiation', 'type': 'cumulative', 'stats': ['sum', 'mean', 'max', 'min']},
    # 总蒸散发 (单位: m, 水当量)
    {'name': 'total_evaporation', 'base_name': 'Total_Evaporation', 'type': 'cumulative', 'stats': ['sum', 'mean', 'max', 'min']},
    # 总降水量 (单位: m, 水当量)
    {'name': 'total_precipitation', 'base_name': 'Total_Precipitation', 'type': 'cumulative', 'stats': ['sum', 'mean', 'max', 'min']},
]

# --- GEE初始化 ---
try:
    ee.Initialize(project=GEE_PROJECT_ID, opt_url='https://earthengine-highvolume.googleapis.com')
    print(f"GEE初始化成功！使用项目: {GEE_PROJECT_ID}")
except Exception as e:
    print(f"GEE初始化失败。错误: {e}")
    print("请检查网络连接、GEE项目ID是否正确，或尝试 'earthengine authenticate' 命令重新认证。")
    exit()

# --- 研究区域加载 ---
try:
    roi = ee.FeatureCollection(ROI_PATH).geometry()
    # 尝试获取ROI信息以验证其有效性
    roi.area(maxError=1).getInfo()
    print(f"研究区域(ROI)加载成功: '{ROI_PATH}'")
except Exception as e:
    print(f"错误: 无法加载研究区域(ROI)。请确认路径 '{ROI_PATH}' 是否正确，以及您是否有权访问。")
    print(f"GEE返回的错误信息: {e}")
    exit()

# --- 数据导出函数 ---
def export_to_drive(image, base_name, statistic, year):
    """统一的导出函数，负责构造文件名并启动导出任务。"""

    # 文件名和任务描述，确保唯一性
    # 最终文件名格式: BaseName_Statistic_Year (e.g., Air_Temperature_2m_Max_1980)
    stat_str = statistic.capitalize()
    file_and_task_name = f'{base_name}_{stat_str}_{year}'

    # --- 文件名统计口径说明 (注释) ---
    # 1. 瞬时变量 (如气温):
    #    - Mean/Max/Min: 基于全年所有【小时】数据计算的统计值。
    #      (e.g., 'Max' 指的是年内小时最高温)
    # 2. 累积变量 (如降水):
    #    - Sum: 全年【总量】。
    #    - Mean/Max/Min: 基于【日总量】数据在年内计算的统计值。
    #      (e.g., 'Max' 指的是年内日最大降水量)

    # 解决方案：创建正确的文件夹结构
    # Google Earth Engine会自动创建文件夹层级，使用 '/' 分隔符
    # 这将创建: GEE_ERA5 (主文件夹) -> 1980/2023 (年份子文件夹)
    target_folder = f'{OUTPUT_FOLDER}/{year}'

    task = ee.batch.Export.image.toDrive(
        image=image.toDouble(),
        description=file_and_task_name,
        folder=target_folder,  # 使用完整的文件夹路径
        fileNamePrefix=file_and_task_name,  # 保持原有的文件名格式
        region=roi,
        scale=EXPORT_SCALE,
        crs=EXPORT_CRS,
        maxPixels=1e13,
        fileFormat='GeoTIFF'
    )
    task.start()
    print(f"  --> GEE任务 '{file_and_task_name}' 已启动，保存至: '{target_folder}'。")

# --- 数据准备函数 (计算但不导出) ---

def prepare_soil_images(start_date, end_date, year, roi):
    """
    统一处理所有分层土壤变量, 计算并返回待导出的影像列表。
    """
    images_to_export = []
    print("\n- 开始准备分层土壤变量影像...")

    for config in SOIL_VARIABLES_CONFIG:
        base_name = config['base_name']
        bands_to_load = config['bands']
        band_prefix = config['band_prefix']
        
        print(f"  - 处理: {base_name}")

        hourly_soil_collection = (ee.ImageCollection('ECMWF/ERA5_LAND/HOURLY')
                                .filterDate(start_date, end_date)
                                .select(bands_to_load)
                                .filterBounds(roi))

        for stat in SOIL_STATS_TO_COMPUTE:
            print(f"    计算 {stat} 值并合成...")
            
            annual_stat_layers = None
            if stat == 'mean':
                annual_stat_layers = hourly_soil_collection.mean()
            elif stat == 'max':
                annual_stat_layers = hourly_soil_collection.max()
            elif stat == 'min':
                annual_stat_layers = hourly_soil_collection.min()

            if annual_stat_layers is None:
                continue

            l1 = annual_stat_layers.select(bands_to_load[0]).rename('l1')
            l2 = annual_stat_layers.select(bands_to_load[1]).rename('l2')
            l3 = annual_stat_layers.select(bands_to_load[2]).rename('l3')
            l4 = annual_stat_layers.select(bands_to_load[3]).rename('l4')
            source_layers = ee.Image.cat([l1, l2, l3, l4])

            def calc_coeff(target_start, target_end, layer_start, layer_end, total_depth):
                overlap = max(0, min(target_end, layer_end) - max(target_start, layer_start))
                return overlap / total_depth if total_depth > 0 else 0

            band_list = []
            for depth_start, depth_end in DEPTH_RANGES_CM:
                total_depth = depth_end - depth_start
                if total_depth <= 0: continue

                c1 = calc_coeff(depth_start, depth_end, 0, 7, total_depth)
                c2 = calc_coeff(depth_start, depth_end, 7, 28, total_depth)
                c3 = calc_coeff(depth_start, depth_end, 28, 100, total_depth)
                c4 = calc_coeff(depth_start, depth_end, 100, 289, total_depth)

                if (c1 + c2 + c3 + c4) > 0:
                    expression = f'b("l1")*{c1} + b("l2")*{c2} + b("l3")*{c3} + b("l4")*{c4}'
                    composite_band = source_layers.expression(expression)
                    depth_band_name = f"{band_prefix}_{depth_start}_{depth_end}cm"
                    band_list.append(composite_band.rename(depth_band_name))

            if not band_list:
                print(f"    * 警告: 变量 {base_name} 的自定义深度区间均超出可用层范围，已跳过。")
                continue
            
            synthesized_image = ee.Image.cat(band_list).clip(roi)
            images_to_export.append({
                'image': synthesized_image,
                'base_name': base_name,
                'statistic': stat
            })
    return images_to_export

def prepare_other_variable_images(start_date, end_date, year, roi):
    """
    处理所有非土壤变量，计算并返回待导出的影像列表。
    """
    images_to_export = []
    print("\n- 开始准备其他气象变量影像...")

    for var_info in OTHER_VARIABLES_TO_PROCESS:
        band_name = var_info['name']
        base_name = var_info['base_name']
        var_type = var_info['type']
        stats_to_compute = var_info['stats']
            
        print(f"  - 处理变量: {base_name} (类型: {var_type})")
            
        effective_end_date = end_date
        if var_type == 'cumulative':
            effective_end_date = end_date.advance(1, 'day')

        hourly_collection = (ee.ImageCollection('ECMWF/ERA5_LAND/HOURLY')
                            .filterDate(start_date, effective_end_date)
                            .select(band_name)
                            .filterBounds(roi))

        collection_for_stats = hourly_collection
        if var_type == 'cumulative':
            print(f"    计算每日总量...")
            num_days = end_date.difference(start_date, 'day')

            def daily_aggregator(day_offset):
                current_day_start = start_date.advance(day_offset, 'day')
                current_day_end = current_day_start.advance(1, 'day')
                # 使用 01:00 当天至 01:00 次日的时间窗口以正确覆盖全天 24 小时累积量。
                # ERA5 累积变量在 00:00 时刻的值对应上一日 23:00–00:00 的累积，
                # 不属于当日；因此应从 01:00 开始并包含次日 00:00。
                daily_sum = hourly_collection.filterDate(
                    current_day_start.advance(1, 'hour'),
                    current_day_end.advance(1, 'hour')
                ).sum()
                return daily_sum.set('system:time_start', current_day_start.millis())

            day_offsets = ee.List.sequence(0, num_days.subtract(1))
            collection_for_stats = ee.ImageCollection.fromImages(day_offsets.map(daily_aggregator))

        print(f"    在 {'日' if var_type == 'cumulative' else '小时'} 数据上进行年度统计...")
        for stat in stats_to_compute:
            result_image = None
            if stat == 'sum' and var_type == 'cumulative':
                result_image = collection_for_stats.sum()
            elif stat == 'mean':
                result_image = collection_for_stats.mean()
            elif stat == 'max':
                result_image = collection_for_stats.max()
            elif stat == 'min':
                result_image = collection_for_stats.min()
            
            if result_image is not None:
                images_to_export.append({
                    'image': result_image.clip(roi),
                    'base_name': base_name,
                    'statistic': stat
                })
    return images_to_export


# --- 主执行流程 ---
def main():
    """主函数，编排所有数据处理和导出任务。"""
    script_start_time = time.time()
    print(f"--- 开始处理年份: {YEARS} ---")

    for year in YEARS:
        print(f"\n--- 正在处理 {year}年 ---")
        
        # 统一管理当年所有待导出的影像
        all_images_for_year = []
        
        start_date = ee.Date(f'{year}-01-01')
        end_date = ee.Date(f'{year + 1}-01-01')

        # [步骤1] 准备所有土壤变量影像
        soil_images = prepare_soil_images(start_date, end_date, year, roi)
        all_images_for_year.extend(soil_images)

        # [步骤2] 准备所有其他变量影像
        other_images = prepare_other_variable_images(start_date, end_date, year, roi)
        all_images_for_year.extend(other_images)
        
        # [步骤3] 统一启动当年的所有导出任务
        print(f"\n- {year}年: 所有影像计算完成，开始启动导出任务...")
        if not all_images_for_year:
            print(f"  * {year}年没有任何影像需要导出，已跳过。")
            continue
            
        for item in all_images_for_year:
            export_to_drive(
                image=item['image'],
                base_name=item['base_name'],
                statistic=item['statistic'],
                year=year
            )

    print(f"\n✅ 所有年份的所有导出任务均已成功启动！")
    print(f"请访问 https://code.earthengine.google.com/tasks 页面查看并手动运行它们。")
    script_end_time = time.time()
    print(f"\n脚本启动流程完成，总耗时: {script_end_time - script_start_time:.2f} 秒。")


if __name__ == '__main__':
    main() 