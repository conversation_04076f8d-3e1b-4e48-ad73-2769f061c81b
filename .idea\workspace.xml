<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="ca70f05a-2605-4425-bf05-9a2ddba033ab" name="更改" comment="">
      <change beforePath="$PROJECT_DIR$/.idea/workspace.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/workspace.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/03 VIF方差膨胀剔除/correlation_analysis-全.py" beforeDir="false" afterPath="$PROJECT_DIR$/03 VIF方差膨胀剔除/correlation_analysis-全.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/03 VIF方差膨胀剔除/correlation_analysis.py" beforeDir="false" afterPath="$PROJECT_DIR$/03 VIF方差膨胀剔除/correlation_analysis.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/04 剖面数据重构建模/.Rhistory" beforeDir="false" afterPath="$PROJECT_DIR$/04 剖面数据重构建模/.Rhistory" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/04 剖面数据重构建模/01_main.R" beforeDir="false" afterPath="$PROJECT_DIR$/04 剖面数据重构建模/01_main.R" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/04 剖面数据重构建模/02_config.yml" beforeDir="false" afterPath="$PROJECT_DIR$/04 剖面数据重构建模/02_config.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/04 剖面数据重构建模/04_data.R" beforeDir="false" afterPath="$PROJECT_DIR$/04 剖面数据重构建模/04_data.R" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="2zLbjZ5fuPlHfbbuQFdfFhAn2vW" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;Python 测试.Python 测试 (test_letter_logic.py 内).executor&quot;: &quot;Run&quot;,
    &quot;Python 测试.test_assumptions_example.test_real_data_example 的 Python 测试.executor&quot;: &quot;Run&quot;,
    &quot;Python.01_main_analysis.executor&quot;: &quot;Run&quot;,
    &quot;Python.08_assumption_tests.executor&quot;: &quot;Run&quot;,
    &quot;Python.A_main.executor&quot;: &quot;Run&quot;,
    &quot;Python.B_config.executor&quot;: &quot;Run&quot;,
    &quot;Python.a_assumption_main.executor&quot;: &quot;Run&quot;,
    &quot;Python.ajk.executor&quot;: &quot;Run&quot;,
    &quot;Python.assumption_tests.executor&quot;: &quot;Run&quot;,
    &quot;Python.config.executor&quot;: &quot;Run&quot;,
    &quot;Python.correlation_analysis-全.executor&quot;: &quot;Run&quot;,
    &quot;Python.correlation_analysis.executor&quot;: &quot;Run&quot;,
    &quot;Python.main.executor&quot;: &quot;Run&quot;,
    &quot;Python.main_simple.executor&quot;: &quot;Run&quot;,
    &quot;Python.run_analysis.executor&quot;: &quot;Run&quot;,
    &quot;Python.statistical_analysis.executor&quot;: &quot;Run&quot;,
    &quot;Python.tif提取-论文.executor&quot;: &quot;Run&quot;,
    &quot;Python.vif_analysis (1).executor&quot;: &quot;Run&quot;,
    &quot;Python.vif_analysis copy.executor&quot;: &quot;Run&quot;,
    &quot;Python.vif_analysis.executor&quot;: &quot;Run&quot;,
    &quot;Python.土壤剖面数据标准化.executor&quot;: &quot;Run&quot;,
    &quot;Python.批量重命名工具.executor&quot;: &quot;Run&quot;,
    &quot;Python.数据统计.executor&quot;: &quot;Run&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;last_opened_file_path&quot;: &quot;E:/05 Python/Devway/01 硕士论文/03 VIF方差膨胀剔除&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;com.jetbrains.python.configuration.PyActiveSdkModuleConfigurable&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="E:\05 Python\Devway\01 硕士论文\03 VIF方差膨胀剔除" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="E:\05 Python\Devway\01 硕士论文\05 时空变化分析" />
    </key>
  </component>
  <component name="RunManager" selected="Python.correlation_analysis-全">
    <configuration name="数据统计" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="01 硕士论文" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/05 时空变化分析" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/05 时空变化分析/数据统计.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="correlation_analysis-全" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="01 硕士论文" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/03 VIF方差膨胀剔除" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/03 VIF方差膨胀剔除/correlation_analysis-全.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="correlation_analysis" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="01 硕士论文" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/03 VIF方差膨胀剔除" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/03 VIF方差膨胀剔除/correlation_analysis.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="vif_analysis-全" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="01 硕士论文" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/03 VIF方差膨胀剔除" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="E:\05 Python\Devway\01 硕士论文\03 VIF方差膨胀剔除\vif_analysis-全.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="vif_analysis" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="01 硕士论文" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/03 VIF方差膨胀剔除" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/03 VIF方差膨胀剔除/vif_analysis.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Python.correlation_analysis-全" />
        <item itemvalue="Python.correlation_analysis" />
        <item itemvalue="Python.vif_analysis" />
        <item itemvalue="Python.vif_analysis-全" />
        <item itemvalue="Python.数据统计" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-python-sdk-890ed5b35930-d9c5bdb153f4-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-251.23774.444" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="ca70f05a-2605-4425-bf05-9a2ddba033ab" name="更改" comment="" />
      <created>1751513435438</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1751513435438</updated>
    </task>
    <servers />
  </component>
</project>