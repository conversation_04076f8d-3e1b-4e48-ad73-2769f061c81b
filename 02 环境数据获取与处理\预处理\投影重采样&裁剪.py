# ---- 脚本说明 ----
"""
脚本功能说明:
    本脚本为最终优化版, 一次性完成 GeoTIFF 影像的【重投影 → 重采样 → 裁剪】。
    提供两种处理模式, 可在性能与稳定性之间选择。

工作流程:
    1. 根据 CONFIG 中的 'processing_mode' 选择工作流:
        - 'memory' (内存模式): 中间数据在内存中处理, 速度快, 适合多数情况。
        - 'disk' (磁盘模式): 中间数据写入临时文件, 占用内存少, 适合处理超大文件或内存不足的设备。
    2. 自动完成重投影、重采样和裁剪, 将结果写入 output_dir。

CONFIG 需配置的核心参数:
    shp_path           - (必需) 矢量边界文件(.shp)。
    tif_dir            - (必需) 待处理的 GeoTIFF 输入目录。
    output_dir         - (必需) 输出目录。
    target_crs         - (可选) 目标坐标系(如 'EPSG:4550')。若设置, 所有输出将强制转换到此CRS, 'clip_only' 将失效。设为 None 则使用 SHP 的 CRS。
    processing_mode    - (必需) 'memory' 或 'disk'。
    temp_dir           - (仅在 'disk' 模式下使用) 临时目录。
    max_workers        - (可选) 最大工作进程数。
    use_parallel       - (可选) 是否使用并行处理。
    target_resolution  - (可选) 目标分辨率元组 (x, y)。
    resampling         - (必选) 重采样算法, 如 "nearest", "bilinear"。
    force_nodata        - (可选) 统一 nodata 值，如需强制设置请填写数值，例如 -9999。
    clip_only          - (可选) 仅执行裁剪，不做重投影与重采样。
    all_touched        - (可选) 裁剪时是否包含与边界接触的所有像元。
"""

import os
import rasterio
from rasterio.io import MemoryFile
import geopandas as gpd
from pathlib import Path
from rasterio.mask import mask
from rasterio.warp import reproject, Resampling
from rasterio.transform import from_origin
import concurrent.futures
import multiprocessing
import time
from tqdm import tqdm
import numpy as np
import uuid
import math

# ---- 启用GDAL多线程 ----
os.environ.setdefault("GDAL_NUM_THREADS", "ALL_CPUS")

# ---- 配置区 ----
# 最佳实践配置，可按需修改
CONFIG = {
    'shp_path': r"E:\03 文件夹\00_论文资料\To 文彪\东三省shp\东三省shp.shp",
    'output_dir': r'D:\Desktop\环境辅助变量',
    'tif_dir': r"E:\03 文件夹\00_论文资料\To 文彪\环境辅助变量\分类变量",
    'target_crs': 'EPSG:4550',  # 目标坐标系(如 'EPSG:4326')。若设置, 所有输出将强制转换到此CRS。设为 None 则使用 SHP 的 CRS。
    'processing_mode': 'disk',  # 'memory' (性能优先) 或 'disk' (稳定优先)
    'temp_dir': r'D:\temp_processing_cache',  # 仅在 'disk' 模式下使用
    'max_workers': 4,
    'use_parallel': True,
    'target_resolution': (250, 250),
    'resampling': 'nearest',
    'force_nodata': -9999.0,  # 统一 nodata 值。对于浮点型数据，推荐使用-9999.0。对于整型，可设为 -32768 等。设为 None 则尝试保留原值。
    'clip_only': False,    # True 时仅执行裁剪，不做重投影与重采样。若设置了 target_crs，此项将无效。
    'all_touched': True,   # 裁剪时是否包含与边界接触的所有像元
}

# ---- 插值方法中英文对照表 ----
# 如需新增算法, 只需在此字典中添加条目, 同时确保 rasterio.Resampling 中存在对应名称
RESAMPLING_NAME_MAP = {
    'nearest': '最邻近插值',
    'bilinear': '双线性插值',
    'cubic': '三次卷积插值',
    'cubic_spline': '三次样条插值',
    'lanczos': 'Lanczos 插值',
    'average': '平均值重采样',
    'mode': '众数重采样'
}

# ---- 工具函数 ----

def restore_metadata(src, dest):
    """从源数据集恢复元数据(波段描述, 单位, 标签)到目标数据集。"""
    # 恢复波段描述
    if src.descriptions and any(src.descriptions):
        # 仅在描述数量与目标波段数匹配时才一次性赋值
        if len(src.descriptions) == dest.count:
            dest.descriptions = src.descriptions
        else:
            # 否则逐个设置，更安全
            print(f"  [元数据警告] {Path(src.name).name} 的波段描述数量与目标不匹配，将尝试逐个恢复。")
            for i, desc in enumerate(src.descriptions, 1):
                if i <= dest.count and desc:
                    dest.set_band_description(i, desc)

    # 恢复波段单位
    if hasattr(src, 'units') and src.units and any(src.units):
        if len(src.units) == dest.count:
            dest.units = src.units
        else:
            print(f"  [元数据警告] {Path(src.name).name} 的波段单位数量与目标不匹配，将尝试逐个恢复。")
            for i, unit in enumerate(src.units, 1):
                if i <= dest.count and unit:
                    dest.set_band_unit(i, unit)

    # 恢复标签
    all_tags = {}
    if hasattr(src, 'tags_ns') and src.tags_ns():
        for ns in src.tags_ns():
            all_tags[ns] = src.tags(ns)
    else:
        tags = src.tags()
        if tags:
            all_tags['default_namespace'] = tags
    
    for ns, tags in all_tags.items():
        if tags:
            if ns == 'default_namespace':
                dest.update_tags(**tags)
            else:
                dest.update_tags(ns=ns, **tags)

def find_tif_files(directory):
    """递归查找目录中的所有TIF文件"""
    p = Path(directory)
    # 优化：使用单次rglob和后缀检查，提高效率
    return [str(f) for f in p.rglob('*.tif*') if f.suffix.lower() in ('.tif', '.tiff')]

def read_shapefile(shp_path):
    """读取SHP文件，获取边界和坐标系"""
    try:
        shp = gpd.read_file(shp_path)
        if shp.empty:
            raise ValueError(f"SHP文件 {shp_path} 为空")
        return shp, shp.crs
    except Exception as e:
        raise Exception(f"读取SHP文件失败: {e}")

def is_nodata_compatible(value, dtype_str):
    """检查nodata值是否与给定的数据类型兼容。"""
    if value is None:
        return True
    dtype = np.dtype(dtype_str)
    if 'int' in dtype.name:
        try:
            info = np.iinfo(dtype)
            return info.min <= value <= info.max
        except ValueError:
            # 如果不是标准的numpy整型，则假定不兼容
            return False
    # 浮点型通常可以容纳任何数值
    return True

def get_output_profile(src_profile, new_transform=None, new_width=None, new_height=None, new_crs=None, nodata_val=None, new_dtype=None):
    """获取标准化的输出配置文件"""
    profile = src_profile.copy()
    
    # 应用新的空间参考信息
    if new_transform:
        profile['transform'] = new_transform
    if new_width:
        profile['width'] = new_width
    if new_height:
        profile['height'] = new_height
    if new_crs:
        profile['crs'] = new_crs
        
    # 强制提升数据类型
    if new_dtype:
        profile['dtype'] = new_dtype

    # 强制写入nodata值，确保一致性
    if nodata_val is not None:
        profile['nodata'] = nodata_val
    
    # 应用最佳实践选项
    profile.update({
        'driver': 'GTiff',
        'compress': 'lzw',
        'predictor': 2,
        'tiled': True,
        'blockxsize': 256,
        'blockysize': 256,
        'bigtiff': 'YES'
    })
    return profile

def reproject_to_memory(src_path, dst_crs, master_transform, master_width, master_height, resampling_method, src_nodata, dst_nodata, output_dtype):
    """(内存模式) 将TIF重投影并重采样到内存, 返回内存文件对象。"""
    try:
        with rasterio.open(src_path) as src:
            # --- 修改：不再计算，直接使用黄金标准模板 ---
            profile = get_output_profile(src.profile, new_transform=master_transform, new_width=master_width, new_height=master_height, new_crs=dst_crs, nodata_val=dst_nodata, new_dtype=output_dtype)
            
            memfile = MemoryFile()
            with memfile.open(**profile) as dst:
                for i in range(1, src.count + 1):
                    reproject(
                        source=rasterio.band(src, i),
                        destination=rasterio.band(dst, i),
                        src_transform=src.transform,
                        src_crs=src.crs,
                        dst_transform=master_transform, # 使用模板
                        dst_crs=dst_crs,
                        src_nodata=src_nodata,
                        dst_nodata=dst_nodata,
                        resampling=getattr(Resampling, resampling_method, Resampling.bilinear)
                    )
                
                restore_metadata(src, dst)
            
            return memfile

    except Exception as e:
        print(f"内存重投影 {Path(src_path).name} 时出错: {e}")
        return None

def reproject_to_disk(src_path, dst_path, dst_crs, master_transform, master_width, master_height, resampling_method, src_nodata, dst_nodata, output_dtype):
    """(磁盘模式) 将TIF重投影并重采样到磁盘临时文件。"""
    try:
        with rasterio.open(src_path) as src:
            # --- 修改：不再计算，直接使用黄金标准模板 ---
            profile = get_output_profile(src.profile, new_transform=master_transform, new_width=master_width, new_height=master_height, new_crs=dst_crs, nodata_val=dst_nodata, new_dtype=output_dtype)
            
            with rasterio.open(dst_path, 'w', **profile) as dst:
                for i in range(1, src.count + 1):
                    reproject(
                        source=rasterio.band(src, i),
                        destination=rasterio.band(dst, i),
                        src_transform=src.transform,
                        src_crs=src.crs,
                        dst_transform=master_transform, # 使用模板
                        dst_crs=dst_crs,
                        src_nodata=src_nodata,
                        dst_nodata=dst_nodata,
                        resampling=getattr(Resampling, resampling_method, Resampling.bilinear)
                    )
                
                restore_metadata(src, dst)
        return True
    except Exception as e:
        print(f"磁盘重投影 {Path(src_path).name} 时出错: {e}")
        return False

def clip_from_dataset(src_dataset, shapes, output_path, nodata_val, all_touched):
    """(内存模式) 从已打开的数据集裁剪, 并写入最终文件。"""
    try:
        # 确保在裁剪时使用一致的nodata值填充
        out_image, out_transform = mask(src_dataset, shapes, crop=True, all_touched=all_touched, nodata=nodata_val, filled=True)
        profile = get_output_profile(src_dataset.profile, new_transform=out_transform, new_width=out_image.shape[2], new_height=out_image.shape[1], nodata_val=nodata_val)
        
        with rasterio.open(output_path, "w", **profile) as dest:
            dest.write(out_image)
            restore_metadata(src_dataset, dest)

        return True
    except Exception as e:
        print(f"从内存裁剪 {Path(output_path).name} 时出错: {e}")
        return False

def clip_from_disk(tif_path, shapes, output_path, nodata_val, all_touched):
    """(磁盘模式) 从磁盘上的TIF文件裁剪, 并写入最终文件。"""
    try:
        with rasterio.open(tif_path) as src:
            # 确保在裁剪时使用一致的nodata值填充
            out_image, out_transform = mask(src, shapes, crop=True, all_touched=all_touched, nodata=nodata_val, filled=True)
            profile = get_output_profile(src.profile, new_transform=out_transform, new_width=out_image.shape[2], new_height=out_image.shape[1], nodata_val=nodata_val)
            
            with rasterio.open(output_path, "w", **profile) as dest:
                dest.write(out_image)
                restore_metadata(src, dest)
        return True
    except Exception as e:
        print(f"从磁盘裁剪 {Path(tif_path).name} 时出错: {e}")
        return False

def clip_only_tif(src_path, shapes, shp_crs, target_crs, output_path, nodata_val, all_touched):
    """
    仅裁剪TIF，不进行重投影/重采样。
    此函数执行严格的CRS检查，以确保输入数据符合预期。
    - 如果设置了 target_crs, 则栅格和矢量都必须是 target_crs。
    - 如果未设置 target_crs, 则栅格和矢量必须具有相同的 CRS。
    """
    try:
        with rasterio.open(src_path) as src:
            src_crs = src.crs

            # --- CRS 验证 ---
            error_msg = None
            if target_crs:
                # 模式一: 检查所有输入是否都符合 target_crs
                if src_crs != target_crs:
                    error_msg = f"栅格CRS '{src_crs}' 与目标CRS '{target_crs}' 不匹配。"
                elif shp_crs != target_crs:
                    error_msg = f"矢量CRS '{shp_crs}' 与目标CRS '{target_crs}' 不匹配。"
            else:
                # 模式二: 检查栅格和矢量CRS是否一致
                if src_crs != shp_crs:
                    error_msg = f"栅格CRS '{src_crs}' 与矢量CRS '{shp_crs}' 不匹配。"

            if error_msg:
                # 抛出特定错误，由上层捕获
                raise ValueError(error_msg)
            
            # --- 执行裁剪 ---
            # 经过验证，矢量与栅格坐标系一致，无需再转换
            out_nodata = nodata_val if nodata_val is not None else src.nodata

            # --- 数据类型兼容性检查与自动提升 ---
            output_dtype = src.profile['dtype']
            if out_nodata is not None:
                if not is_nodata_compatible(out_nodata, output_dtype):
                    new_dtype = 'float32'
                    print(f"\n[注意] {Path(src_path).name} (dtype: {output_dtype}) 与 nodata 值 {out_nodata} 不兼容。")
                    print(f"       在仅裁剪模式下，输出文件将被自动转换为 {new_dtype} 以确保数据统一性。")
                    output_dtype = new_dtype
            
            out_image, out_transform = mask(src, shapes, crop=True, all_touched=all_touched, nodata=out_nodata, filled=True)
            
            profile = get_output_profile(src.profile, new_transform=out_transform, new_width=out_image.shape[2], new_height=out_image.shape[1], nodata_val=out_nodata, new_dtype=output_dtype)
            # 确保输出文件的CRS是正确的
            profile['crs'] = src_crs

            with rasterio.open(output_path, "w", **profile) as dest:
                dest.write(out_image)
                restore_metadata(src, dest)
        return True
    except Exception as e:
        # 将原始异常向上抛出，由 process_tif 统一处理
        raise e

def check_crs_and_resolution(src_crs, target_crs, resolution):
    """检查CRS单位和分辨率设置是否匹配，并在可能存在问题时发出警告。"""
    if resolution is None or src_crs is None or target_crs is None:
        return
    if src_crs.is_geographic and not target_crs.is_geographic:
        print("="*20 + " 配置警告 " + "="*20)
        print(f"源坐标系 '{src_crs.to_string()}' 是地理坐标系 (单位: 度)。")
        print(f"目标坐标系 '{target_crs.to_string()}' 是投影坐标系 (单位: 米)。")
        print(f"您设置的目标分辨率是 {resolution}，这将被解释为目标坐标系的单位(米)。")
        print("如果您的意图是设置以度为单位的分辨率，请确保源和目标CRS都是地理坐标系或进行相应转换。")
        print("="*52)

# ---- 主流程函数 ----

def process_tif(params):
    """处理单个TIF文件的包装函数, 根据模式选择工作流。"""
    # --- 从字典解包参数，提高可读性和健壮性 ---
    tif_path = Path(params['tif_path'])
    shapes = params['shapes']
    shp_original_crs = params['shp_original_crs']
    # --- 新增: 接收黄金标准模板参数 ---
    master_transform = params['master_transform']
    master_width = params['master_width']
    master_height = params['master_height']
    # --- 结束 ---
    reproj_target_crs = params['reproj_target_crs']
    base_tif_dir = Path(params['base_tif_dir'])
    output_dir = Path(params['output_dir'])
    resampling_method = params['resampling']
    mode = params['mode']
    temp_dir = Path(params['temp_dir'])
    clip_only = params['clip_only']
    all_touched = params['all_touched']

    relative_path = tif_path.relative_to(base_tif_dir)
    output_path = output_dir / relative_path
    output_path.parent.mkdir(parents=True, exist_ok=True)

    # 该值将作为整个批次的统一nodata值
    unified_nodata_value = params.pop('dst_nodata', -9999.0)

    # 在 clip_only 模式下，兼容性检查在专用函数内部完成
    if clip_only:
        try:
            # 仅裁剪模式，执行严格CRS检查。失败会抛出异常。
            clip_only_tif(str(tif_path), shapes, shp_original_crs, reproj_target_crs, str(output_path), unified_nodata_value, all_touched)
            orig_size = tif_path.stat().st_size / (1024 * 1024)
            final_size = output_path.stat().st_size / (1024 * 1024)
            return True, f"成功(clip_only): {relative_path} ({orig_size:.2f} MB -> {final_size:.2f} MB)"
        except Exception as e:
            error_info = f"失败: {relative_path} - {str(e)}"
            return False, error_info

    # --- 主工作流：nodata兼容性检查与类型提升 ---
    try:
        with rasterio.open(tif_path) as src_ds:
            src_dtype = src_ds.profile['dtype']
            # 读取源文件真实的nodata值
            original_src_nodata = src_ds.nodata

            # 确定输出数据类型
            if not is_nodata_compatible(unified_nodata_value, src_dtype):
                output_dtype = 'float32'
                print(f"\n[注意] {tif_path.name} (dtype: {src_dtype}) 与 nodata 值 {unified_nodata_value} 不兼容，输出将转为 {output_dtype}。")
            else:
                output_dtype = src_dtype

    except Exception as e:
        # 如果文件无法打开或读取元数据失败，则使用安全模式继续
        print(f"[警告] 无法读取 {tif_path.name} 元数据 ({e})，将使用安全模式转换。")
        original_src_nodata = None # 源nodata未知
        output_dtype = 'float32'   # 使用安全的float类型

    try:
        # --- 标准重投影/重采样 -> 裁剪流程 ---
        # 确定用于重投影的目标CRS
        dst_crs = reproj_target_crs if reproj_target_crs else shp_original_crs
        
        if mode == 'memory':
            # --- 内存工作流 ---
            # --- 修改：传入黄金标准模板 ---
            memfile = reproject_to_memory(str(tif_path), dst_crs, master_transform, master_width, master_height, resampling_method, original_src_nodata, unified_nodata_value, output_dtype)
            if not memfile:
                raise RuntimeError("重投影到内存失败")
            with memfile.open() as reprojected_src:
                if not clip_from_dataset(reprojected_src, shapes, str(output_path), unified_nodata_value, all_touched):
                    raise RuntimeError("从内存裁剪失败")

            # 显式关闭并清理内存文件，确保子进程资源释放
            memfile.close()
            del memfile

        elif mode == 'disk':
            # --- 磁盘工作流 ---
            pid = os.getpid()
            tif_name = tif_path.name
            temp_path = temp_dir / f"temp_{pid}_{uuid.uuid4().hex}_{tif_name}"
            
            try:
                # --- 修改：传入黄金标准模板 ---
                if not reproject_to_disk(str(tif_path), str(temp_path), dst_crs, master_transform, master_width, master_height, resampling_method, original_src_nodata, unified_nodata_value, output_dtype):
                    raise RuntimeError("重投影到磁盘失败")
                if not clip_from_disk(str(temp_path), shapes, str(output_path), unified_nodata_value, all_touched):
                    raise RuntimeError("从磁盘裁剪失败")
            finally:
                # 确保每次任务的临时文件都被清理
                if temp_path.exists():
                    try:
                        temp_path.unlink()
                    except OSError:
                        pass # 忽略小概率的删除失败
        else:
            raise ValueError(f"未知的处理模式: {mode}")

        # 成功处理
        orig_size = tif_path.stat().st_size / (1024 * 1024)
        final_size = output_path.stat().st_size / (1024 * 1024)
        result_info = f"成功: {relative_path} (从 {orig_size:.2f} MB -> {final_size:.2f} MB)"
        
        return True, result_info

    except Exception as e:
        error_info = f"失败: {relative_path} - {str(e)}"
        return False, error_info

def main():
    """主函数：初始化并执行处理任务。"""
    start_time = time.time()

    # ---- 1. 直接从CONFIG加载配置 ----
    cfg = CONFIG
    
    # ---- 2. 校验和初始化 ----
    if cfg['resampling'] not in RESAMPLING_NAME_MAP or not hasattr(Resampling, cfg['resampling']):
        raise ValueError(f"未知或不受支持的重采样算法: {cfg['resampling']}")
    
    target_crs_str = cfg.get('target_crs')
    clip_only = cfg.get('clip_only', False)
    mode = cfg.get('processing_mode', 'memory')
    output_dir = Path(cfg['output_dir'])
    base_tif_dir = Path(cfg['tif_dir'])
    temp_dir = Path(cfg['temp_dir'])
    
    # ---- 路径安全校验 (关键!) ----
    resolved_output_dir = output_dir.resolve()
    resolved_tif_dir = base_tif_dir.resolve()
    if resolved_output_dir == resolved_tif_dir:
        raise ValueError(f"致命错误: 输出目录 '{resolved_output_dir}' 不能与输入目录 '{resolved_tif_dir}' 相同! 这会直接覆盖你的原始数据。")

    print("="*50 + f"\nGeoTIFF 一体化处理工具 ({mode} 模式)\n" + "="*50)

    output_dir.mkdir(parents=True, exist_ok=True)
    if mode == 'disk':
        temp_dir.mkdir(parents=True, exist_ok=True)
    
    try:
        # ---- 3. 准备数据和参数 ----
        shp, shp_original_crs = read_shapefile(cfg['shp_path'])
        
        shapes_for_clipping = [geom for geom in shp.geometry]
        reproj_target_crs = rasterio.crs.CRS.from_string(target_crs_str) if target_crs_str else None
        
        effective_target_crs = reproj_target_crs if reproj_target_crs else shp_original_crs
        # --- 新增: 创建黄金标准栅格模板 ---
        print("\n--- 创建黄金标准栅格模板 ---")
        # 确保用于计算模板的shp是目标CRS
        if effective_target_crs != shp_original_crs:
            shp_for_bounds = shp.to_crs(effective_target_crs)
        else:
            shp_for_bounds = shp

        # 使用整个shp的边界来计算一个统一的transform
        bounds = shp_for_bounds.total_bounds # (minx, miny, maxx, maxy)
        res_x, res_y = cfg['target_resolution']

        # 以左上角为原点创建 transform
        master_transform = from_origin(bounds[0], bounds[3], res_x, res_y)
        
        # 计算输出栅格的宽度和高度（向上取整以覆盖整个范围）
        master_width = math.ceil((bounds[2] - bounds[0]) / res_x)
        master_height = math.ceil((bounds[3] - bounds[1]) / res_y)
        
        print(f"模板计算完成: Width={master_width}, Height={master_height}")
        print(f"所有输出将强制对齐到此网格，确保空间一致性。")
        # --- 结束 ---

        if clip_only:
            print("\n工作模式: [仅裁剪]")
            if reproj_target_crs:
                print(f"  - 将验证输入数据的坐标系是否为: {reproj_target_crs.to_string()}")
            else:
                print(f"  - 将验证输入栅格与矢量的坐标系是否一致 ({shp_original_crs.to_string()})")
        else:
            print("工作模式: [重投影/重采样 -> 裁剪]")
            effective_target_crs = reproj_target_crs if reproj_target_crs else shp_original_crs
            print(f"  - 所有输出将被统一到坐标系: {effective_target_crs.to_string()}")

            if effective_target_crs != shp_original_crs:
                print("  - 正在转换矢量边界到目标坐标系...")
                shp = shp.to_crs(effective_target_crs)
                shapes_for_clipping = [geom for geom in shp.geometry]

        tif_files = find_tif_files(base_tif_dir)
        if not tif_files:
            print("在指定目录中未找到TIF文件。")
            return
        
        # CRS与分辨率匹配检查 (仅对第一个文件)
        if tif_files and not clip_only:
            with rasterio.open(tif_files[0]) as first_tif:
                src_crs_check = first_tif.crs
            check_crs_and_resolution(src_crs_check, effective_target_crs, cfg['target_resolution'])
        
        total_files = len(tif_files)
        print(f"\n找到 {total_files} 个TIF文件，开始处理...")
        
        # ---- 4. 确定统一的nodata值 ----
        batch_nodata_value = cfg.get('force_nodata')
        if batch_nodata_value is None:
            # force_nodata 未设置，从第一个文件自动检测
            try:
                with rasterio.open(tif_files[0]) as first_tif:
                    batch_nodata_value = first_tif.nodata
                if batch_nodata_value is None:
                    batch_nodata_value = -9999.0 # 如果第一个文件也没有，则使用默认值
                    print(f"自动检测: 第一个TIF文件无nodata值，统一使用默认值: {batch_nodata_value}")
                else:
                    print(f"自动检测: 将使用第一个TIF文件的nodata值作为统一值: {batch_nodata_value}")
            except Exception as e:
                batch_nodata_value = -9999.0
                print(f"自动检测: 读取第一个TIF文件失败({e})，统一使用默认值: {batch_nodata_value}")
        else:
            print(f"强制统一: 所有文件nodata值将被设为: {batch_nodata_value}")

        success_count = 0
        failed_files = []
        
        # --- 5. 执行处理 ---
        gdal_cache_max = 512 # MB
        with rasterio.Env(GDAL_CACHEMAX=gdal_cache_max):
            use_parallel = cfg.get('use_parallel', True)
            max_workers = cfg.get('max_workers') if cfg.get('max_workers') else min(32, multiprocessing.cpu_count() + 4)

            # 为每个文件准备独立的参数字典
            task_args = [{
                'tif_path': tif,
                'shapes': shapes_for_clipping,
                'shp_original_crs': shp_original_crs,
                'reproj_target_crs': reproj_target_crs,
                'base_tif_dir': base_tif_dir,
                'output_dir': output_dir,
                'resampling': cfg['resampling'],
                'mode': mode,
                'temp_dir': temp_dir,
                'dst_nodata': batch_nodata_value, # <--- 传入统一的nodata值
                'clip_only': clip_only,
                'all_touched': cfg['all_touched'],
                # --- 新增: 传递黄金标准模板 ---
                'master_transform': master_transform,
                'master_width': master_width,
                'master_height': master_height
                # --- 结束 ---
            } for tif in tif_files]

            if use_parallel:
                print(f"使用 {max_workers} 个工作进程进行并行处理 (GDAL缓存: {gdal_cache_max}MB)...")
                # 使用 as_completed 实时获取结果
                with concurrent.futures.ProcessPoolExecutor(max_workers=max_workers) as executor:
                    futures = {executor.submit(process_tif, arg): arg['tif_path'] for arg in task_args}
                    
                    with tqdm(total=len(futures), desc="处理进度", bar_format='{l_bar}{bar}| {n_fmt}/{total_fmt} [{elapsed}<{remaining}, {rate_fmt}{postfix}]') as pbar:
                        for future in concurrent.futures.as_completed(futures):
                            tif_path = futures[future]
                            try:
                                result, info = future.result()
                                if result:
                                    success_count += 1
                                    pbar.set_postfix_str(f"OK: {Path(tif_path).name}", refresh=True)
                                else:
                                    failed_files.append(info)
                                    print(f"\n[警告] {info}")
                            except Exception as exc:
                                relative_tif_path = Path(tif_path).relative_to(base_tif_dir)
                                failed_files.append(f"失败: {relative_tif_path} - {exc}")
                                print(f"\n[错误] 处理 {Path(tif_path).name} 时子进程崩溃: {exc}")
                            pbar.update(1)
            else:
                # 串行处理
                print("以串行模式处理 (禁用并行)...")
                with tqdm(total=total_files, desc="处理进度") as pbar:
                    for args in task_args:
                        result, info = process_tif(args)
                        if result:
                            success_count += 1
                            print(info)
                        else:
                            failed_files.append(info.replace("失败: ", "", 1))
                            print(f"[警告] {info}")
                        pbar.update(1)
            
            # --- 6. 输出处理摘要 ---
            elapsed_time = time.time() - start_time
            
            print("\n" + "="*50)
            print("处理摘要")
            print("="*50)
            
            print(f"处理完成! 成功处理 {success_count}/{total_files} 个文件。")
            print(f"总处理时间: {elapsed_time:.2f} 秒")

            summary_target_crs = reproj_target_crs if reproj_target_crs else shp_original_crs
            if not clip_only:
                print(f"目标坐标系: {summary_target_crs.to_string()}")
                print(f"目标分辨率: {cfg['target_resolution'][0]} x {cfg['target_resolution'][1]}")
                method_cn = RESAMPLING_NAME_MAP.get(cfg['resampling'], '未知方法')
                print(f"重采样算法: {cfg['resampling']} ({method_cn})")
            
            print(f"结果保存在: {output_dir.resolve()}")
            
            if failed_files:
                print(f"\n--- 失败的文件 ({len(failed_files)}) ---")
                for f in failed_files[:15]:
                    print(f" - {f}")
                if len(failed_files) > 15:
                    print(f" ... 以及其他 {len(failed_files) - 15} 个文件")
            
            # 清理空的临时目录
            if mode == 'disk' and temp_dir.exists():
                try:
                    if not any(temp_dir.iterdir()):
                        temp_dir.rmdir()
                        print(f"已清理并删除空的临时目录: {temp_dir}")
                except Exception as e:
                    print(f"清理临时目录时出错: {e}")
        
    except Exception as e:
        print(f"\n发生严重错误，程序终止: {e}")

if __name__ == "__main__":
    # Windows 平台在调用多进程时需要 freeze_support() 才能正常启动子进程，
    # 尤其在打包后的可执行文件或交互式解释器下更为重要。
    multiprocessing.freeze_support()
    main() 