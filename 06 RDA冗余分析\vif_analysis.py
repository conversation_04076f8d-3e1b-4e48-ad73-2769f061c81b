# -*- coding: utf-8 -*-
"""
变化量数据分组VIF分析脚本 - 专为RDA分析设计

本脚本提供一个专门针对变化量数据的分组VIF（方差膨胀因子）分析流程，用于RDA分析前的变量筛选。

主要功能包括：
1. 分组VIF分析：按变量类型（气候、地形、植被、人类活动、3D土壤、分类变量）分别进行VIF筛选
2. 智能数据处理：3D变量使用原始数据(124行)，2D变量使用聚合数据(31行)
3. 自定义变量命名：支持中文变量名和分组名显示，图表更美观
4. 科学筛选策略：使用标准VIF阈值(5)，不人为限制变量数量
5. 完整可视化：生成分组变量数量分布图、VIF值热图、变量总览图
6. 结果导出：输出包含筛选结果的Excel报告和高质量图表

适用场景：
- 土壤属性变化量的驱动力分析
- RDA分析前的环境变量筛选
- 多重共线性检测和处理
"""

import pandas as pd
from statsmodels.stats.outliers_influence import variance_inflation_factor
import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

# =============================================================================
# --- 1. 全局与绘图设置 ---
# =============================================================================
try:
    # 设置中文字体，以确保图表能正确显示中文标签
    plt.rcParams['font.sans-serif'] = ['SimHei']
    plt.rcParams['axes.unicode_minus'] = False
except Exception:
    print("\n警告: 未能设置中文字体 'SimHei'。图表中的中文可能无法正常显示。")

# =============================================================================
# --- 2. 用户核心配置区 ---
# =============================================================================

# --- 文件路径 ---
# 变化量数据文件路径（2023年-1980年的差值数据）
change_data_file_path = r"E:\05 Python\Devway\01 硕士论文\00 原始数据\嫩江_模型预测数据\嫩江_变化量.xlsx"

# --- 数据聚合配置 ---
# 是否将多层剖面数据按剖面ID聚合成单行记录
agg_mode = True
profile_id = 'ProfileID' # 用于聚合的唯一ID列

# --- VIF阈值 ---
# 使用学术界广泛接受的标准阈值
vif_threshold = 5

# --- 分组VIF分析参数 ---
# 是否启用分组分析模式
enable_group_analysis = True

# 注意：删除了变量数量限制，只使用VIF阈值进行筛选
# 这样更符合统计学原理，让数据特征自然决定最终变量集

# 传统单组分析的参数（备用）
min_sample_var_ratio = 3
max_variables = 31 // min_sample_var_ratio

# --- 输出目录 ---
# 所有分析结果（Excel报告和图片）将保存到此目录
output_dir = r"E:\05 Python\Devway\01 硕士论文\00 原始数据\嫩江_VIF_筛选变化量结果"

# --- 自定义命名 ---
# 分组中文名称映射
group_chinese_names = {
    'climate': '气候',
    'terrain': '地形',
    'vegetation': '植被',
    'human': '人类活动',
    'soil_3d': '3D土壤',
    'categorical': '分类变量'
}

# 为长变量名提供简洁的显示名称
custom_variable_names = {
    # 气候变量
    'Air_Temperature_2m_Mean': '气温',
    'LST': '地表温度',
    'Total_Precipitation_Mean': '降水量',
    'Total_Precipitation_Sum': '总降水',
    'Total_Evaporation_Mean': '蒸发量',
    'Surface_Net_Solar_Radiation_Mean': '净太阳辐射',
    'Surface_Net_Thermal_Radiation_Mean': '净热辐射',
    'Wind_U_Component_10m_Mean': '风速U分量',
    'Wind_V_Component_10m_Mean': '风速V分量',

    # 地形变量
    '250DEM': 'DEM高程',
    'Aspect': '坡向',
    'Slope': '坡度',
    'Plan_Curvature': '平面曲率',
    'Profile_Curvature': '剖面曲率',
    'Terrain_Ruggedness_Index': '地形粗糙度',
    'Topographic_Position_Index': '地形位置指数',
    'Topographic_Wetness_Index': '地形湿润指数',
    'Valley_Depth': '谷深',
    'Vertical_Distance_to_Channel_Network': '到河网距离',
    'Flow_Accumulation': '汇流累积量',
    'Flow_Direction': '流向',
    'Sink_Route': '汇流路径',

    # 植被变量
    'NDVI': '归一化植被指数',
    'EVI': '增强植被指数',
    'NDWI': '归一化水体指数',
    'MNDWI': '修正水体指数',
    'SAVI': '土壤调节植被指数',
    'RVI': '比值植被指数',
    'LAI': '叶面积指数',
    'FAPAR': '光合有效辐射',
    'FVC': '植被覆盖度',
    'NPP': '净初级生产力',
    'GPP': '总初级生产力',
    'BSI': '裸土指数',
    'IBI': '不透水面指数',
    'Coloration_Index': '色彩指数',
    'Redness_Index': '红度指数',
    'Saturation_Index': '饱和度指数',

    # 人类活动变量
    'Population': '人口密度',
    'GDP': '国内生产总值',
    'NTL': '夜间灯光',
    'Built_Up': '建设用地',
    'CLCD': '土地覆盖',

    # 3D土壤变量
    'bdod': '土壤容重',
    'cec': '阳离子交换量',
    'Soil_Temperature_Mean': '土壤温度',
    'Soil_Water_Content_Mean': '土壤含水量',

    # 分类变量
    'landform': '地貌类型',
    'lithology': '岩性类型',
    'soiltype': '土壤类型'
}

# --- 变量角色定义 ---

# 基础排除变量（所有组都排除）
base_exclude_cols = [
    # 基础信息
    'ProfileID', 'Longitude', 'Latitude', 'City', 'Location',
    'Soilclass-1980', 'Soilclass-2023', 'LandUse-1980', 'LandUse-2023', 'year',
    # 剖面深度信息
    '深度范围', '深度中点',
    # 因变量 (Y) - 土壤属性变化量
    '△pH', '△SOM', '△TN', '△TP', '△物理粘粒', '△物理砂粒'
]

# 变量分组定义
variable_groups = {
    'climate': [
        # 气候相关变量
        'Air_Temperature_2m_Mean', 'LST', 'Total_Precipitation_Mean', 'Total_Precipitation_Sum',
        'Total_Evaporation_Mean', 'Surface_Net_Solar_Radiation_Mean', 'Surface_Net_Thermal_Radiation_Mean',
        'Wind_U_Component_10m_Mean', 'Wind_V_Component_10m_Mean'
    ],
    'terrain': [
        # 地形相关变量
        '250DEM', 'Aspect', 'Slope', 'Plan_Curvature', 'Profile_Curvature',
        'Terrain_Ruggedness_Index', 'Topographic_Position_Index', 'Topographic_Wetness_Index',
        'Valley_Depth', 'Vertical_Distance_to_Channel_Network', 'Flow_Accumulation',
        'Flow_Direction', 'Sink_Route'
    ],
    'vegetation': [
        # 植被相关变量
        'NDVI', 'EVI', 'NDWI', 'MNDWI', 'SAVI', 'RVI', 'LAI', 'FAPAR', 'FVC',
        'NPP', 'GPP', 'BSI', 'IBI', 'Coloration_Index', 'Redness_Index', 'Saturation_Index'
    ],
    'human': [
        # 人类活动变量
        'Population', 'GDP', 'NTL', 'Built_Up', 'CLCD'
    ],
    'soil_3d': [
        # 3D土壤变量（不聚合）
        'bdod', 'cec', 'Soil_Temperature_Mean', 'Soil_Water_Content_Mean'
    ],
    'categorical': [
        # 分类变量
        'landform', 'lithology', 'soiltype'
    ]
}

def group_vif_analysis(orig_df, agg_df):
    """分组VIF分析：对不同类型的变量分别进行VIF筛选"""
    print("\n" + "="*80)
    print("开始分组VIF分析")
    print("="*80)

    final_selected_vars = {}
    all_results = {}

    for group_name, var_list in variable_groups.items():
        print(f"\n{'='*60}")
        print(f"分析变量组: {group_name.upper()}")
        print(f"{'='*60}")

        # 选择数据集：3D变量使用原始数据，其他使用聚合数据
        if group_name == 'soil_3d':
            df_to_use = orig_df
            print(f"使用原始数据 (124行) - 保持深度维度")
        else:
            df_to_use = agg_df
            print(f"使用聚合数据 (31行) - 按剖面聚合")

        # 筛选当前组的可用变量
        available_vars = [var for var in var_list if var in df_to_use.columns]
        if not available_vars:
            print(f"警告: {group_name}组没有可用变量")
            continue

        print(f"可用变量: {available_vars}")
        print(f"变量数量: {len(available_vars)}")

        # 进行VIF分析（不限制变量数量）
        df_group = df_to_use[available_vars].copy()

        # 处理缺失值和常量列
        const_cols = [col for col in df_group.columns if df_group[col].nunique(dropna=False) <= 1]
        if const_cols:
            print(f"移除常量列: {const_cols}")
            df_group.drop(columns=const_cols, inplace=True)

        if df_group.shape[1] < 2:
            print(f"有效变量少于2个，跳过VIF分析")
            final_selected_vars[group_name] = available_vars
            continue

        # 处理缺失值
        df_group.fillna(df_group.mean(), inplace=True)

        # 执行VIF分析（只使用VIF阈值，不限制数量）
        try:
            _, selected_vars, final_vif, vif_history = calc_vif_for_group(
                df_group, vif_threshold, f"{group_name}组"
            )
            final_selected_vars[group_name] = selected_vars
            all_results[group_name] = {
                'final_vif': final_vif,
                'history': vif_history,
                'data_shape': df_group.shape
            }
        except Exception as e:
            print(f"VIF分析失败: {e}")
            # 如果VIF分析失败，保留所有变量
            final_selected_vars[group_name] = available_vars
            continue

    return final_selected_vars, all_results

def create_group_vif_plots(selected_vars, group_results, output_dir):
    """生成分组VIF分析的可视化图表"""
    print("\n--- 正在生成分组VIF可视化图表 ---")

    # 1. 各组变量数量条形图
    plt.figure(figsize=(12, 8))
    groups = list(selected_vars.keys())
    # 使用中文分组名称
    chinese_groups = [group_chinese_names.get(group, group) for group in groups]
    counts = [len(vars_list) for vars_list in selected_vars.values()]

    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD']
    bars = plt.bar(chinese_groups, counts, color=colors[:len(groups)])

    plt.title('各组筛选变量数量分布', fontsize=20, pad=20)
    plt.xlabel('变量组', fontsize=16)
    plt.ylabel('变量数量', fontsize=16)
    plt.xticks(rotation=45, fontsize=14)
    plt.yticks(fontsize=14)

    # 在柱子上添加数值标签
    for bar, count in zip(bars, counts):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1,
                str(count), ha='center', va='bottom', fontsize=14, fontweight='bold')

    plt.grid(axis='y', alpha=0.3)
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, "分组变量数量分布图.png"), dpi=500, bbox_inches='tight')
    plt.close()
    print(f"图表已保存: 分组变量数量分布图.png")

    # 2. 各组最终VIF值热图
    plt.figure(figsize=(16, 10))

    # 收集所有变量的VIF值
    all_vif_data = []
    for group_name, result in group_results.items():
        if 'final_vif' in result and not result['final_vif'].empty:
            vif_df = result['final_vif'].copy()
            vif_df['group'] = group_name
            vif_df['display_name'] = vif_df['feature'].map(
                lambda x: custom_variable_names.get(x, x)
            )
            all_vif_data.append(vif_df)

    if all_vif_data:
        combined_vif = pd.concat(all_vif_data, ignore_index=True)

        # 添加中文分组名称
        combined_vif['chinese_group'] = combined_vif['group'].map(
            lambda x: group_chinese_names.get(x, x)
        )

        # 创建透视表用于热图
        pivot_data = combined_vif.pivot_table(
            index='display_name',
            columns='chinese_group',
            values='VIF',
            fill_value=0
        )

        # 创建热图
        plt.figure(figsize=(12, max(8, len(pivot_data) * 0.4)))
        sns.heatmap(pivot_data, annot=True, fmt='.2f', cmap='RdYlBu_r',
                   center=2.5, cbar_kws={'label': 'VIF值'})

        plt.title('各组变量最终VIF值热图', fontsize=18, pad=20)
        plt.xlabel('变量组', fontsize=14)
        plt.ylabel('变量名称', fontsize=14)
        plt.xticks(rotation=45, fontsize=12)
        plt.yticks(rotation=0, fontsize=10)

        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, "分组VIF值热图.png"), dpi=500, bbox_inches='tight')
        plt.close()
        print(f"图表已保存: 分组VIF值热图.png")

    # 3. 最终筛选变量总览图
    plt.figure(figsize=(16, 12))

    # 准备数据
    all_vars = []
    all_groups = []
    all_vifs = []

    for group_name, vars_list in selected_vars.items():
        for var in vars_list:
            all_vars.append(custom_variable_names.get(var, var))
            all_groups.append(group_name)

            # 获取VIF值
            vif_val = 1.0  # 默认值
            if group_name in group_results and 'final_vif' in group_results[group_name]:
                vif_df = group_results[group_name]['final_vif']
                if not vif_df.empty:
                    vif_row = vif_df[vif_df['feature'] == var]
                    if not vif_row.empty:
                        vif_val = vif_row['VIF'].iloc[0]
            all_vifs.append(vif_val)

    # 创建DataFrame
    plot_df = pd.DataFrame({
        'variable': all_vars,
        'group': all_groups,
        'vif': all_vifs
    })

    # 按组和VIF值排序
    plot_df = plot_df.sort_values(['group', 'vif'])

    # 创建颜色映射（使用中文分组名）
    unique_groups = plot_df['group'].unique()
    chinese_unique_groups = [group_chinese_names.get(group, group) for group in unique_groups]
    group_colors = dict(zip(chinese_unique_groups, colors[:len(unique_groups)]))
    plot_df['chinese_group'] = plot_df['group'].map(lambda x: group_chinese_names.get(x, x))
    plot_df['color'] = plot_df['chinese_group'].map(group_colors)

    # 绘制条形图
    bars = plt.barh(range(len(plot_df)), plot_df['vif'], color=plot_df['color'])

    plt.yticks(range(len(plot_df)), plot_df['variable'], fontsize=10)
    plt.xlabel('VIF值', fontsize=14)
    plt.title('最终筛选变量VIF值总览', fontsize=18, pad=20)

    # 添加VIF阈值线
    plt.axvline(x=vif_threshold, color='red', linestyle='--', linewidth=2,
               label=f'VIF阈值 = {vif_threshold}')

    # 添加图例（使用中文分组名）
    legend_elements = [plt.Rectangle((0,0),1,1, facecolor=group_colors[chinese_group],
                                   label=chinese_group) for chinese_group in chinese_unique_groups]
    legend_elements.append(plt.Line2D([0], [0], color='red', linestyle='--',
                                    label=f'VIF阈值 = {vif_threshold}'))

    # 调整图例位置，避免重叠
    plt.legend(handles=legend_elements, loc='center left', bbox_to_anchor=(1.02, 0.5),
              fontsize=11, frameon=True, fancybox=True, shadow=True,
              title='变量分组', title_fontsize=12)

    plt.grid(axis='x', alpha=0.3)
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, "最终筛选变量VIF总览图.png"), dpi=500,
               bbox_inches='tight', pad_inches=0.2)
    plt.close()
    print(f"图表已保存: 最终筛选变量VIF总览图.png")

def calc_vif_for_group(df_vars, threshold, group_label):
    """为特定变量组执行VIF分析（只使用VIF阈值，不限制变量数量）"""
    print(f"\n开始对{group_label}进行VIF分析...")
    print(f"样本数量: {len(df_vars)}")
    print(f"初始变量数量: {df_vars.shape[1]}")
    print(f"VIF阈值: {threshold}")

    vars_df = df_vars.copy()
    vif_history = []
    iter_num = 0

    while True:
        iter_num += 1
        var_count = vars_df.shape[1]

        # 计算VIF
        vif_data = pd.DataFrame()
        vif_data["feature"] = vars_df.columns
        try:
            vif_data["VIF"] = [variance_inflation_factor(vars_df.values, i) for i in range(var_count)]
        except Exception as e:
            print(f"VIF计算失败: {e}")
            final_vars = vars_df.columns.tolist()
            break

        vif_data = vif_data.sort_values(by='VIF', ascending=False).reset_index(drop=True)
        max_vif = vif_data['VIF'].iloc[0]
        max_var = vif_data['feature'].iloc[0]

        print(f"轮次 {iter_num} | 变量数: {var_count} | 最大VIF: {max_vif:.4f} (在 '{max_var}' 上)")

        history_entry = {'iteration': iter_num, 'max_vif': max_vif, 'removed_variable': max_var if max_vif >= threshold else 'None'}
        vif_history.append(history_entry)

        # 检查是否满足VIF阈值
        if max_vif < threshold:
            print(f"\n✓ VIF条件满足！所有剩余变量的VIF值均低于 {threshold}")
            final_vars = vars_df.columns.tolist()
            break

        # 移除VIF最大的变量
        vars_df = vars_df.drop(columns=[max_var])

        if vars_df.shape[1] < 2:
            print("变量数量少于2，停止迭代")
            final_vars = vars_df.columns.tolist()
            break

    # 计算最终VIF
    if len(final_vars) >= 2:
        final_vif_data = pd.DataFrame()
        final_vif_data["feature"] = final_vars
        try:
            final_vif_data["VIF"] = [variance_inflation_factor(vars_df.values, i) for i in range(len(final_vars))]
            final_vif_data = final_vif_data.sort_values(by='VIF').reset_index(drop=True)
        except:
            final_vif_data = pd.DataFrame({'feature': final_vars, 'VIF': [1.0] * len(final_vars)})
    else:
        final_vif_data = pd.DataFrame({'feature': final_vars, 'VIF': [1.0] * len(final_vars)})

    print(f"最终选择的变量: {final_vars}")
    if len(final_vars) > 0:
        print(f"最大VIF值: {final_vif_data['VIF'].max():.4f}")

    history_df = pd.DataFrame(vif_history)
    return vars_df, final_vars, final_vif_data, history_df

# =============================================================================
# --- 3. 核心功能函数 ---
# =============================================================================

def aggregate_data(df, id_col):
    """将多层剖面数据聚合为单行记录"""
    if not agg_mode:
        print("配置为跳过剖面数据聚合步骤。")
        return df

    if id_col not in df.columns:
        print(f"错误: 聚合ID列 '{id_col}' 不在数据中。", file=sys.stderr)
        return None

    group_keys = [id_col]
    if 'year' in df.columns:
        group_keys.append('year')
    else:
        print(f"警告: 未检测到 'year' 列。将仅按 '{id_col}' 进行聚合。")

    print(f"正在根据 {group_keys} 列对剖面数据进行聚合...")

    num_cols = df.select_dtypes(include=['number']).columns
    text_cols = df.select_dtypes(exclude=['number']).columns

    agg_funcs = {col: 'mean' for col in num_cols}
    agg_funcs.update({col: 'first' for col in text_cols})

    for key in group_keys:
        if key in agg_funcs:
            agg_funcs[key] = 'first'

    agg_df = df.groupby(group_keys, as_index=False).agg(agg_funcs)
    print(f"聚合完成。数据从 {len(df)} 行减少到 {len(agg_df)} 行。")
    return agg_df



# 已删除无用的 create_vif_plots 函数

def create_vif_plots_DELETED(final_vif, vif_history, year_label, output_dir, threshold):
    """生成并保存VIF分析过程的两张核心可视化图表。"""
    print("\n--- 正在生成可视化图表 ---")

    # 图1: 最终入选变量的VIF值条形图-升序
    plt.figure(figsize=(12, 8))
    final_vif_sorted = final_vif.sort_values(by='VIF', ascending=True).copy()

    # 应用自定义变量名
    final_vif_sorted['display_name'] = final_vif_sorted['feature'].map(
        lambda x: custom_variable_names.get(x, x)
    )

    sns.barplot(x='VIF', y='display_name', data=final_vif_sorted, hue='display_name', palette='viridis', legend=False)
    plt.axvline(x=threshold, color='r', linestyle='--', label=f'VIF阈值 = {threshold}')
    plt.title(f'VIF值条形图', fontsize=24)
    plt.xlabel('方差膨胀因子 (VIF)', fontsize=16)
    plt.ylabel('环境变量', fontsize=16)
    plt.tick_params(axis='both', which='major', labelsize=16)  # 设置刻度字体大小
    plt.legend(fontsize=16)  # 设置图例字体大小
    plt.tight_layout()
    plot1_path = os.path.join(output_dir, f"最终入选变量的VIF值条形图.png")
    try:
        plt.savefig(plot1_path, dpi=500)
        print(f"图表已保存: {plot1_path}")
    except Exception as e:
        print(f"保存图表1时出错: {e}", file=sys.stderr)
    plt.close()

    # 图2: VIF迭代剔除过程图 
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(16, 12), height_ratios=[3, 1])

    # 主图：VIF变化曲线
    ax1.plot(vif_history['iteration'], vif_history['max_vif'], marker='o', linestyle='-',
             color='b', linewidth=2, markersize=8, label='每轮最大VIF值')
    ax1.axhline(y=threshold, color='r', linestyle='--', linewidth=2, label=f'VIF阈值 = {threshold}')
    ax1.set_yscale('log')
    ax1.set_title('VIF迭代剔除过程', fontsize=20, pad=20)
    ax1.set_ylabel('最大VIF值 (对数尺度)', fontsize=16)
    ax1.tick_params(axis='both', which='major', labelsize=14)
    ax1.grid(True, linestyle='--', alpha=0.6)
    ax1.legend(fontsize=14, loc='upper right')

    # 下方表格：显示剔除的变量
    removed_vars = []
    iterations = []
    for i, row in vif_history.iterrows():
        if row['removed_variable'] != 'None':
            removed_vars.append(row['removed_variable'])
            iterations.append(row['iteration'])

    if removed_vars:
        # 创建表格数据 - 按列分组显示
        rows_per_col = 12  # 每列显示12行
        num_cols = (len(removed_vars) + rows_per_col - 1) // rows_per_col  # 计算需要的列数

        # 创建列标题
        col_labels = []
        for col in range(num_cols):
            start_idx = col * rows_per_col
            end_idx = min(start_idx + rows_per_col, len(removed_vars))
            if start_idx < len(removed_vars):
                col_labels.append(f"剔除变量 ({start_idx+1}-{end_idx})")

        # 创建表格数据
        table_data = []
        for row in range(rows_per_col):
            row_data = []
            for col in range(num_cols):
                idx = col * rows_per_col + row
                if idx < len(removed_vars):
                    row_data.append(f"第{iterations[idx]}轮: {removed_vars[idx]}")
                else:
                    row_data.append('')  # 空单元格
            table_data.append(row_data)

        # 在下方子图中显示表格
        ax2.axis('off')
        table = ax2.table(cellText=table_data,
                         colLabels=col_labels,
                         cellLoc='left',  # 左对齐，便于阅读长变量名
                         loc='center',
                         colWidths=[1.0/len(col_labels)] * len(col_labels))
        table.auto_set_font_size(False)
        table.set_fontsize(14)  # 适中的字体大小
        table.scale(1, 2.5)  # 调整表格高度

        # 设置表格样式
        for i in range(len(table_data) + 1):
            for j in range(len(col_labels)):
                cell = table[(i, j)]
                if i == 0:  # 表头
                    cell.set_facecolor('#4CAF50')
                    cell.set_text_props(weight='bold', color='white')
                else:  # 数据行
                    if table_data[i-1][j]:  # 非空单元格
                        cell.set_facecolor('#f0f0f0' if i % 2 == 0 else 'white')
                    else:  # 空单元格
                        cell.set_facecolor('white')
    else:
        ax2.axis('off')
        ax2.text(0.5, 0.5, '无变量被剔除', ha='center', va='center', fontsize=16)

    ax1.set_xlabel('迭代轮次', fontsize=16)
    plt.tight_layout()
    plot2_path = os.path.join(output_dir, f"VIF迭代剔除过程图.png")
    try:
        plt.savefig(plot2_path, dpi=500)
        print(f"图表已保存: {plot2_path}")
    except Exception as e:
        print(f"保存图表2时出错: {e}", file=sys.stderr)
    plt.close()

def process_dataset(file_path, year_label):
    """封装了从数据加载到结果保存的完整VIF分析流程。"""
    print("\n" + "#"*80)
    print(f"# 开始处理: {year_label} ({os.path.basename(file_path)})")
    print("#"*80 + "\n")
    try:
        orig_df = pd.read_excel(file_path)  # 变化量数据通常在第一个sheet
        print(f"成功加载数据: {orig_df.shape[0]} 行, {orig_df.shape[1]} 列。")
        print(f"唯一剖面数量: {orig_df['ProfileID'].nunique()}")
    except FileNotFoundError:
        print(f"错误：文件未找到于 '{file_path}'。", file=sys.stderr)
        return
    except Exception as e:
        print(f"读取Excel文件时出错: {e}", file=sys.stderr)
        return

    # 聚合数据（用于2D变量）
    if agg_mode:
        agg_df = aggregate_data(orig_df.copy(), profile_id)
        if agg_df is None:
            print(f"错误：聚合失败。")
            return
    else:
        agg_df = orig_df.copy()

    # 选择分析模式
    if enable_group_analysis:
        # 分组VIF分析
        final_selected_vars, group_results = group_vif_analysis(orig_df, agg_df)

        # 汇总结果
        print("\n" + "="*80)
        print("分组VIF分析结果汇总")
        print("="*80)

        all_selected_vars = []
        for group_name, vars_list in final_selected_vars.items():
            print(f"\n{group_name.upper()}组 ({len(vars_list)}个变量): {vars_list}")
            all_selected_vars.extend(vars_list)

        print(f"\n总计选择变量数量: {len(all_selected_vars)}")
        print(f"所有选择的变量: {all_selected_vars}")

        # 保存结果
        save_group_results(orig_df, agg_df, final_selected_vars, group_results, year_label)

    else:
        # 传统单组VIF分析已被移除，只使用分组分析
        print("注意：传统VIF分析已被移除，请启用分组分析模式")

def save_group_results(orig_df, agg_df, selected_vars, group_results, year_label):
    """保存分组VIF分析结果"""
    os.makedirs(output_dir, exist_ok=True)

    # 构建最终输出数据
    all_vars = []
    for group_vars in selected_vars.values():
        all_vars.extend(group_vars)

    # 基础信息列
    base_cols = [col for col in base_exclude_cols if col in orig_df.columns]
    all_cols_to_keep = base_cols + all_vars

    # 确保所有列都存在
    available_cols = [col for col in all_cols_to_keep if col in orig_df.columns]
    output_df = orig_df[available_cols]

    print(f"\n正在保存结果...")
    print(f"输出数据维度: {output_df.shape[0]} 行, {output_df.shape[1]} 列")

    # 保存到Excel
    output_filename = os.path.join(output_dir, f"分组VIF_筛选结果.xlsx")
    try:
        with pd.ExcelWriter(output_filename, engine='openpyxl') as writer:
            # 主数据表
            output_df.to_excel(writer, sheet_name='筛选后数据', index=False)

            # 各组结果汇总（包含中文名称）
            summary_data = []
            for group_name, vars_list in selected_vars.items():
                chinese_group_name = group_chinese_names.get(group_name, group_name)
                for var in vars_list:
                    chinese_var_name = custom_variable_names.get(var, var)
                    summary_data.append({
                        '变量组(英文)': group_name,
                        '变量组(中文)': chinese_group_name,
                        '变量名(英文)': var,
                        '变量名(中文)': chinese_var_name,
                        '变量类型': '3D变量' if group_name == 'soil_3d' else '2D变量'
                    })

            summary_df = pd.DataFrame(summary_data)
            summary_df.to_excel(writer, sheet_name='变量分组汇总', index=False)

            # 各组VIF详情（添加中文名称）
            for group_name, result in group_results.items():
                if 'final_vif' in result and not result['final_vif'].empty:
                    vif_detail = result['final_vif'].copy()
                    # 添加中文变量名列
                    vif_detail['变量名(中文)'] = vif_detail['feature'].map(
                        lambda x: custom_variable_names.get(x, x)
                    )
                    # 重新排列列的顺序
                    vif_detail = vif_detail[['feature', '变量名(中文)', 'VIF']]
                    vif_detail.columns = ['变量名(英文)', '变量名(中文)', 'VIF值']

                    chinese_group_name = group_chinese_names.get(group_name, group_name)
                    sheet_name = f'{chinese_group_name}_VIF'
                    vif_detail.to_excel(writer, sheet_name=sheet_name, index=False)

        print(f"结果已保存至: {output_filename}")

        # 生成可视化图表
        create_group_vif_plots(selected_vars, group_results, output_dir)

    except Exception as e:
        print(f"保存结果时出错: {e}", file=sys.stderr)





# =============================================================================
# --- 4. 主执行逻辑 ---
# =============================================================================
if __name__ == "__main__":
    print("变化量数据VIF分析脚本启动...")
    print(f"数据文件: {change_data_file_path}")
    print(f"结果将保存至目录: '{os.path.abspath(output_dir)}'")

    if enable_group_analysis:
        print(f"\n分组VIF分析设置:")
        print(f"  - VIF阈值: {vif_threshold}")
        print(f"  - 变量筛选策略: 只使用VIF阈值，不限制变量数量")
        print(f"  - 3D变量使用原始数据(124行)")
        print(f"  - 2D变量使用聚合数据(31行)")
    else:
        print(f"传统VIF分析设置:")
        print(f"  - VIF阈值: {vif_threshold}")
        print(f"  - 最小样本/变量比例: {min_sample_var_ratio}:1")
        print(f"  - 最大变量数量: {max_variables}")

    print("\n" + "="*80)
    print("开始变化量数据VIF分析")
    print("="*80)
    process_dataset(change_data_file_path, "变化量数据")
    print("\n脚本执行完毕。")



