# =============================================================================
# 论文级高质量可视化函数
# =============================================================================

# 定义配色方案 (采用现代、专业的配色)
rda_colors <- list(
  # 基础配色
  primary = "#3498db",       # 鲜明蓝色
  secondary = "#9b59b6",     # 优雅紫色
  accent = "#f39c12",        # 明亮橙色
  warning = "#e74c3c",       # 鲜明红色
  success = "#2ecc71",       # 清新绿色
  neutral = "#34495e",       # 深蓝灰色
  
  # 渐变色和分组色
  gradient = c("#1a5276", "#2874a6", "#3498db", "#5dade2", "#85c1e9", "#aed6f1"),
  soil_vars = c("#a04000", "#d35400", "#e67e22", "#f39c12", "#f8c471"),  # 土壤变量(棕色系)
  env_vars = c("#1e8449", "#27ae60", "#2ecc71", "#58d68d", "#7dcea0"),   # 环境变量(绿色系)
  
  # 不同响应变量的专属颜色
  response = c(
    "△pH" = "#e74c3c",
    "△SOM" = "#2ecc71",
    "△TN" = "#3498db",
    "△TP" = "#9b59b6",
    "△物理粘粒" = "#f39c12"
  ),
  
  # 透明度设置
  alpha = list(
    points = 0.7,
    arrows = 0.9,
    text = 1.0
  )
)

# 现代化主题设置
theme_rda <- function() {
  theme_minimal() +
  theme(
    # 整体文字与边框
    text = element_text(family = "sans", color = "#2c3e50"),
    plot.title = element_text(size = 16, face = "bold", hjust = 0.5, margin = margin(b = 15)),
    plot.subtitle = element_text(size = 12, hjust = 0.5, margin = margin(b = 10)),
    
    # 坐标轴设置
    axis.title = element_text(size = 13, face = "bold", margin = margin(t = 10, b = 10)),
    axis.text = element_text(size = 11, color = "#34495e"),
    axis.ticks = element_line(color = "#bdc3c7"),
    axis.line = element_line(color = "#bdc3c7", linewidth = 0.5),
    
    # 网格线设置
    panel.grid.major = element_line(color = "#ecf0f1", linewidth = 0.3),
    panel.grid.minor = element_line(color = "#f7f9f9", linewidth = 0.15),
    
    # 图例设置
    legend.title = element_text(size = 12, face = "bold"),
    legend.text = element_text(size = 10),
    legend.background = element_rect(fill = "white", color = NA),
    legend.key = element_rect(fill = NA, color = NA),
    
    # 边距
    plot.margin = margin(t = 20, r = 20, b = 20, l = 20)
  )
}

# 变量分类函数
classify_variables <- function(var_names) {

  # 静态地形变量
  static_vars <- c("Plan_Curvature", "Profile_Curvature", "Terrain_Ruggedness_Index",
                   "Topographic_Position_Index", "Vertical_Distance_to_Channel_Network",
                   "Flow_Accumulation", "Sink_Route")

  # 动态环境变量
  dynamic_vars <- c("LST", "Total_Precipitation_Mean", "Total_Evaporation_Mean",
                    "MNDWI", "RVI", "LAI", "FAPAR", "FVC", "NPP", "Coloration_Index",
                    "GDP", "NTL", "Built_Up", "CLCD", "bdod", "Soil_Temperature_Mean",
                    "Soil_Water_Content_Mean")

  # 分类结果
  classification <- list(
    static = intersect(var_names, static_vars),
    dynamic = intersect(var_names, dynamic_vars),
    other = setdiff(var_names, c(static_vars, dynamic_vars))
  )

  # 输出分类信息
  cat("=== 变量分类 ===\n")
  cat("📊 静态协变量(", length(classification$static), "个):", paste(classification$static, collapse = ", "), "\n")
  cat("📈 动态协变量(", length(classification$dynamic), "个):", paste(classification$dynamic, collapse = ", "), "\n")
  if(length(classification$other) > 0) {
    cat("❓ 未分类变量:", paste(classification$other, collapse = ", "), "\n")
  }

  return(classification)
}

# 创建论文级RDA双序图（使用ggplot2 + ggrepel避免标签重叠）
create_rda_biplot <- function(rda_result, title = "RDA双序图", output_path) {

  if(is.null(rda_result) || length(rda_result$rda_model$CCA$eig) == 0) {
    cat("⚠️ RDA结果无效，跳过双序图绘制\n")
    return(NULL)
  }

  # 计算轴解释度
  eigenvals <- eigenvals(rda_result$rda_model)
  total_var <- sum(eigenvals)
  if(length(eigenvals) >= 2) {
    axis1_var <- round(eigenvals[1] / total_var * 100, 2)
    axis2_var <- round(eigenvals[2] / total_var * 100, 2)
  } else {
    axis1_var <- round(eigenvals[1] / total_var * 100, 2)
    axis2_var <- 0
  }

  # 提取样本得分
  site_scores <- scores(rda_result$rda_model, display = "sites", choices = 1:2)
  site_tbl <- as_tibble(site_scores) %>%
    mutate(label = rownames(site_scores), type = "sites")

  # 提取响应变量得分
  species_scores <- scores(rda_result$rda_model, display = "species", choices = 1:2)
  species_tbl <- as_tibble(species_scores) %>%
    mutate(label = rownames(species_scores), type = "species")

  # 处理环境变量
  env_tbl <- tibble()
  if(!is.null(rda_result$explanatory_data)) {
    tryCatch({
      env_fit <- envfit(rda_result$rda_model, rda_result$explanatory_data, permutations = 999)

      # 选择重要变量（降低阈值以显示更多变量）
      var_importance <- data.frame(
        Variable = names(env_fit$vectors$r),
        R_squared = env_fit$vectors$r,
        P_value = env_fit$vectors$pvals
      )
      var_importance <- var_importance[order(var_importance$R_squared, decreasing = TRUE), ]

      # 选择前8个最重要的变量（避免过度拥挤）
      top_vars <- head(var_importance, 8)

      if(nrow(top_vars) > 0) {
        env_scores <- scores(env_fit, display = "vectors")[top_vars$Variable, , drop = FALSE]
        # 缩放环境变量箭头以适应图表
        scaling_factor <- 0.8
        env_scores <- env_scores * scaling_factor

        env_tbl <- as_tibble(env_scores) %>%
          mutate(label = rownames(env_scores), type = "env")
      }

      cat("   显示", nrow(top_vars), "个重要环境变量\n")
    }, error = function(e) {
      cat("   ⚠️ 环境变量处理失败\n")
    })
  }

  # 创建ggplot图表
  p <- ggplot() +
    # 背景和网格
    theme_rda() +
    
    # 添加辅助网格线
    geom_hline(yintercept = 0, linetype = "dashed", color = "#bdc3c7", alpha = 0.8) +
    geom_vline(xintercept = 0, linetype = "dashed", color = "#bdc3c7", alpha = 0.8) +

    # 添加样本点（小而半透明）
    geom_point(data = site_tbl,
               aes(x = RDA1, y = RDA2),
               color = rda_colors$primary, alpha = rda_colors$alpha$points, 
               size = 2, shape = 21, fill = "white", stroke = 0.5) +
    
    # 为样本添加椭圆聚类区域（95%置信区间）
    stat_ellipse(data = site_tbl, aes(x = RDA1, y = RDA2), 
                 type = "norm", level = 0.95, 
                 color = rda_colors$primary, linetype = 2, alpha = 0.3) +

    # 添加响应变量箭头（更鲜明的红色，粗）
    geom_segment(data = species_tbl,
                 aes(x = 0, y = 0, xend = RDA1, yend = RDA2),
                 arrow = arrow(length = unit(0.35, "cm"), type = "closed", ends = "last"),
                 color = rda_colors$warning, linewidth = 1.8, alpha = 0.9) +

    # 添加环境变量箭头（蓝色）
    {if(nrow(env_tbl) > 0) {
      geom_segment(data = env_tbl,
                   aes(x = 0, y = 0, xend = RDA1, yend = RDA2),
                   arrow = arrow(length = unit(0.25, "cm"), type = "closed"),
                   color = rda_colors$primary, linewidth = 0.8, alpha = 0.8)
    }} +

    # 使用ggrepel智能放置响应变量标签
    geom_text_repel(data = species_tbl,
                    aes(x = RDA1, y = RDA2, label = label),
                    color = rda_colors$warning, size = 5, fontface = "bold",
                    seed = 123, max.overlaps = 20,
                    box.padding = 1, point.padding = 0.5,
                    min.segment.length = 0) +

    # 使用ggrepel智能放置环境变量标签
    {if(nrow(env_tbl) > 0) {
      geom_text_repel(data = env_tbl,
                      aes(x = RDA1, y = RDA2, label = label),
                      color = rda_colors$primary, size = 4, fontface = "bold",
                      seed = 456, max.overlaps = 20,
                      box.padding = 0.7, point.padding = 0.5,
                      min.segment.length = 0.2)
    }} +

    # 设置坐标轴标签和标题
    labs(
      title = title,
      subtitle = paste0("总解释度: ", round(rda_result$explained_variance, 2), "%"),
      x = paste0("RDA1 (", axis1_var, "%)"),
         y = paste0("RDA2 (", axis2_var, "%)"),
      caption = paste0("样本数: ", nrow(site_tbl))
    ) +

    # 固定坐标比例
    coord_fixed() +

    # 微调图表边距
    theme(
      plot.subtitle = element_text(size = 12, color = "#7f8c8d"),
      plot.caption = element_text(size = 9, color = "#7f8c8d", hjust = 1)
    )
    
  # 设置高质量输出参数
  ggsave(output_path, p, width = 12, height = 10, dpi = 300,
         bg = "white", device = cairo_pdf)
  
  # 同时保存一个PNG版本
  png_path <- gsub("\\.pdf$", ".png", output_path)
  if(png_path == output_path) {
    # 如果不是PDF，保持原样
  ggsave(output_path, p, width = 12, height = 10, dpi = 300)
  } else {
    # 额外保存一个PNG
    ggsave(png_path, p, width = 12, height = 10, dpi = 300)
  }

  cat("✅ RDA双序图已保存:", basename(output_path), "\n")
  return(p)
}

# 创建变量重要性图
create_variable_importance_plot <- function(rda_result, output_path) {

  if(is.null(rda_result$explanatory_data)) {
    cat("⚠️ 缺少环境变量数据，跳过重要性图绘制\n")
    return(NULL)
  }

  # 使用envfit计算变量重要性
  env_fit <- envfit(rda_result$rda_model, rda_result$explanatory_data, permutations = 999)

  # 提取重要性数据
  importance_data <- data.frame(
    Variable = names(env_fit$vectors$r),
    R_squared = env_fit$vectors$r,
    P_value = env_fit$vectors$pvals,
    Significant = env_fit$vectors$pvals < 0.05
  )

  # 按重要性排序
  importance_data <- importance_data[order(importance_data$R_squared, decreasing = TRUE), ]
  
  # 为显著性添加星号标识
  importance_data$sig_symbol <- ""
  importance_data$sig_symbol[importance_data$P_value < 0.05] <- "*"
  importance_data$sig_symbol[importance_data$P_value < 0.01] <- "**"
  importance_data$sig_symbol[importance_data$P_value < 0.001] <- "***"
  
  # 添加显著性类别
  importance_data$sig_level <- "ns"
  importance_data$sig_level[importance_data$P_value < 0.05] <- "p < 0.05"
  importance_data$sig_level[importance_data$P_value < 0.01] <- "p < 0.01"
  importance_data$sig_level[importance_data$P_value < 0.001] <- "p < 0.001"
  
  # 为显著性设置因子顺序
  importance_data$sig_level <- factor(importance_data$sig_level, 
                                      levels = c("p < 0.001", "p < 0.01", "p < 0.05", "ns"))
  
  # 设置变量名顺序
  importance_data$Variable <- factor(importance_data$Variable, 
                                     levels = rev(importance_data$Variable))

  # 为显著性水平设置配色
  sig_colors <- c(
    "p < 0.001" = rda_colors$success, 
    "p < 0.01" = "#58d68d", 
    "p < 0.05" = "#abebc6", 
    "ns" = "#d5dbdb"
  )
  
  # 创建更美观的ggplot条形图
  p <- ggplot(importance_data, aes(x = R_squared, y = Variable, fill = sig_level)) +
    geom_bar(stat = "identity", width = 0.7) +
    geom_text(aes(label = sig_symbol), hjust = -0.5, size = 5) +
    geom_vline(xintercept = 0.1, linetype = "dashed", 
               color = rda_colors$warning, linewidth = 0.8) +
    scale_fill_manual(values = sig_colors, name = "显著性水平") +
    labs(
      title = "环境变量重要性 (R²)",
      subtitle = "基于999次置换检验",
      x = "解释度 (R²)",
      y = "",
      caption = "虚线表示R² = 0.1; * p<0.05, ** p<0.01, *** p<0.001"
    ) +
    theme_rda() +
    theme(
      axis.text.y = element_text(size = 11),
      panel.grid.major.y = element_blank(),
      legend.position = "top"
    )

  # 保存高质量图表
  ggsave(output_path, p, width = 12, height = 8, dpi = 300)

  cat("✅ 变量重要性图已保存:", basename(output_path), "\n")
  return(importance_data)
}

# 主可视化函数
create_publication_plots <- function(rda_results, processed_data, output_dir, analysis_type = "overall") {
  cat("\n🎨 === 创建论文级图表 ===\n")

  if(analysis_type == "stratified") {
    # 分层分析图表
    cat("📊 创建分层分析图表...\n")
    valid_results <- 0

    for(layer_name in names(rda_results)) {
      result <- rda_results[[layer_name]]
      if(is.null(result) || is.null(result$rda_model)) {
        cat("  ⚠️ 跳过", layer_name, "（无有效结果）\n")
        next
      }

      valid_results <- valid_results + 1
      cat("  处理深度层:", layer_name, "\n")

      # 只创建RDA双序图（核心图表）
      tryCatch({
        biplot_path <- file.path(output_dir, paste0("RDA双序图_", layer_name, ".png"))
        create_rda_biplot(result, paste0("RDA双序图 - ", layer_name), biplot_path)
      }, error = function(e) {
        cat("    ⚠️ 创建", layer_name, "双序图失败:", e$message, "\n")
      })
    }

    cat("  有效结果数:", valid_results, "/", length(rda_results), "\n")

    # 解释度对比图
    if(valid_results > 1) {
      tryCatch({
        variance_path <- file.path(output_dir, "分层RDA解释度对比.png")
        create_variance_barplot(rda_results, variance_path, "stratified")
      }, error = function(e) {
        cat("  ⚠️ 创建解释度对比图失败:", e$message, "\n")
      })
    }

  } else {
    # 整体分析图表
    cat("📊 创建整体分析图表...\n")
    if(!is.null(rda_results) && !is.null(rda_results$rda_model)) {
      # 只创建RDA双序图
      tryCatch({
        biplot_path <- file.path(output_dir, "RDA双序图_整体.png")
        create_rda_biplot(rda_results, "RDA双序图 - 整体数据", biplot_path)
      }, error = function(e) {
        cat("  ⚠️ 创建整体双序图失败:", e$message, "\n")
      })
    } else {
      cat("  ⚠️ 整体RDA结果无效\n")
    }
  }

  # 核心RDA分析图表
  cat("\n📊 === 生成核心RDA分析图表 ===\n")

  # 变量重要性图（如果是整体分析）
  if(analysis_type == "overall" && !is.null(rda_results$explanatory_data)) {
    importance_path <- file.path(output_dir, "环境变量重要性分析.png")
    create_variable_importance_plot(rda_results, importance_path)
  }

  # 创建分响应变量的详细分析图
  cat("\n📊 === 生成分响应变量分析图表 ===\n")
  create_individual_response_analysis(rda_results, processed_data, output_dir, analysis_type)

  cat("✅ 核心图表创建完成！\n")
}

# 创建分响应变量的详细分析
create_individual_response_analysis <- function(rda_results, processed_data, output_dir, analysis_type) {

  if(analysis_type != "overall" || is.null(rda_results$explanatory_data)) {
    cat("⚠️ 跳过分响应变量分析（仅支持整体分析）\n")
    return(NULL)
  }

  response_vars <- processed_data$response_vars
  env_data <- rda_results$explanatory_data

  # 为每个响应变量创建单独的RDA分析
  for(response_var in response_vars) {

    cat("📊 分析", response_var, "的驱动因子...\n")

    # 提取单个响应变量数据
    response_data <- processed_data$data[, response_var, drop = FALSE]
    response_data <- response_data[complete.cases(response_data), , drop = FALSE]

    # 匹配环境变量数据
    env_matched <- env_data[rownames(response_data), , drop = FALSE]

    # 执行单响应变量RDA
    tryCatch({
      single_rda <- rda(response_data ~ ., data = env_matched)

      # 使用envfit找到最重要的变量
      env_fit <- envfit(single_rda, env_matched, permutations = 999)

      # 验证envfit结果
      if(is.null(env_fit$vectors) || is.null(env_fit$vectors$r) || length(env_fit$vectors$r) == 0) {
        cat("   ⚠️", response_var, "envfit分析失败，跳过\n")
        next
      }

      # 选择显著且重要的变量（前10个）
      var_importance <- data.frame(
        Variable = names(env_fit$vectors$r),
        R_squared = env_fit$vectors$r,
        P_value = env_fit$vectors$pvals,
        stringsAsFactors = FALSE
      )
      var_importance <- var_importance[order(var_importance$R_squared, decreasing = TRUE), ]
      top_vars <- head(var_importance[var_importance$P_value < 0.1, ], 10)  # 放宽到p<0.1

      if(nrow(top_vars) > 0 && nrow(top_vars) >= 3) {  # 至少需要3个变量才绘图
        # 创建清晰的双序图
        output_path <- file.path(output_dir, paste0(response_var, "_驱动因子分析.png"))
        create_modern_single_response_biplot(single_rda, env_fit, top_vars, response_var, output_path)

        # 创建变量重要性条形图
        importance_path <- file.path(output_dir, paste0(response_var, "_变量重要性.png"))
        create_modern_importance_barplot(top_vars, response_var, importance_path)

        cat("   ✅", response_var, "分析完成，显著变量:", nrow(top_vars), "个\n")
      } else {
        cat("   ⚠️", response_var, "显著变量不足（需要≥3个），跳过绘图\n")
      }

    }, error = function(e) {
      cat("   ❌", response_var, "分析失败:", e$message, "\n")
    })
  }
}

# 创建现代化的单响应变量双序图
create_modern_single_response_biplot <- function(rda_model, env_fit, top_vars, response_var, output_path) {
  tryCatch({
    cat("     🔍 调试信息 - 开始创建", response_var, "双序图\n")
    
    # 计算解释度
    eigenvals <- eigenvals(rda_model)
    total_var <- sum(eigenvals)
    rda1_var <- round(eigenvals[1] / total_var * 100, 2)
    cat("     📊 解释度计算完成:", rda1_var, "%\n")

    # 获取样本点、响应变量和环境变量得分
    site_scores <- scores(rda_model, display = "sites", choices = 1:2)
    species_scores <- scores(rda_model, display = "species", choices = 1:2)
    cat("     📍 样本点得分维度:", dim(site_scores), "\n")
    cat("     📍 物种得分维度:", dim(species_scores), "\n")
    
    # 安全获取环境变量得分
    env_scores_all <- scores(env_fit, display = "vectors")
    if(is.null(env_scores_all) || nrow(env_scores_all) == 0) {
      stop("无法获取环境变量得分")
    }
    cat("     🌍 环境变量得分维度:", dim(env_scores_all), "\n")
  
  # 确保所有requested变量都存在
  available_vars <- intersect(top_vars$Variable, rownames(env_scores_all))
  if(length(available_vars) == 0) {
    stop("没有可用的环境变量得分")
  }
  
  important_scores <- env_scores_all[available_vars, , drop = FALSE]
  
  # 构建数据框 - 添加数据验证
  site_df <- data.frame(site_scores, row.names = rownames(site_scores))
  species_df <- data.frame(species_scores, row.names = rownames(species_scores))
  
  # 确保P值匹配
  matched_pvals <- top_vars$P_value[match(available_vars, top_vars$Variable)]
  
  env_df <- data.frame(
    important_scores, 
    Variable = available_vars,
    P_value = matched_pvals,
    row.names = available_vars,
    stringsAsFactors = FALSE
  )
  
  # 设置显著性分组
  env_df$significance <- "p ≥ 0.05"
  env_df$significance[env_df$P_value < 0.05] <- "p < 0.05"
  env_df$significance[env_df$P_value < 0.01] <- "p < 0.01"
  env_df$significance[env_df$P_value < 0.001] <- "p < 0.001"
  env_df$significance <- factor(env_df$significance, 
                              levels = c("p < 0.001", "p < 0.01", "p < 0.05", "p ≥ 0.05"))
  
  # 设置箭头颜色
  sig_colors <- c(
    "p < 0.001" = rda_colors$success, 
    "p < 0.01" = "#58d68d", 
    "p < 0.05" = "#abebc6", 
    "p ≥ 0.05" = "#d5dbdb"
  )
  
  # 获取响应变量颜色
  response_color <- rda_colors$response[response_var]
  if(is.na(response_color)) response_color <- rda_colors$warning
  
  # 创建ggplot图表
  p <- ggplot() +
    # 添加网格线和辅助线
    geom_hline(yintercept = 0, linetype = "dashed", color = "#bdc3c7", alpha = 0.8) +
    geom_vline(xintercept = 0, linetype = "dashed", color = "#bdc3c7", alpha = 0.8) +
    
    # 添加样本点
    geom_point(data = site_df, 
               aes(x = RDA1, y = RDA2),
               shape = 21, fill = "white", color = "#3498db",
               alpha = 0.7, size = 2.5, stroke = 0.5) +
    
    # 添加样本点的聚类椭圆
    stat_ellipse(data = site_df, 
                aes(x = RDA1, y = RDA2),
                type = "norm", level = 0.95,
                color = "#3498db", alpha = 0.2, linetype = 2) +
    
    # 添加响应变量箭头
    geom_segment(data = species_df,
                aes(x = 0, y = 0, xend = RDA1, yend = RDA2),
                arrow = arrow(length = unit(0.5, "cm"), type = "closed"),
                color = response_color, linewidth = 2, alpha = 0.9) +
    
    # 添加环境变量箭头（按显著性着色）
    geom_segment(data = env_df,
                aes(x = 0, y = 0, xend = RDA1, yend = RDA2, color = significance),
                arrow = arrow(length = unit(0.3, "cm"), type = "closed"),
                linewidth = 0.8, alpha = 0.9) +
    
    # 添加响应变量标签
    geom_text(data = species_df,
             aes(x = RDA1 * 1.05, y = RDA2 * 1.05, label = response_var),
             color = response_color, size = 5, fontface = "bold") +
    
    # 添加环境变量标签（使用ggrepel避免重叠）
    geom_text_repel(data = env_df,
                   aes(x = RDA1, y = RDA2, label = Variable, color = significance),
                   size = 3.5, fontface = "bold",
                   box.padding = 0.5, point.padding = 0.3,
                   seed = 123, max.overlaps = 20) +
    
    # 坐标和标签设置
    labs(
      title = paste(response_var, "的主要驱动因子"),
      subtitle = paste0("RDA1解释: ", rda1_var, "% 变异"),
      x = paste0("RDA1 (", rda1_var, "%)"),
      y = "RDA2",
      caption = paste("样本数:", nrow(site_df), "| 显著驱动因子:", sum(env_df$P_value < 0.05))
    ) +
    
    # 颜色映射
    scale_color_manual(values = sig_colors, name = "显著性水平") +
    
    # 固定坐标比例
    coord_fixed() +
    
    # 应用自定义主题
    theme_rda()
  
    # 保存图表
    ggsave(output_path, p, width = 10, height = 8, dpi = 300)
    
    cat("   ✅ 现代化双序图已保存:", basename(output_path), "\n")
    return(p)
    
  }, error = function(e) {
    cat("     ❌ 绘图失败，详细错误:", e$message, "\n")
    cat("     🔍 调试信息 - 数据框状态检查:\n")
    
    # 输出更多调试信息
    if(exists("site_df")) {
      cat("       site_df维度:", nrow(site_df), "x", ncol(site_df), "\n")
      cat("       site_df列名:", paste(colnames(site_df), collapse=", "), "\n")
    }
    if(exists("species_df")) {
      cat("       species_df维度:", nrow(species_df), "x", ncol(species_df), "\n")
      cat("       species_df列名:", paste(colnames(species_df), collapse=", "), "\n")
    }
    if(exists("env_df")) {
      cat("       env_df维度:", nrow(env_df), "x", ncol(env_df), "\n")
      cat("       env_df列名:", paste(colnames(env_df), collapse=", "), "\n")
    }
    
    stop(paste("绘图函数错误:", e$message))
  })
}

# 创建现代化变量重要性条形图
create_modern_importance_barplot <- function(top_vars, response_var, output_path) {
  # 数据准备
  importance_df <- top_vars
  importance_df$Variable <- factor(importance_df$Variable, 
                                  levels = rev(importance_df$Variable[order(importance_df$R_squared)]))
  
  # 为显著性添加标签
  importance_df$significance <- "p ≥ 0.05"
  importance_df$significance[importance_df$P_value < 0.05] <- "p < 0.05"
  importance_df$significance[importance_df$P_value < 0.01] <- "p < 0.01"
  importance_df$significance[importance_df$P_value < 0.001] <- "p < 0.001"
  
  importance_df$significance <- factor(importance_df$significance, 
                                     levels = c("p < 0.001", "p < 0.01", "p < 0.05", "p ≥ 0.05"))
  
  # 设置显著性颜色
  sig_colors <- c(
    "p < 0.001" = rda_colors$success, 
    "p < 0.01" = "#58d68d", 
    "p < 0.05" = "#abebc6", 
    "p ≥ 0.05" = "#d5dbdb"
  )
  
  # 获取响应变量颜色
  response_color <- rda_colors$response[response_var]
  if(is.na(response_color)) response_color <- rda_colors$warning
  
  # 创建ggplot图表
  p <- ggplot(importance_df, aes(x = Variable, y = R_squared, fill = significance)) +
    geom_bar(stat = "identity") +
    geom_hline(yintercept = 0.1, linetype = "dashed", 
               color = "#e74c3c", linewidth = 0.8) +
    scale_fill_manual(values = sig_colors, name = "显著性水平") +
    labs(
      title = paste(response_var, "的环境驱动因子重要性"),
      subtitle = "基于R²值排序，显示了环境变量解释能力",
      x = "",
      y = "解释度 (R²)",
      caption = "虚线表示R² = 0.1阈值"
    ) +
    coord_flip() +
    theme_rda() +
    theme(
      axis.text.y = element_text(size = 11),
      legend.position = "top",
      panel.grid.major.y = element_blank()
    )
  
  # 保存图表
  ggsave(output_path, p, width = 10, height = 6, dpi = 300)
  
  cat("   ✅ 现代化重要性条形图已保存:", basename(output_path), "\n")
  return(p)
}

# 创建解释度对比条形图
create_variance_barplot <- function(rda_results, output_path, analysis_type = "stratified") {
  # 构建数据
  if(analysis_type == "stratified") {
    variance_data <- data.frame(
      Layer = character(),
      Variance = numeric(),
      stringsAsFactors = FALSE
    )
    
    for(layer_name in names(rda_results)) {
      result <- rda_results[[layer_name]]
      if(!is.null(result) && !is.null(result$explained_variance)) {
        variance_data <- rbind(variance_data, 
                              data.frame(Layer = layer_name, 
                                        Variance = result$explained_variance))
      }
    }
    
    if(nrow(variance_data) > 0) {
      # 设置深度层的因子顺序
      layer_order <- c("0-10cm", "10-30cm", "30-60cm", "60-100cm")
      variance_data$Layer <- factor(variance_data$Layer, levels = layer_order)
      
      # 创建条形图
      p <- ggplot(variance_data, aes(x = Layer, y = Variance, fill = Layer)) +
        geom_bar(stat = "identity", width = 0.7) +
        geom_text(aes(label = paste0(round(Variance, 1), "%")), 
                 vjust = -0.5, size = 4, fontface = "bold") +
        scale_fill_brewer(palette = "Blues") +
        labs(
          title = "不同土壤深度层的RDA解释度对比",
          subtitle = "显示环境因子对各深度土壤属性变化的解释能力",
          x = "土壤深度层",
          y = "解释度 (%)",
          caption = paste("基于", nrow(variance_data), "个有效深度层的分析结果")
        ) +
        theme_rda() +
        theme(legend.position = "none")
      
      ggsave(output_path, p, width = 10, height = 6, dpi = 300)
      cat("✅ 深度层解释度对比图已保存:", basename(output_path), "\n")
    }
  }
}
