@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: 批量文件重命名工具 (Windows批处理版本)
:: 功能：将文件名中的指定字符替换为下划线，递归处理所有子文件夹

:: ==================== 配置区 ====================
:: 默认处理目录
set "default_directory=."

:: 要替换的字符（用空格分隔）
set "chars_to_replace= -"

:: 替换后的字符
set "replacement_char=_"

:: 是否默认处理文件夹名 (1=是, 0=否)
set "process_folders_default=0"

:: 排除的文件夹名称（用空格分隔，不区分大小写）
set "excluded_folders=.git .svn __pycache__ node_modules"

:: 排除的文件名（用空格分隔，不区分大小写）
set "excluded_files=desktop.ini thumbs.db .DS_Store"

:: 是否显示详细日志 (1=是, 0=否)
set "verbose_logging=1"

:: 最大处理文件数量 (0=无限制)
set "max_files_to_process=0"
:: ===============================================

echo ========================================
echo 批量文件重命名工具 (批处理版本)
echo 功能：将文件名中的指定字符替换为下划线
echo ========================================
echo.
echo 📋 当前配置:
echo   - 默认目录: %default_directory%
echo   - 替换字符: %chars_to_replace% -^> %replacement_char%
echo   - 默认处理文件夹: %process_folders_default%
if not "%excluded_folders%"=="" echo   - 排除文件夹: %excluded_folders%
if not "%max_files_to_process%"=="0" echo   - 最大处理文件数: %max_files_to_process%
echo ========================================
echo.

:: 获取目标目录
set "target_dir=%~1"
if "%target_dir%"=="" (
    set /p "target_dir=请输入要处理的目录路径（回车使用配置的默认目录: %default_directory%）: "
    if "!target_dir!"=="" set "target_dir=%default_directory%"
)

:: 验证目录是否存在
if not exist "%target_dir%" (
    echo ❌ 错误: 目录 '%target_dir%' 不存在！
    pause
    exit /b 1
)

:: 获取绝对路径
pushd "%target_dir%" 2>nul
if errorlevel 1 (
    echo ❌ 错误: 无法访问目录 '%target_dir%'！
    pause
    exit /b 1
)
set "target_dir=%CD%"
popd

echo 目标目录: %target_dir%
echo.

:: 询问是否包含文件夹重命名
if "%process_folders_default%"=="1" (
    set "default_hint=y"
) else (
    set "default_hint=N"
)
set /p "include_folders=是否同时重命名文件夹？(y/N，默认: !default_hint!): "
if "!include_folders!"=="" set "include_folders=!default_hint!"
if /i "!include_folders!"=="y" (
    set "process_folders=1"
    echo 注意：将同时重命名文件和文件夹
) else (
    set "process_folders=0"
    echo 注意：将只重命名文件，不处理文件夹名
)
echo.

:: 预览模式
echo 🔍 预览模式 - 显示将要进行的更改:
echo ========================================

set "preview_count=0"
set "skip_count=0"

:: 预览文件重命名
set "processed_files=0"
for /r "%target_dir%" %%f in (*) do (
    set "filename=%%~nxf"
    set "filepath=%%f"
    set "should_process=0"
    set "is_excluded=0"

    :: 检查是否在排除文件列表中
    for %%e in (%excluded_files%) do (
        if /i "!filename!"=="%%e" set "is_excluded=1"
    )

    :: 检查文件名是否包含需要替换的字符
    if "!is_excluded!"=="0" (
        for %%c in (%chars_to_replace%) do (
            echo !filename! | findstr "%%c" >nul
            if !errorlevel! equ 0 set "should_process=1"
        )
    )

    if "!should_process!"=="1" (
        :: 检查是否达到最大处理数量限制
        if not "%max_files_to_process%"=="0" (
            if !processed_files! geq %max_files_to_process% (
                echo   ⚠️  已达到最大处理文件数量限制: %max_files_to_process%
                goto :preview_folders
            )
        )

        set "new_filename=!filename!"
        :: 替换所有指定字符
        for %%c in (%chars_to_replace%) do (
            set "new_filename=!new_filename:%%c=%replacement_char%!"
        )

        set "new_filepath=%%~dpf!new_filename!"

        :: 检查新文件名是否已存在
        if exist "!new_filepath!" (
            if "%verbose_logging%"=="1" echo   ⚠️  跳过: !filename! ^(目标文件名已存在^)
            set /a skip_count+=1
        ) else (
            if "%verbose_logging%"=="1" echo   📝 将重命名: !filename! -^> !new_filename!
            set /a preview_count+=1
        )
        set /a processed_files+=1
    )
)

:preview_folders

:: 预览文件夹重命名（如果启用）
if "!process_folders!"=="1" (
    for /f "delims=" %%d in ('dir "%target_dir%" /ad /b /s 2^>nul') do (
        for %%i in ("%%d") do (
            set "dirname=%%~nxi"
            set "dirpath=%%d"
            
            :: 检查文件夹名是否包含空格
            echo !dirname! | findstr " " >nul
            if !errorlevel! equ 0 (
                set "new_dirname=!dirname: =_!"
                set "parent_dir=%%~dpi"
                set "new_dirpath=!parent_dir!!new_dirname!"
                
                :: 检查新文件夹名是否已存在
                if exist "!new_dirpath!" (
                    echo   ⚠️  跳过文件夹: !dirname! ^(目标文件夹名已存在^)
                    set /a skip_count+=1
                ) else (
                    echo   📁 将重命名文件夹: !dirname! -^> !new_dirname!
                    set /a preview_count+=1
                )
            )
        )
    )
)

echo.
echo 📊 预览结果统计:
echo   - 将要处理的项目: !preview_count!
echo   - 将要跳过的项目: !skip_count!

if !preview_count! equ 0 (
    echo.
    echo ✨ 没有需要重命名的文件或文件夹！
    pause
    exit /b 0
)

:: 确认执行
echo.
set /p "confirm=❓ 确认执行重命名操作？(y/N): "
if /i not "!confirm!"=="y" (
    echo ❌ 操作已取消
    pause
    exit /b 0
)

:: 执行重命名
echo.
echo 🚀 开始执行重命名操作...
echo ========================================

set "success_count=0"
set "error_count=0"
set "final_skip_count=0"

:: 重命名文件
for /r "%target_dir%" %%f in (*) do (
    set "filename=%%~nxf"
    set "filepath=%%f"
    
    :: 检查文件名是否包含空格
    echo !filename! | findstr " " >nul
    if !errorlevel! equ 0 (
        set "new_filename=!filename: =_!"
        set "new_filepath=%%~dpf!new_filename!"
        
        :: 检查新文件名是否已存在
        if exist "!new_filepath!" (
            echo   ⚠️  跳过: !filename! ^(目标文件名已存在^)
            set /a final_skip_count+=1
        ) else (
            ren "!filepath!" "!new_filename!" 2>nul
            if !errorlevel! equ 0 (
                echo   ✅ 已重命名: !filename! -^> !new_filename!
                set /a success_count+=1
            ) else (
                echo   ❌ 重命名失败: !filename!
                set /a error_count+=1
            )
        )
    )
)

:: 重命名文件夹（如果启用）
if "!process_folders!"=="1" (
    :: 从最深层开始重命名，避免路径变化导致的问题
    for /f "delims=" %%d in ('dir "%target_dir%" /ad /b /s 2^>nul ^| sort /r') do (
        for %%i in ("%%d") do (
            set "dirname=%%~nxi"
            set "dirpath=%%d"
            
            :: 检查文件夹名是否包含空格
            echo !dirname! | findstr " " >nul
            if !errorlevel! equ 0 (
                set "new_dirname=!dirname: =_!"
                set "parent_dir=%%~dpi"
                set "new_dirpath=!parent_dir!!new_dirname!"
                
                :: 检查新文件夹名是否已存在
                if exist "!new_dirpath!" (
                    echo   ⚠️  跳过文件夹: !dirname! ^(目标文件夹名已存在^)
                    set /a final_skip_count+=1
                ) else (
                    ren "!dirpath!" "!new_dirname!" 2>nul
                    if !errorlevel! equ 0 (
                        echo   ✅ 已重命名文件夹: !dirname! -^> !new_dirname!
                        set /a success_count+=1
                    ) else (
                        echo   ❌ 重命名文件夹失败: !dirname!
                        set /a error_count+=1
                    )
                )
            )
        )
    )
)

echo.
echo 📊 执行结果统计:
echo   - 成功处理: !success_count!
echo   - 跳过项目: !final_skip_count!
echo   - 失败项目: !error_count!

if !error_count! equ 0 (
    echo.
    echo 🎉 所有操作完成！
) else (
    echo.
    echo ⚠️  完成，但有 !error_count! 个项目处理失败
)

echo.
pause
