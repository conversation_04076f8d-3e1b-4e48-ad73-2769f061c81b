# 批量文件重命名工具使用说明

## 功能介绍
这个工具可以递归遍历指定目录及其所有子目录，将文件名和文件夹名中的指定字符（默认为空格和连字符）替换为下划线。

## 新增配置功能
- ✅ **可配置替换字符**：默认替换空格和连字符，可自定义
- ✅ **可配置默认目录**：设置常用的处理目录
- ✅ **文件类型过滤**：可指定只处理特定扩展名的文件
- ✅ **排除规则**：可排除特定的文件和文件夹
- ✅ **处理数量限制**：可设置最大处理文件数量
- ✅ **详细日志控制**：可开关详细输出信息

## 文件说明
- `批量重命名工具.py` - Python版本（推荐）
- `批量重命名工具.bat` - Windows批处理版本
- `使用说明.md` - 本说明文件

## 使用方法

### Python版本（推荐）

#### 方法1：直接运行
```bash
python 批量重命名工具.py
```
然后按提示输入目录路径，或直接回车使用当前目录。

#### 方法2：命令行参数
```bash
python 批量重命名工具.py "目标目录路径"
```

#### 特点：
- ✅ 跨平台兼容（Windows/Mac/Linux）
- ✅ 预览模式，先显示将要更改的文件
- ✅ 安全检查，避免覆盖已存在的文件
- ✅ 详细的执行日志和统计信息
- ✅ 支持中断操作（Ctrl+C）
- ✅ 完善的错误处理

### Windows批处理版本

#### 方法1：双击运行
直接双击 `批量重命名工具.bat` 文件

#### 方法2：命令行运行
```cmd
批量重命名工具.bat "目标目录路径"
```

#### 特点：
- ✅ Windows原生支持，无需额外环境
- ✅ 预览模式
- ✅ 基本的安全检查
- ❌ 仅限Windows系统

## 使用流程

1. **选择目录**：输入要处理的目录路径，或使用当前目录
2. **选择处理范围**：决定是否同时重命名文件夹
3. **预览更改**：工具会显示所有将要进行的重命名操作
4. **确认执行**：确认无误后，输入 'y' 开始实际重命名
5. **查看结果**：显示处理统计信息

## 安全特性

- **预览模式**：执行前先显示所有将要进行的更改
- **冲突检测**：如果目标文件名已存在，会跳过该文件
- **用户确认**：需要用户明确确认才会执行实际重命名
- **详细日志**：显示每个文件的处理状态
- **错误处理**：遇到错误时不会中断整个流程

## 配置说明

### Python版本配置（在脚本顶部的CONFIG字典中）

```python
CONFIG = {
    # 默认处理目录
    'default_directory': '.',

    # 要替换的字符列表（这些字符将被替换为下划线）
    'chars_to_replace': [' ', '-'],

    # 替换后的字符
    'replacement_char': '_',

    # 是否默认处理文件夹名
    'process_folders_default': False,

    # 文件扩展名过滤（空列表表示处理所有文件）
    'file_extensions_filter': [],  # 例如: ['.txt', '.docx', '.xlsx']

    # 排除的文件夹名称
    'excluded_folders': ['.git', '.svn', '__pycache__', 'node_modules'],

    # 排除的文件名模式
    'excluded_files': ['desktop.ini', 'thumbs.db', '.DS_Store'],

    # 最大处理文件数量（0表示无限制）
    'max_files_to_process': 0
}
```

### 批处理版本配置（在脚本顶部的配置区）

```batch
:: 默认处理目录
set "default_directory=."

:: 要替换的字符（用空格分隔）
set "chars_to_replace= -"

:: 替换后的字符
set "replacement_char=_"

:: 排除的文件夹名称（用空格分隔）
set "excluded_folders=.git .svn __pycache__ node_modules"
```

## 示例

### 处理前的文件结构：
```
项目文件夹/
├── 数据 分析-最终版.xlsx
├── 结果 报告-v2.docx
├── 子文件夹 1/
│   ├── 图表 数据-备份.csv
│   └── 备份 文件-old.txt
└── 子文件夹-重要/
    └── 最终 结果-final.pdf
```

### 处理后的文件结构：
```
项目文件夹/
├── 数据_分析_最终版.xlsx
├── 结果_报告_v2.docx
├── 子文件夹_1/
│   ├── 图表_数据_备份.csv
│   └── 备份_文件_old.txt
└── 子文件夹_重要/
    └── 最终_结果_final.pdf
```

## 注意事项

1. **备份重要数据**：虽然工具有安全检查，但建议在处理重要文件前先备份
2. **权限要求**：确保对目标目录有读写权限
3. **文件占用**：确保要重命名的文件没有被其他程序占用
4. **路径长度**：Windows系统注意路径长度限制
5. **特殊字符**：工具只处理空格，不处理其他特殊字符

## 常见问题

**Q: 如果目标文件名已存在怎么办？**
A: 工具会跳过该文件，不会覆盖已存在的文件。

**Q: 可以撤销重命名操作吗？**
A: 工具本身不提供撤销功能，建议在操作前备份重要数据。

**Q: 为什么某些文件重命名失败？**
A: 可能的原因包括：文件被占用、权限不足、路径过长等。

**Q: 可以只处理特定类型的文件吗？**
A: 可以！在Python版本中设置 `file_extensions_filter` 参数，例如 `['.txt', '.docx', '.xlsx']` 只处理这些类型的文件。

**Q: 如何修改要替换的字符？**
A: 在配置区修改 `chars_to_replace` 参数，可以添加或删除要替换的字符。

**Q: 如何设置默认处理目录？**
A: 修改配置中的 `default_directory` 参数为您常用的目录路径。

## 技术支持

如果遇到问题或需要定制功能，请检查：
1. Python环境是否正确安装（Python版本）
2. 目录路径是否正确
3. 是否有足够的权限
4. 文件是否被其他程序占用
