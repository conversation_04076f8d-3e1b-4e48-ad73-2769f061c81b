# =============================================================================
# 三种方法对比建模配置文件
# 参考04克里金模块的配置结构，但针对新的技术路线优化
# =============================================================================

# --- 数据路径配置 ---
data_paths:
  # 完整数据文件路径（包含基础信息+属性+环境变量，用于缺失值分析、建模和预测）
  full_data_path: "../00 原始数据/辅助变量-两期数据.xlsx"

  # 相关性分析结果目录（基于相关性分析脚本输出）
  correlation_root: "../00 原始数据/典型县/相关性分析结果"

  # 输出结果目录（建模结果保存位置）
  output_directory: "../00 原始数据/典型县/模型预测结果"

# --- 分析范围配置 ---

# === 县城分析策略配置 ===
# 简单开关：true=合并所有县城分析, false=分县城分析
combine_counties: false               

# 要处理的县城列表
counties:
  - "嫩江"
  - "梨树"
  - "科左"
  - "铁岭"
  - "凤城"

# 要处理的年份列表
years:
  - 1980
  - 2023

# 要建模的目标土壤属性列表
target_soil_properties:
  - "黑土层厚度"
  - "SOM"
  - "pH"
  - "TN"
  - "TP"
  - "物理粘粒"

# --- 数据列名配置 ---
column_names:
  county: "City"
  year: "year"
  longitude: "Longitude"
  latitude: "Latitude"
  depth: "深度中点"
  profile_id: "ProfileID"
  # boundary_field: "县"  # 未使用

# --- 坐标系配置 ---
crs_target: 4550  # 中国大地坐标系2000 / 3-degree Gauss-Kruger CM 114E

# ==================================
# ---- 三种方法建模参数配置 ----
# ==================================

# --- 通用建模参数 ---
general_options:
  # 深度层划分（按实际采样深度范围划分）
  depth_layers:
    - name: "0-10cm"      # 表土层
      range: [0, 10]
      center: 5
    - name: "10-30cm"     # 亚表土层
      range: [10, 30]
      center: 20
    - name: "30-60cm"     # 亚土层
      range: [30, 60]
      center: 45
    - name: "60-100cm"    # 底土层
      range: [60, 100]
      center: 80

  # 建模基本要求
  min_samples_for_modeling: 10    # 建模所需的最少样本数（适应极小样本）

  # 智能建模策略参数
  max_variable_missing_rate: 0.3     # （环境变量和邻近层变量）最大缺失率阈值

  # === 基于实际建模样本量的自适应协变量选择策略 ===
  adaptive_variable_selection:
    enable: true                    # 启用基于建模样本量的自适应选择
    
    # 环境变量数量规则（基于土壤制图文献实践，平衡统计安全性与预测精度）
    sample_size_rules:
      - sample_range: [10, 30]      # 小样本
        max_variables: 3             # RF最坏：6+3=9特征，30÷9=3.3:1（文献可接受）
      - sample_range: [31, 50]      # 中小样本（合并县城）
        max_variables: 6             # RF最坏：6+6=12特征，50÷12=4.2:1（文献常见）
      - sample_range: [51, 70]      # 中等样本（合并县城）
        max_variables: 10            # RF最坏：6+10=16特征，70÷16=4.4:1（文献标准）
      - sample_range: [71, 100]     # 大样本（合并县城）
        max_variables: 15            # RF最坏：6+15=21特征，80÷21=3.8:1（文献常见）
        
    
    # 显著性阈值设置
    significance_thresholds:
      primary: 0.05                 # 主要显著性阈值 (p<0.05)
      marginal: 0.20                # 边际显著性阈值 (p<0.1)
    
    # 相关性强度阈值
    correlation_thresholds:
      min_abs_r: 0.20                # 最小相关系数绝对值

# ==================================
# ---- 自动调优配置（基于学术文献标准）----
# ==================================

# --- 通用调优设置---
auto_tuning:
  # 交叉验证策略
  cv_strategy: "adaptive"
  adaptive_cv:
    loo_threshold: 20                     # n ≤ 20: 留一交叉验证
    five_fold_threshold: 50               # 20 < n ≤ 50: 5折交叉验证
    ten_fold_threshold: 50                # n > 50: 10折交叉验证



# ==================================
# ---- 建模方法配置（基于文献标准范围）----
# ==================================

# --- 回归克里金 ---
regression_kriging:
  # 变异函数模型（按优先级自动尝试，指数模型通常较稳健）
  vgm_models: ["Exp", "Sph", "Gau"]

  # 调优参数范围（扩展范围，适应所有样本大小）
  tune:
    nmax: [6, 8, 12]                        # 邻近点数（扩展范围，适应不同样本大小）
    nmin: [3, 5]                  # 最小邻近点数（扩展范围）

# --- 普通克里金 ---
ordinary_kriging:
  # 变异函数模型（扩展模型选择，适应不同数据特征）
  vgm_models: ["Exp", "Sph", "Gau"]

  # 调优参数范围（扩展范围，适应所有样本大小）
  tune:
    nmax: [6, 8, 12]                         # 邻近点数（与回归克里金一致）
    nmin: [3, 5]                  # 最小邻近点数（与回归克里金一致）

# --- 随机森林 ---
random_forest:
  # 调优参数范围（扩展范围，适应所有样本大小）
  tune:
    ntree: [50, 100, 200]             # 树数量（扩展范围）
    nodesize: [ 3, 5]                  # 叶节点最小样本数（包含小样本适用的较小值）
    mtry_factor: [0.33, 0.5, 0.7]           # mtry因子（保持不变）

  # 固定参数
  importance: true                          # 计算变量重要性
  seed: 666                                # 随机种子（确保可重现性）

# ==================================
# ---- 方法对比与缺失值填补配置 ----
# ==================================

# --- 方法对比配置 ---
method_comparison:
  # 模型选择策略
  selection_criteria: "r2"                  # 选择标准：r2(R²优先)
  r2_tolerance: 0.05                        # R²差异容忍度（小于此值时比较RMSE）

# --- 缺失值填补配置 ---
gap_filling:
  # 填补策略
  only_fill_missing_layers: true            # 只对有缺失值的深度层建模
  use_neighbor_layers: true                 # 使用邻近深度层作为协变量

  # 分层保底策略（基于物理合理性）
  fallback_strategy: "hierarchical"         # 分层保底策略
  hierarchical_fallback:
    - "profile_interpolation"               # 优先：同剖面其他深度PCHIP插值外推
    - "neighbor_mean"                       # 次选：邻近深度层均值
    - "depth_mean"                          # 保底：同深度层均值

  # 插值参数（基于土壤剖面标准化方法）
  interpolation:
    method: "pchip"                         # PCHIP插值（保持单调性，避免负值）
    min_points: 2                           # 最少需要2个点进行插值
    extrapolation: true                     # 允许外推

  # 输出设置
  output_filled_excel: true                 # 输出填补后的Excel文件
  filled_excel_filename: "东北分_填补后完整数据.xlsx"
  generate_report: true                     # 生成填补报告
  report_filename: "东北分_性能报告.xlsx"
  generate_visualizations: true             # 生成可视化图表
  visualization_dpi: 300                    # 图表分辨率

# ==================================
# ---- 实验配置 ----
# ==================================

# --- 测试模式 ---
experiment:
  test_mode: false                           # 启用测试模式（false=处理所有县城属性，true=仅测试模式）
  test_counties: ['嫩江', '梨树', '铁岭', '科左','凤城']                   # 测试县城（仅test_mode=true时生效）
  test_properties: [ '黑土层厚度']            # 测试土壤属性（仅test_mode=true时生效）- 测试多属性
  test_years: [1980, 2023]                        # 测试年份（仅test_mode=true时生效）

# --- 文件结束 ---