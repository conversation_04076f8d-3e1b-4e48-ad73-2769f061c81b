
新建一个NODE_HOME，变量值为安装路径：D:\Dev-code\nodejs
然后再在系统变量的【path】中添加
%NODE_HOME%
%NODE_HOME%\node_global
%NODE_HOME%\node_cache
用户变量C:\User\35025\AppDate\Roaming\npm改成D:\Dev-code\nodejs\node_global

npm config set cache "D:\Dev-code\nodejs\node_cache"
npm config set prefix "D:\Dev-code\nodejs\node_global"
npm config get cache
npm config get prefix
npm config set registry https://registry.npmjs.org
npm config get registry
npm cache clean --force