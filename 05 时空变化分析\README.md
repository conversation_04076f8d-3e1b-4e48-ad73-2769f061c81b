# 土壤数据时空变化分析系统

## 项目概述

本项目是一个专业的土壤数据时空变化分析系统，用于分析土壤理化性质在不同时间、空间和深度维度上的变化规律。系统采用方差分析方法，生成统计图表和Excel报告。

## 核心功能

### 1. 数据分析类型
- **县城对比分析**：比较不同县城间的土壤指标差异
- **时间对比分析**：分析同一县城不同年份的变化趋势
- **深度时间对比**：分析不同深度层在时间维度上的变化
- **深度剖面对比**：比较同一时期不同深度层的差异

### 2. 统计分析
- Kruskal-Wallis H检验（非参数方法）
- 多重比较校正（FDR-BH方法）
- 显著性字母标记（a, b, c等）
- 异常值检测与处理（IQR方法）

### 3. 可视化输出
- 箱线图展示数据分布
- 显著性标记
- 专业图表样式
- 高分辨率PNG输出

### 4. 报告生成
- Excel格式统计报告
- 分析概要表
- 统一结果表
- 显著性汇总表
- 统计方法说明

## 文件结构

```
05 时空变化分析/
├── A_main.py              # 主程序入口
├── B_config.py            # 配置文件
├── C_data_loader.py       # 数据加载与预处理
├── D_statistics.py        # 统计分析模块
├── E_analysis_engine.py   # 分析引擎
├── F_visualization.py     # 可视化模块
├── G_excel_export.py      # Excel导出模块
├── a_assumption_main.py   # 正态性检验程序
├── b_assumption_tests.py  # 假设检验模块
└── README.md              # 项目说明文档
```

## 核心模块说明

### A_main.py - 主程序
- 程序入口点
- 协调各模块工作流程
- 控制分析范围和类型

### B_config.py - 配置管理
- 数据路径配置
- 分析参数设置
- 图表样式配置
- 异常值处理配置

### C_data_loader.py - 数据处理
- `init_config()`: 动态识别数据配置
- `load_data()`: 加载Excel数据
- `preprocess_data()`: 数据预处理主函数
- `handle_outliers()`: 异常值处理

### D_statistics.py - 统计分析
- 非参数统计检验
- 多重比较校正
- 显著性判断

### E_analysis_engine.py - 分析引擎
- 统一的分析接口
- 结果格式化
- 批量分析处理

### F_visualization.py - 可视化
- 箱线图绘制
- 显著性标记
- 图表样式设置

### G_excel_export.py - 报告导出
- Excel报告生成
- 多工作表输出
- 格式化处理

## 使用方法

### 1. 环境要求
```python
pandas >= 1.3.0
numpy >= 1.20.0
matplotlib >= 3.3.0
seaborn >= 0.11.0
scipy >= 1.7.0
xlsxwriter >= 1.4.0
```

### 2. 配置数据路径
在 `B_config.py` 中设置：
```python
DATA_PATH = "你的数据文件路径.xlsx"
OUTPUT_DIR = "输出目录路径"
```

### 3. 设置分析范围
```python
# 全面分析（所有县城）
ANALYSIS_SCOPE = "all"

# 指定县城分析
ANALYSIS_SCOPE = ['嫩江', '凤城']
```

### 4. 运行分析
```bash
python A_main.py
```

## 数据格式要求

输入Excel文件必须包含以下列：
- `City`: 县城名称
- `year`: 年份
- `深度范围`: 深度层（如'0-10', '10-30'等）
- 土壤指标列：SOM, TN, TP, pH, 物理粘粒, 物理砂粒等

## 输出结果

### 1. 图表文件
- 县城对比图表
- 时间对比图表  
- 深度时间对比图表
- 深度剖面对比图表

### 2. Excel报告
- 分析概要表
- 统一结果表
- 显著性汇总表
- p值对比表
- 统计方法说明

## 技术特点

1. **模块化设计**：功能分离，易于维护
2. **配置驱动**：灵活的参数配置
3. **异常值处理**：IQR方法自动处理异常值
4. **专业统计**：非参数方法，适合土壤数据
5. **高质量输出**：专业图表和详细报告

## 注意事项

1. 确保数据文件格式正确
2. 检查必需列是否存在
3. 异常值处理默认启用
4. 输出目录会自动创建
5. 大数据集可能需要较长处理时间

## 更新日志

- v1.0: 基础功能实现
- v1.1: 优化数据处理逻辑
- v1.2: 简化函数命名和结构
- v1.3: 改进注释和文档

## 联系方式

如有问题或建议，请联系项目维护者。
