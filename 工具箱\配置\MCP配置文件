{"mcpServers": {"github": {"command": "cmd", "args": ["/c", "npx", "-y", "@modelcontextprotocol/server-github"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "****************************************"}, "disabled": false, "autoApprove": []}, "interactive-feedback-mcp": {"command": "uv", "args": ["--directory", "D:/MCP/interactive-feedback-mcp", "run", "server.py"], "timeout": 600, "autoApprove": ["interactive_feedback"]}, "sequential-thinking": {"command": "cmd", "args": ["/c", "npx", "-y", "@modelcontextprotocol/server-sequential-thinking"]}, "桌面控制-文件系统": {"command": "cmd", "args": ["/c", "npx", "-y", "@wonderwhy-er/desktop-commander"]}, "文件系统": {"command": "cmd", "args": ["/c", "npx", "-y", "@modelcontextprotocol/server-filesystem", "E:\\", "D:\\"]}, "网页获取": {"isActive": true, "command": "uvx", "args": ["mcp-server-fetch"]}, "浏览器自动化": {"command": "cmd", "args": ["/c", "npx", "-y", "@executeautomation/playwright-mcp-server"]}, "联网搜索": {"command": "cmd", "args": ["/c", "npx", "-y", "duckduckgo-mpc-server"]}, "命令行管理": {"command": "cmd", "args": ["/c", "npx", "-y", "mcp-server-commands"]}, "graph-memory": {"command": "cmd", "args": ["/c", "npx", "-y", "@izumisy/mcp-duckdb-memory-server"], "env": {"MEMORY_FILE_PATH": "D:\\Autodesk\\memory.data"}}}}