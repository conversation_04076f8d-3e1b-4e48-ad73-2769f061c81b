# =============================================================================
# TIF提取备用函数 - 用于第四章空间制图
# 
# 本文件包含所有TIF文件相关的提取函数，用于空间预测制图
# 当前的点尺度建模不使用这些函数，但第四章需要时可以调用
# =============================================================================

# --- 环境协变量加载函数（TIF文件） ---
load_environmental_variables <- function(config, county, year) {
  cat("正在加载环境协变量（TIF文件）...\n")
  
  # 构建TIF文件目录路径
  tif_directory <- file.path(config$data_paths$tif_root_directory, as.character(year))
  
  if(!dir.exists(tif_directory)) {
    warning("TIF目录不存在: ", tif_directory)
    return(NULL)
  }
  
  # 递归查找所有TIF文件
  tif_files <- list.files(tif_directory, pattern = "\\.tif$", recursive = TRUE, full.names = TRUE)
  
  if(length(tif_files) == 0) {
    warning("未找到TIF文件: ", tif_directory)
    return(NULL)
  }
  
  cat("找到", length(tif_files), "个TIF文件\n")
  
  # 处理TIF文件（移除年份后缀等）
  processed_files <- process_tif_files(tif_files, year)
  
  if(length(processed_files) == 0) {
    warning("处理后无有效TIF文件")
    return(NULL)
  }
  
  # 加载栅格数据
  env_stack <- tryCatch({
    raster::stack(processed_files)
  }, error = function(e) {
    warning("加载环境变量失败: ", e$message)
    return(NULL)
  })
  
  if(!is.null(env_stack)) {
    cat("成功加载", nlayers(env_stack), "个环境变量\n")
    
    # 清理图层名称（移除年份后缀）
    layer_names <- names(env_stack)
    clean_names <- clean_variable_names(layer_names)
    names(env_stack) <- clean_names
    
    cat("环境变量名称:", paste(clean_names[1:min(5, length(clean_names))], collapse = ", "),
        ifelse(length(clean_names) > 5, "...", ""), "\n")
  }
  
  return(env_stack)
}

# --- TIF文件处理函数 ---
process_tif_files <- function(tif_files, year) {
  
  processed_files <- c()
  
  for(tif_file in tif_files) {
    # 获取文件名（不含路径和扩展名）
    file_name <- tools::file_path_sans_ext(basename(tif_file))
    
    # 移除年份后缀（如果存在）
    clean_name <- gsub(paste0("_", year, "$"), "", file_name)
    clean_name <- gsub(paste0(year, "$"), "", clean_name)
    
    # 检查文件是否可读
    if(file.exists(tif_file)) {
      processed_files <- c(processed_files, tif_file)
    }
  }
  
  return(processed_files)
}

# --- 变量名清理函数 ---
clean_variable_names <- function(layer_names) {
  clean_names <- layer_names
  
  # 移除常见的年份后缀模式
  year_patterns <- c("_\\d{4}$", "\\d{4}$", "_\\d{4}_", "\\d{4}_")
  
  for(pattern in year_patterns) {
    clean_names <- gsub(pattern, "", clean_names)
  }
  
  # 移除多余的下划线
  clean_names <- gsub("_+$", "", clean_names)
  clean_names <- gsub("^_+", "", clean_names)
  
  return(clean_names)
}

# --- TIF环境协变量提取函数（支持多波段深度匹配） ---
extract_environmental_values_tif <- function(layer_data, env_stack, depth_layer_name, config) {
  if(is.null(env_stack) || is.null(layer_data)) {
    return(layer_data)
  }
  
  cat("正在提取环境协变量值（深度层:", depth_layer_name, "）...\n")
  
  # 创建空间点对象
  coordinates <- layer_data[, c(config$column_names$longitude, config$column_names$latitude)]
  
  # 创建空间点对象（简化处理，直接使用地理坐标）
  sp_points <- sp::SpatialPoints(coordinates)
  
  # 获取当前深度层的波段索引
  depth_config <- config$general_options$depth_layers
  current_depth <- depth_config[sapply(depth_config, function(x) x$name == depth_layer_name)]
  
  if(length(current_depth) == 0) {
    warning("未找到深度层配置: ", depth_layer_name)
    band_index <- 1  # 默认使用第一波段
  } else {
    band_index <- current_depth[[1]]$band_index
  }
  
  # 提取环境变量值（支持多波段）
  env_values <- tryCatch({
    extract_multiband_values(env_stack, sp_points, band_index)
  }, error = function(e) {
    warning("环境变量提取失败: ", e$message)
    return(NULL)
  })
  
  if(!is.null(env_values)) {
    # 合并环境变量到数据中
    env_df <- as.data.frame(env_values)
    
    # 设置列名（如果是多波段，添加深度后缀）
    if(ncol(env_df) > 0) {
      if(nlayers(env_stack) > 1) {
        # 多波段文件，为变量名添加深度后缀
        names(env_df) <- paste0(names(env_stack), "_", depth_layer_name)
      } else {
        # 单波段文件，保持原名
        names(env_df) <- names(env_stack)
      }
    }
    
    # 移除全为NA的列
    env_df <- env_df[, colSums(is.na(env_df)) < nrow(env_df), drop = FALSE]
    
    if(ncol(env_df) > 0) {
      layer_data <- cbind(layer_data, env_df)
      cat("成功提取", ncol(env_df), "个环境变量（波段", band_index, "）\n")
    }
  }
  
  return(layer_data)
}

# --- 多波段值提取函数 ---
extract_multiband_values <- function(env_stack, sp_points, band_index) {
  
  n_bands <- nlayers(env_stack)
  
  if(n_bands == 1) {
    # 单波段文件，直接提取
    cat("单波段文件，适用于所有深度层\n")
    return(raster::extract(env_stack, sp_points))
    
  } else {
    # 多波段文件，提取指定波段
    cat("多波段文件，提取第", band_index, "波段（共", n_bands, "波段）\n")
    
    if(band_index > n_bands) {
      warning("波段索引超出范围: ", band_index, " > ", n_bands, "，使用最后一个波段")
      band_index <- n_bands
    }
    
    # 提取指定波段
    target_band <- env_stack[[band_index]]
    return(raster::extract(target_band, sp_points))
  }
}
