# =============================================================================
# 数据处理函数
# =============================================================================

# 配置验证函数
validate_config <- function() {
  cat("=== 配置验证 ===\n")
  
  if(!file.exists(vif_file_path)) {
    stop("❌ VIF筛选结果文件不存在: ", vif_file_path)
  } else {
    cat("✅ VIF筛选结果文件存在\n")
  }
  
  if(!file.exists(change_file_path)) {
    stop("❌ 变化量数据文件不存在: ", change_file_path)
  } else {
    cat("✅ 变化量数据文件存在\n")
  }
  
  if(missing_threshold < 0 || missing_threshold > 1) {
    stop("❌ 缺失值阈值必须在0-1之间")
  }
  
  cat("✅ 配置验证通过\n\n")
}

# 创建输出目录
create_output_dir <- function(dir_path) {
  if (!dir.exists(dir_path)) {
    dir.create(dir_path, recursive = TRUE)
    cat("📁 创建输出目录:", dir_path, "\n")
  } else {
    cat("📁 输出目录已存在:", dir_path, "\n")
  }
}

# 众数函数
get_mode <- function(x) {
  x <- x[!is.na(x)]
  if(length(x) == 0) return(NA)
  ux <- unique(x)
  ux[which.max(tabulate(match(x, ux)))]
}

# 改进的缺失值处理函数
handle_missing_values <- function(data, threshold = 0.3, 
                                 numeric_method = "mean", 
                                 categorical_method = "mode") {
  cat("=== 缺失值处理 ===\n")
  
  original_rows <- nrow(data)
  
  # 计算每行的缺失值比例
  missing_per_row <- rowSums(is.na(data)) / ncol(data)
  rows_to_remove <- missing_per_row > threshold
  
  if(sum(rows_to_remove) > 0) {
    cat("🗑️ 删除", sum(rows_to_remove), "行缺失值比例超过", threshold*100, "%的数据\n")
    data <- data[!rows_to_remove, ]
  }
  
  # 分析剩余数据的缺失值情况
  missing_summary <- colSums(is.na(data))
  missing_cols <- missing_summary[missing_summary > 0]
  
  if(length(missing_cols) > 0) {
    cat("📊 剩余缺失值情况:\n")
    for(col_name in names(missing_cols)) {
      cat("   ", col_name, ":", missing_cols[col_name], "个缺失值\n")
    }
    
    # 处理数值型变量
    numeric_cols <- sapply(data, is.numeric)
    if(any(numeric_cols)) {
      numeric_data <- data[, numeric_cols, drop = FALSE]
      missing_numeric <- colSums(is.na(numeric_data))
      missing_numeric <- missing_numeric[missing_numeric > 0]
      
      if(length(missing_numeric) > 0) {
        cat("🔢 填补数值型变量:\n")
        for(col_name in names(missing_numeric)) {
          if(numeric_method == "mean") {
            fill_value <- mean(numeric_data[[col_name]], na.rm = TRUE)
          } else {
            fill_value <- median(numeric_data[[col_name]], na.rm = TRUE)
          }
          data[[col_name]][is.na(data[[col_name]])] <- fill_value
          cat("   ", col_name, ": 用", round(fill_value, 4), "填补\n")
        }
      }
    }
    
    # 处理分类型变量
    categorical_cols <- !numeric_cols
    if(any(categorical_cols)) {
      categorical_data <- data[, categorical_cols, drop = FALSE]
      missing_categorical <- colSums(is.na(categorical_data))
      missing_categorical <- missing_categorical[missing_categorical > 0]
      
      if(length(missing_categorical) > 0) {
        cat("📝 填补分类型变量:\n")
        for(col_name in names(missing_categorical)) {
          if(categorical_method == "mode") {
            fill_value <- get_mode(categorical_data[[col_name]])
            data[[col_name]][is.na(data[[col_name]])] <- fill_value
            cat("   ", col_name, ": 用'", fill_value, "'填补\n")
          }
        }
      }
    }
  } else {
    cat("✅ 没有缺失值\n")
  }
  
  final_rows <- nrow(data)
  cat("📊 数据行数变化:", original_rows, "→", final_rows, "\n\n")
  
  return(data)
}

# 分析转移模式
analyze_transition_patterns <- function(data) {
  cat("🔍 分析转移模式...\n")

  # 土地利用转移分析
  landuse_1980 <- data$`LandUse-1980`
  landuse_2023 <- data$`LandUse-2023`
  landuse_1980[is.na(landuse_1980)] <- "未知"
  landuse_2023[is.na(landuse_2023)] <- "未知"

  lu_transition_matrix <- table(landuse_1980, landuse_2023)
  cat("土地利用转移矩阵:\n")
  print(lu_transition_matrix)

  # 土壤类型转移分析
  soiltype_1980 <- data$`Soilclass-1980`
  soiltype_2023 <- data$`Soilclass-2023`
  soiltype_1980[is.na(soiltype_1980)] <- "未知"
  soiltype_2023[is.na(soiltype_2023)] <- "未知"

  st_transition_matrix <- table(soiltype_1980, soiltype_2023)
  cat("\n土壤类型转移矩阵:\n")
  print(st_transition_matrix)

  return(list(
    lu_matrix = lu_transition_matrix,
    st_matrix = st_transition_matrix
  ))
}

# 变量分类函数
classify_variables <- function(env_vars) {
  cat("=== 变量分类 ===\n")
  
  # 基于VIF分析脚本的变量分组
  variable_groups <- list(
    climate = c('Air_Temperature_2m_Mean', 'LST', 'Total_Precipitation_Mean', 'Total_Precipitation_Sum',
                'Total_Evaporation_Mean', 'Surface_Net_Solar_Radiation_Mean', 'Surface_Net_Thermal_Radiation_Mean',
                'Wind_U_Component_10m_Mean', 'Wind_V_Component_10m_Mean'),
    terrain = c('250DEM', 'Aspect', 'Slope', 'Plan_Curvature', 'Profile_Curvature',
                'Terrain_Ruggedness_Index', 'Topographic_Position_Index', 'Topographic_Wetness_Index',
                'Valley_Depth', 'Vertical_Distance_to_Channel_Network', 'Flow_Accumulation',
                'Flow_Direction', 'Sink_Route'),
    vegetation = c('NDVI', 'EVI', 'NDWI', 'MNDWI', 'SAVI', 'RVI', 'LAI', 'FAPAR', 'FVC',
                   'NPP', 'GPP', 'BSI', 'IBI', 'Coloration_Index', 'Redness_Index', 'Saturation_Index'),
    human = c('Population', 'GDP', 'NTL', 'Built_Up', 'CLCD'),
    soil_3d = c('bdod', 'cec', 'Soil_Temperature_Mean', 'Soil_Water_Content_Mean'),
    categorical = c('landform', 'lithology', 'soiltype')
  )
  
  # 静态协变量：地形 + 分类变量
  static_vars <- c(variable_groups$terrain, variable_groups$categorical)
  # 动态协变量：气候 + 植被 + 人类活动 + 3D土壤
  dynamic_vars <- c(variable_groups$climate, variable_groups$vegetation, 
                   variable_groups$human, variable_groups$soil_3d)
  
  static_available <- intersect(static_vars, env_vars)
  dynamic_available <- intersect(dynamic_vars, env_vars)
  unclassified <- setdiff(env_vars, c(static_available, dynamic_available))
  
  cat("📊 静态协变量(", length(static_available), "个):", paste(static_available, collapse = ", "), "\n")
  cat("📈 动态协变量(", length(dynamic_available), "个):", paste(dynamic_available, collapse = ", "), "\n")
  
  if(length(unclassified) > 0) {
    cat("❓ 未分类变量:", paste(unclassified, collapse = ", "), "\n")
    dynamic_available <- c(dynamic_available, unclassified)
  }
  
  return(list(
    static = static_available,
    dynamic = dynamic_available,
    groups = variable_groups
  ))
}

# 数据预处理主函数
preprocess_data <- function(vif_data, change_data, response_vars) {
  cat("=== 数据预处理 ===\n")
  
  # 基础信息列
  base_cols <- c("ProfileID", "Longitude", "Latitude", "City", "Location", 
                 "Soilclass-1980", "Soilclass-2023", "LandUse-1980", "LandUse-2023",
                 "深度范围", "深度中点")
  
  # VIF筛选后的环境变量
  env_cols <- setdiff(colnames(vif_data), c(base_cols, response_vars))
  
  # 构建最终数据集
  final_cols <- c(base_cols, response_vars, env_cols)
  available_cols <- intersect(final_cols, colnames(change_data))
  processed_data <- change_data[, available_cols]
  
  cat("📊 数据维度:", nrow(processed_data), "行 ×", ncol(processed_data), "列\n")
  cat("🎯 响应变量:", paste(response_vars, collapse = ", "), "\n")
  cat("🌍 环境变量数量:", length(env_cols), "\n")
  
  # 处理缺失值
  processed_data <- handle_missing_values(
    processed_data, 
    threshold = missing_threshold,
    numeric_method = numeric_missing_method,
    categorical_method = categorical_missing_method
  )
  
  return(list(
    data = processed_data,
    response_vars = response_vars,
    env_vars = env_cols,
    base_vars = base_cols
  ))
}

# 数据预处理函数（包含转移变量）
preprocess_data_with_transitions <- function(vif_data, change_data, response_vars, create_transitions = TRUE) {
  cat("=== 数据预处理（含转移变量）===\n")

  # 基础信息列
  base_cols <- c("ProfileID", "Longitude", "Latitude", "City", "Location",
                 "Soilclass-1980", "Soilclass-2023", "LandUse-1980", "LandUse-2023",
                 "深度范围", "深度中点")

  # VIF筛选后的环境变量
  env_cols <- setdiff(colnames(vif_data), c(base_cols, response_vars))

  # 构建最终数据集
  final_cols <- c(base_cols, response_vars, env_cols)
  available_cols <- intersect(final_cols, colnames(change_data))
  processed_data <- change_data[, available_cols]

  cat("📊 数据维度:", nrow(processed_data), "行 ×", ncol(processed_data), "列\n")
  cat("🎯 响应变量:", paste(response_vars, collapse = ", "), "\n")
  cat("🌍 环境变量数量:", length(env_cols), "\n")

  # 处理缺失值
  processed_data <- handle_missing_values(
    processed_data,
    threshold = missing_threshold,
    numeric_method = numeric_missing_method,
    categorical_method = categorical_missing_method
  )

  # 准备环境变量矩阵
  explanatory_vars <- processed_data[, env_cols, drop = FALSE]

  # 创建转移变量
  transition_patterns <- NULL
  if(create_transitions) {
    cat("\n🔄 创建转移变量...\n")

    # 分析转移模式
    transition_patterns <- analyze_transition_patterns(processed_data)

    # 创建简化的转移变量（分类变量而不是多个0/1变量）
    cat("🔄 创建简化转移变量...\n")

    # 土地利用转移（作为分类变量）
    landuse_1980 <- processed_data$`LandUse-1980`
    landuse_2023 <- processed_data$`LandUse-2023`
    landuse_1980[is.na(landuse_1980)] <- "未知"
    landuse_2023[is.na(landuse_2023)] <- "未知"
    landuse_transition <- paste(landuse_1980, "→", landuse_2023, sep = "")

    # 土壤类型转移（优化版本 - 减少冗余）
    soiltype_1980 <- processed_data$`Soilclass-1980`
    soiltype_2023 <- processed_data$`Soilclass-2023`
    soiltype_1980[is.na(soiltype_1980)] <- "未知"
    soiltype_2023[is.na(soiltype_2023)] <- "未知"
    
    # 创建简化的土壤类型变化指示变量
    soiltype_changed <- ifelse(soiltype_1980 == soiltype_2023, "无变化", "有变化")
    
    # 仅保留主要转移类型（出现频次≥5的组合）
    soiltype_transition <- paste(soiltype_1980, "→", soiltype_2023, sep = "")
    transition_counts <- table(soiltype_transition)
    major_transitions <- names(transition_counts)[transition_counts >= 5]
    soiltype_transition_simplified <- ifelse(
      soiltype_transition %in% major_transitions, 
      soiltype_transition, 
      "其他转移"
    )

    # 创建转移变量数据框（基于最佳实践，仅保留稳定的土地利用转移）
    # 移除土壤转移变量以彻底避免多重共线性问题
    all_transitions <- data.frame(
      LandUse_Transition = as.factor(landuse_transition),
      row.names = rownames(processed_data)
    )
    
    # 记录决策：土壤转移变量存在不可解决的共线性，已移除
    cat("   🔬 基于统计学最佳实践，已移除土壤转移变量以避免共线性\n")

    cat("   土地利用转移类型:", nlevels(all_transitions$LandUse_Transition), "种\n")
    cat("   转移变量将作为因子变量输入RDA（vegan自动处理虚拟变量）\n")

    # 移除常量列
    constant_cols <- sapply(all_transitions, function(x) length(unique(x)) <= 1)
    if(any(constant_cols)) {
      cat("   移除", sum(constant_cols), "个常量变量\n")
      all_transitions <- all_transitions[, !constant_cols, drop = FALSE]
    }

    # 合并到协变量矩阵
    explanatory_vars <- cbind(explanatory_vars, all_transitions)
    cat("   最终协变量矩阵:", nrow(explanatory_vars), "行 ×", ncol(explanatory_vars), "列\n")
  }

  # 更新环境变量列表，排除响应变量和ID列
  actual_env_vars <- setdiff(colnames(processed_data),
                            c(response_vars, "ID", "Depth_Mid", "City", "County"))

  return(list(
    data = processed_data,
    response_vars = response_vars,
    env_vars = actual_env_vars,  # 使用实际存在的列名
    base_vars = base_cols,
    explanatory_matrix = explanatory_vars,
    transition_patterns = transition_patterns
  ))
}
