# ---- 统计分析模块 ----
"""
非参数统计检验和显著性分析
"""

import pandas as pd
from itertools import combinations
import scipy.stats as stats
import scikit_posthocs as sp
from B_config import ALPHA, P_ADJUST

# ---- 基础统计检验函数 ----

def kruskal(data, factor_col, value_col):
    """Kruskal-Wallis H检验"""
    groups = [data[value_col][data[factor_col] == g].dropna()
              for g in data[factor_col].unique()]
    valid_groups = [g for g in groups if len(g) >= 1]

    if len(valid_groups) < 2:
        return None, None

    try:
        return stats.kruskal(*valid_groups)
    except Exception as e:
        print(f"  Kruskal检验错误: {e}")
        return None, None

def mannwhitney(data, factor_col, value_col):
    """Mann-Whitney U检验（两组比较）"""
    groups = data[factor_col].unique()
    if len(groups) != 2:
        return None, None

    g1 = data[value_col][data[factor_col] == groups[0]].dropna()
    g2 = data[value_col][data[factor_col] == groups[1]].dropna()

    if len(g1) < 1 or len(g2) < 1:
        return None, None

    try:
        return stats.mannwhitneyu(g1, g2, alternative='two-sided')
    except Exception as e:
        print(f"  Mann-Whitney检验错误: {e}")
        return None, None

def dunn(data, factor_col, value_col, method=P_ADJUST):
    """执行Dunn事后检验"""
    if data[factor_col].nunique() < 2:
        return None

    try:
        return sp.posthoc_dunn(data, val_col=value_col, group_col=factor_col, p_adjust=method)
    except Exception as e:
        print(f"  Dunn检验错误: {e}")
        return None

def posthoc_analysis(data, factor_col, value_col, alpha=ALPHA, method=P_ADJUST):
    """
    综合事后检验分析，整合Dunn检验和字母标记
    
    Args:
        data: 数据框
        factor_col: 因子列名
        value_col: 数值列名  
        alpha: 显著性水平
        method: p值校正方法
    
    Returns:
        tuple: (dict, pd.DataFrame or None) 包含字母标记的结果字典和p值矩阵
    """
    if data[factor_col].nunique() < 2:
        return {}, None
    
    # 执行Dunn事后检验
    dunn_results = dunn(data, factor_col, value_col, method)
    if dunn_results is None:
        groups = [str(g) for g in data[factor_col].unique()]
        return {g: 'a' for g in groups}, None
    
    # 获取组顺序（按中位数降序）
    ordered_groups = group_order(data, factor_col, value_col)
    
    # 构建p值字典
    p_values_dict = {}
    for i in range(len(ordered_groups)):
        for j in range(i+1, len(ordered_groups)):
            g1, g2 = ordered_groups[i], ordered_groups[j]
            if g1 in dunn_results.index and g2 in dunn_results.columns:
                p_val = dunn_results.loc[g1, g2]
                p_values_dict[(g1, g2)] = p_val
    
    # 生成字母标记
    labels = letters(ordered_groups, p_values_dict, alpha)
    return labels, dunn_results

def group_order(data, factor_col, value_col):
    """按中位数降序获取组顺序"""
    medians = {}
    for group in data[factor_col].unique():
        group_data = data[data[factor_col] == group][value_col].dropna()
        if not group_data.empty:
            medians[str(group)] = group_data.median()

    return [g for g, _ in sorted(medians.items(), key=lambda x: x[1], reverse=True)]

def letters(groups, p_values, alpha=ALPHA):
    """
    使用 Maximal Cliques 生成显著性字母标记（非参数检验版本）。
    返回格式：{group: 'a'} 或 {group: 'ab'} 等。
    """
    if not groups:
        return {}
    if len(groups) == 1:
        return {groups[0]: 'a'}

    # 构建对称 p 值表
    p = {(g1, g2): 1.0 for g1 in groups for g2 in groups}
    for (g1, g2), val in p_values.items():
        p[(g1, g2)] = p[(g2, g1)] = val

    # 找出所有最大非显著差异组（Maximal Cliques）
    cliques = []
    for size in range(len(groups), 0, -1):
        for combo in combinations(groups, size):
            combo_set = frozenset(combo)
            if any(combo_set <= c for c in cliques):
                continue
            if all(p[(a, b)] > alpha for a, b in combinations(combo, 2)):
                cliques.append(combo_set)

    # 按中位数顺序排序 cliques（以最早出现的成员为准）
    cliques.sort(key=lambda c: min(groups.index(g) for g in c))

    # 分配字母
    result = {g: '' for g in groups}
    for letter, clique in zip('abcdefghijklmnopqrstuvwxyz', cliques):
        for g in clique:
            result[g] += letter

    return result


def effect_size(data, factor_col, h_stat):
    """计算Kruskal-Wallis检验的eta²效应量"""
    n = len(data)
    k = data[factor_col].nunique()  # 组数
    
    if h_stat is None or n <= 1 or k <= 1:
        return {'eta_squared': None, 'cohen_f': None, 'interpretation': 'N/A'}

    # 使用文献标准公式: η²[H] = (H - k + 1)/(n - k)
    eta2 = max(0, min((h_stat - k + 1) / (n - k), 1))

    # 根据文献标准划分效应量大小
    if eta2 < 0.01:
        interp = '微小'
    elif eta2 < 0.06:
        interp = '小'
    elif eta2 < 0.14:
        interp = '中等'
    else:
        interp = '大'

    return {'eta_squared': eta2, 'cohen_f': None, 'interpretation': interp}

def analyze(data, factor_col, value_col):
    """执行组间比较分析"""
    result = {
        'factor': factor_col,
        'value': value_col,
        'statistic': None,
        'p_value': None,
        'groups': [],
        'group_data': {},
        'effect_sizes': {},
        'test_method': None
    }

    # 数据清理
    if not pd.api.types.is_numeric_dtype(data[value_col]):
        data = data.copy()
        data[value_col] = pd.to_numeric(data[value_col], errors='coerce')

    clean_data = data.dropna(subset=[value_col])
    if clean_data.empty:
        return result

    # 组信息
    groups = [str(g) for g in clean_data[factor_col].unique()]
    n_groups = len(groups)
    result['groups'] = groups

    # 组统计
    group_stats = {}
    for i, group in enumerate(clean_data[factor_col].unique()):
        values = clean_data[value_col][clean_data[factor_col] == group]
        if not values.empty:
            group_stats[groups[i]] = {
                'n': len(values),
                'mean': values.mean(),
                'median': values.median(),
                'std': values.std(),
                'min': values.min(),
                'max': values.max()
            }
    result['group_data'] = group_stats

    if n_groups < 2:
        return result

    # 选择检验方法
    if n_groups == 2:
        stat, p_val = mannwhitney(clean_data, factor_col, value_col)
        result['test_method'] = 'Mann-Whitney U'
        # 两组比较通常不报告效应量，遵循土壤科学文献惯例
        result['effect_sizes'] = {'eta_squared': None, 'cohen_f': None, 'interpretation': 'N/A'}
    else:
        stat, p_val = kruskal(clean_data, factor_col, value_col)
        result['test_method'] = 'Kruskal-Wallis H'
        # 计算效应量
        if stat is not None:
            result['effect_sizes'] = effect_size(clean_data, factor_col, stat)
        else:
            result['effect_sizes'] = {'eta_squared': None, 'cohen_f': None, 'interpretation': 'N/A'}

    result['statistic'] = stat
    result['p_value'] = p_val

    return result