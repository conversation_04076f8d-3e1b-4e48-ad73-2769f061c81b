# =============================================================================
# R包加载和环境配置
# =============================================================================

cat("=== 检查和加载R包 ===\n")

# 设置CRAN镜像
options(repos = c(CRAN = "https://mirrors.tuna.tsinghua.edu.cn/CRAN/"))

# 核心必需包
core_packages <- c("vegan", "readxl", "dplyr", "openxlsx")

# 可视化包（简化版）
plot_packages <- c("ggplot2", "ggrepel", "RColorBrewer")

# 统计分析包
stat_packages <- c("car", "broom", "tidyr", "scales")

# 检查核心包
missing_core <- c()
for(pkg in core_packages) {
  if(!require(pkg, character.only = TRUE, quietly = TRUE)) {
    missing_core <- c(missing_core, pkg)
    cat("❌ 缺少核心包:", pkg, "\n")
  } else {
    cat("✅ 核心包:", pkg, "\n")
  }
}

if(length(missing_core) > 0) {
  cat("\n⚠️ 正在安装缺少的核心包...\n")
  for(pkg in missing_core) {
    install.packages(pkg, dependencies = TRUE)
    library(pkg, character.only = TRUE)
  }
}

# 检查可视化包
for(pkg in plot_packages) {
  if(!require(pkg, character.only = TRUE, quietly = TRUE)) {
    cat("📦 安装可视化包:", pkg, "\n")
    tryCatch({
      install.packages(pkg, dependencies = TRUE)
      library(pkg, character.only = TRUE)
      cat("✅ 安装成功:", pkg, "\n")
    }, error = function(e) {
      cat("⚠️ 安装失败:", pkg, "\n")
    })
  } else {
    cat("✅ 可视化包:", pkg, "\n")
  }
}

# 检查统计包
for(pkg in stat_packages) {
  if(!require(pkg, character.only = TRUE, quietly = TRUE)) {
    cat("📊 安装统计包:", pkg, "\n")
    tryCatch({
      install.packages(pkg, dependencies = TRUE)
      library(pkg, character.only = TRUE)
      cat("✅ 安装成功:", pkg, "\n")
    }, error = function(e) {
      cat("⚠️ 安装失败:", pkg, "\n")
    })
  } else {
    cat("✅ 统计包:", pkg, "\n")
  }
}

# 设置ggplot2主题（使用系统默认字体，避免字体警告）
theme_set(theme_bw() +
  theme(
    # 使用系统默认字体，避免字体警告
    text = element_text(size = 12),
    plot.title = element_text(size = 16, face = "bold", hjust = 0.5),
    axis.title = element_text(size = 14, face = "bold"),
    axis.text = element_text(size = 12),
    legend.title = element_text(size = 12, face = "bold"),
    legend.text = element_text(size = 11),
    panel.grid.minor = element_blank(),
    strip.text = element_text(size = 12, face = "bold"),
    strip.background = element_rect(fill = "white", color = "black"),
    panel.background = element_rect(fill = "white", colour = "black"),
    plot.background = element_rect(fill = "white", colour = NA)
  ))

# 设置颜色方案
rda_colors <- list(
  primary = "#2E86AB",
  secondary = "#A23B72", 
  accent = "#F18F01",
  warning = "#C73E1D",
  success = "#6A994E",
  neutral = "#577590",
  gradient = c("#2E86AB", "#4A90A4", "#689B9D", "#86A596", "#A4B08F", "#C2BA88"),
  soil_vars = c("#8B4513", "#CD853F", "#DEB887", "#F4A460", "#D2691E"),
  env_vars = c("#228B22", "#32CD32", "#90EE90", "#98FB98", "#00FF7F")
)

cat("✅ 所有包加载完成，主题和颜色配置就绪\n\n")
