{
  "mcpServers": {
    "mcp-feedback-enhanced": {
      "command": "uvx",
      "args": ["mcp-feedback-enhanced@latest"],
      "timeout": 600,
      "env": {
        "MCP_LANGUAGE": "zh-CN",
        "MCP_DESKTOP_MODE": "false"
      },
      "autoApprove": ["interactive_feedback"],
      "disabled": false
    }
  }
}
{
  "mcpServers": {
    "shrimp-task-manager": {
      "command": "npx",
      "args": ["-y", "mcp-shrimp-task-manager"],
      "env": {
        "DATA_DIR": "E:\\05 Python\\Devway\\01 硕士论文\\data",
        "TEMPLATES_USE": "zh",
        "ENABLE_GUI": "false"
      },
      "timeout": 300,
      "disabled": false
    }
  }
}
{
  "mcpServers": {
    "playwright": {
      "command": "npx",
      "args": ["-y", "@playwright/mcp@latest"],
      "env": {
        "PLAYWRIGHT_HEADLESS": "true"
      },
      "timeout": 600,
      "disabled": false
    }
  }
}
{
  "mcpServers": {
    "server-filesystem": {
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-filesystem",
        "E:\\05 Python\\Devway\\01 硕士论文"
      ],
      "env": {},
      "timeout": 180,
      "disabled": false
    }
  }
}
{
  "mcpServers": {
    "context7": {
      "command": "npx",
      "args": ["-y", "@upstash/context7-mcp@latest"],
      "env": {},
      "timeout": 120,
      "disabled": false
    }
  }
}
{
  "mcpServers": {
    "sequential-thinking": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-sequential-thinking@latest"],
      "env": {},
      "timeout": 180,
      "disabled": false
    }
  }
}
