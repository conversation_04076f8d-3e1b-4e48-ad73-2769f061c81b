# =============================================================================
# 统一交叉验证和评估模块
#
# 功能：
# 1. 统一的交叉验证评估（支持所有方法）
# 2. 基于yardstick的标准化评估指标计算
# 3. 简洁高效的代码实现
# =============================================================================

# --- 交叉验证评估函数 ---
perform_cv_evaluation <- function(data, target_var, feature_cols, params, cv_strategy, method, return_full_metrics = FALSE) {

  n_samples <- nrow(data)
  cv_folds <- cv_strategy$folds

  # 存储所有预测结果
  all_observed <- numeric(0)
  all_predicted <- numeric(0)

  # 为k折交叉验证创建折叠索引
  if(cv_strategy$type == "k折") {
    fold_indices <- sample(rep(1:cv_folds, length.out = n_samples))
  }

  for(fold in 1:cv_folds) {
    # 创建训练和测试集（统一逻辑）
    if(cv_strategy$type == "留一法") {
      test_idx <- fold
      train_idx <- setdiff(1:n_samples, test_idx)
    } else {
      test_idx <- which(fold_indices == fold)
      train_idx <- which(fold_indices != fold)
    }

    # 确保训练集和测试集都有数据
    if(length(train_idx) == 0 || length(test_idx) == 0) {
      next
    }

    train_data <- data[train_idx, ]
    test_data <- data[test_idx, ]

    # 统一的模型训练和预测
    pred <- train_and_predict_model(train_data, test_data, target_var, feature_cols, params, method)

    if(!is.null(pred) && length(pred) > 0) {
      all_observed <- c(all_observed, test_data[[target_var]])
      all_predicted <- c(all_predicted, pred)
    }
  }

  # 根据需要返回不同格式
  if(return_full_metrics) {
    # 返回完整的评估指标
    return(calculate_evaluation_metrics(all_observed, all_predicted, method))
  } else {
    # 只返回R²分数（用于调优）
    if(length(all_observed) > 1 && length(all_predicted) > 1) {
      return(rsq_vec(all_observed, all_predicted))
    } else {
      return(-Inf)
    }
  }
}

# --- 统一的模型训练和预测函数 ---
train_and_predict_model <- function(train_data, test_data, target_var, feature_cols, params, method) {

  tryCatch({
    if(method == "random_forest") {
      # 随机森林
      mtry <- max(1, floor(params$mtry_factor * length(feature_cols)))
      rf_formula <- as.formula(paste(target_var, "~", paste(feature_cols, collapse = " + ")))
      model <- randomForest(rf_formula, data = train_data,
                           ntree = params$ntree,
                           nodesize = params$nodesize,
                           mtry = mtry)
      pred <- predict(model, test_data)
      return(as.numeric(pred))

    } else if(method %in% c("regression_kriging", "ordinary_kriging")) {
      # 克里金方法 - 改进的错误处理
      return(train_and_predict_kriging(train_data, test_data, target_var, feature_cols, params, method))
    }

    return(NULL)

  }, error = function(e) {
    return(NULL)
  })
}

# --- 专门的克里金训练预测函数 ---
train_and_predict_kriging <- function(train_data, test_data, target_var, feature_cols, params, method) {

  tryCatch({
    # 预先设置坐标系统
    coordinates(train_data) <- ~ proj_x + proj_y
    coordinates(test_data) <- ~ proj_x + proj_y

    if(method == "regression_kriging") {
      # === 回归克里金：正确的两步法 ===

      # 第一步：拟合回归模型去除趋势
      formula_obj <- as.formula(paste(target_var, "~", paste(feature_cols, collapse = " + ")))

      # 将空间数据转换为普通数据框进行回归
      train_df <- as.data.frame(train_data)
      lm_model <- lm(formula_obj, data = train_df)

      # 计算残差
      train_data$residuals <- residuals(lm_model)

      # 第二步：对残差计算变异函数
      emp_vgm <- variogram(residuals ~ 1, train_data)

      # 拟合变异函数模型
      fit_vgm <- fit.variogram(emp_vgm, vgm(params$vgm_model))

      # 检查变异函数拟合质量
      if(is.null(fit_vgm) || any(is.na(fit_vgm$psill))) {
        return(NULL)
      }

      # 第三步：预测
      # 3.1 用回归模型预测趋势
      test_df <- as.data.frame(test_data)
      trend_pred <- predict(lm_model, newdata = test_df)

      # 3.2 对残差进行克里金预测
      residual_pred <- krige(residuals ~ 1, train_data, test_data, model = fit_vgm,
                            nmax = params$nmax, nmin = params$nmin)

      # 3.3 组合预测结果：趋势 + 残差克里金
      pred <- residual_pred
      pred$var1.pred <- trend_pred + residual_pred$var1.pred

    } else {
      # === 普通克里金：直接对目标变量建立变异函数 ===

      # 构建公式（只有截距）
      formula_obj <- as.formula(paste(target_var, "~ 1"))

      # 计算经验变异函数
      emp_vgm <- variogram(formula_obj, train_data)

      # 拟合变异函数模型
      fit_vgm <- fit.variogram(emp_vgm, vgm(params$vgm_model))

      # 检查变异函数拟合质量
      if(is.null(fit_vgm) || any(is.na(fit_vgm$psill))) {
        return(NULL)
      }

      # 普通克里金预测 - 计算最大搜索距离
      if(!is.null(params$maxdist_factor)) {
        coords <- coordinates(train_data)
        x_range <- diff(range(coords[,1]))
        y_range <- diff(range(coords[,2]))
        maxdist <- params$maxdist_factor * sqrt(x_range^2 + y_range^2)
      } else {
        maxdist <- Inf
      }

      pred <- krige(formula_obj, train_data, test_data, model = fit_vgm,
                   nmax = params$nmax, nmin = params$nmin, maxdist = maxdist)
    }

    return(as.numeric(pred$var1.pred))

  }, error = function(e) {
    # 具体的错误类型处理
    if(grepl("singular|Covariance matrix", e$message)) {
      # 协方差矩阵奇异 - 静默返回NULL
      return(NULL)
    } else if(grepl("convergence|iterations", e$message)) {
      # 变异函数拟合不收敛 - 静默返回NULL
      return(NULL)
    } else if(grepl("zero distance", e$message)) {
      # 零距离问题 - 静默返回NULL
      return(NULL)
    } else {
      # 其他错误 - 静默返回NULL
      return(NULL)
    }
  })
}

# --- 基于yardstick的评估指标计算 ---
calculate_evaluation_metrics <- function(observed, predicted, method_name = "") {

  # 创建数据框并清理数据
  data <- data.frame(
    truth = observed,
    estimate = predicted
  ) %>%
    filter(!is.na(truth), !is.na(estimate), is.finite(truth), is.finite(estimate))

  if(nrow(data) < 2) {
    warning("有效样本数不足，无法计算评估指标")
    return(list(
      r_squared = NA,
      rmse = NA,
      mae = NA,
      ccc = NA,
      rpd = NA,
      n_samples = nrow(data),
      method = method_name
    ))
  }

  # 使用yardstick计算所有指标
  metrics <- data %>%
    summarise(
      r_squared = rsq_vec(truth, estimate),
      rmse = rmse_vec(truth, estimate),
      mae = mae_vec(truth, estimate),
      ccc = ccc_vec(truth, estimate),
      rpd = rpd_vec(truth, estimate),
      correlation = cor(truth, estimate, use = "complete.obs"),
      n_samples = n(),
      method = method_name,
      .groups = 'drop'
    )

  # 转换为列表格式
  result <- as.list(metrics)
  result$residuals <- data$truth - data$estimate
  result$observed <- data$truth
  result$predicted <- data$estimate

  return(result)
}
# --- 统一的评估指标合并函数 ---
merge_evaluation_metrics <- function(params, cv_result, fallback_r2 = NA) {
  if(!is.null(cv_result)) {
    params$r_squared <- cv_result$r_squared
    params$rmse <- cv_result$rmse
    params$mae <- cv_result$mae
    params$ccc <- cv_result$ccc
    params$rpd <- cv_result$rpd
    params$correlation <- cv_result$correlation
  } else {
    # 兜底逻辑：使用提供的R²值，其他指标设为NA
    params$r_squared <- fallback_r2
    params$rmse <- NA
    params$mae <- NA
    params$ccc <- NA
    params$rpd <- NA
    params$correlation <- NA
  }
  return(params)
}

# --- 文件结束 ---
