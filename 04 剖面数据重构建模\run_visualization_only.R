# 单独运行可视化模块测试
cat("=== 测试可视化模块 ===\n")

# 加载配置
library(yaml)
config <- yaml.load_file("02_config.yml")

# 加载可视化模块
source("10_visualization.R")

# 模拟gap_filling_log数据（用于测试）
gap_filling_log <- list()

# 检查是否有性能报告文件
report_path <- file.path(config$data_paths$output_directory, config$gap_filling$report_filename)
cat("检查报告文件:", report_path, "\n")

if(file.exists(report_path)) {
  cat("报告文件存在，开始生成可视化...\n")
  
  # 创建输出目录
  output_dir <- file.path(config$data_paths$output_directory, "可视化结果")
  if(!dir.exists(output_dir)) {
    dir.create(output_dir, recursive = TRUE)
  }
  
  # 只测试性能对比图
  tryCatch({
    performance_plots <- create_performance_comparison_plot(gap_filling_log, config, output_dir)
    cat("✅ 性能对比图生成成功！\n")
  }, error = function(e) {
    cat("❌ 性能对比图生成失败:", e$message, "\n")
  })
  
} else {
  cat("报告文件不存在，请先运行主程序生成数据\n")
}

cat("测试完成\n")
