#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量文件重命名工具
功能：将文件名中的指定字符替换为下划线，递归处理所有子文件夹
"""

import os
import sys
from pathlib import Path

# ==================== 配置区 ====================
CONFIG = {
    # 默认处理目录（如果不通过命令行参数或用户输入指定）
    'default_directory': r"E:\05 Python\Devway\01 硕士论文\00 原始数据\raw data\环境辅助变量",

    # 要替换的字符列表（这些字符将被替换为下划线）
    'chars_to_replace': [' ', '-'],

    # 替换后的字符
    'replacement_char': '_',

    # 是否默认处理文件夹名
    'process_folders_default': False,

    # 是否显示详细日志
    'verbose_logging': True,

    # 是否在预览模式下显示不需要处理的文件
    'show_unchanged_files': False,

    # 文件扩展名过滤（空列表表示处理所有文件，否则只处理指定扩展名）
    'file_extensions_filter': [],  # 例如: ['.txt', '.docx', '.xlsx']

    # 排除的文件夹名称（这些文件夹将被跳过）
    'excluded_folders': ['.git', '.svn', '__pycache__', 'node_modules'],

    # 排除的文件名模式（这些文件将被跳过）
    'excluded_files': ['desktop.ini', 'thumbs.db', '.DS_Store'],

    # 是否在执行前进行二次确认
    'require_confirmation': True,

    # 最大处理文件数量（0表示无限制）
    'max_files_to_process': 0
}
# ===============================================

def should_process_file(filename):
    """检查文件是否应该被处理"""
    # 检查是否在排除列表中
    if filename.lower() in [f.lower() for f in CONFIG['excluded_files']]:
        return False

    # 检查文件扩展名过滤
    if CONFIG['file_extensions_filter']:
        file_ext = os.path.splitext(filename)[1].lower()
        if file_ext not in [ext.lower() for ext in CONFIG['file_extensions_filter']]:
            return False

    # 检查是否包含需要替换的字符
    return any(char in filename for char in CONFIG['chars_to_replace'])

def should_process_folder(foldername):
    """检查文件夹是否应该被处理"""
    # 检查是否在排除列表中
    if foldername.lower() in [f.lower() for f in CONFIG['excluded_folders']]:
        return False

    # 检查是否包含需要替换的字符
    return any(char in foldername for char in CONFIG['chars_to_replace'])

def replace_chars_in_name(name):
    """将名称中的指定字符替换为配置的替换字符"""
    new_name = name
    for char in CONFIG['chars_to_replace']:
        new_name = new_name.replace(char, CONFIG['replacement_char'])
    return new_name

def rename_files_in_directory(directory_path, preview_mode=True, process_folders=None):
    """
    递归遍历目录，将文件名中的指定字符替换为下划线

    参数:
        directory_path: 要处理的目录路径
        preview_mode: 是否为预览模式（True=仅显示将要更改的文件，False=实际执行重命名）
        process_folders: 是否处理文件夹名（None=使用配置默认值）

    返回:
        tuple: (成功处理的文件数, 跳过的文件数, 错误文件数)
    """
    if process_folders is None:
        process_folders = CONFIG['process_folders_default']

    success_count = 0
    skip_count = 0
    error_count = 0
    processed_count = 0

    chars_display = "、".join([f"'{char}'" for char in CONFIG['chars_to_replace']])

    print(f"{'='*60}")
    print(f"{'预览模式' if preview_mode else '执行模式'}: 处理目录 {directory_path}")
    print(f"替换字符: {chars_display} -> '{CONFIG['replacement_char']}'")
    print(f"处理文件夹: {'是' if process_folders else '否'}")
    print(f"{'='*60}")

    # 使用os.walk递归遍历所有文件和文件夹
    for root, dirs, files in os.walk(directory_path):
        # 过滤排除的文件夹
        dirs[:] = [d for d in dirs if d.lower() not in [f.lower() for f in CONFIG['excluded_folders']]]

        if CONFIG['verbose_logging']:
            print(f"\n正在处理目录: {root}")

        # 处理文件
        for filename in files:
            # 检查是否达到最大处理数量限制
            if CONFIG['max_files_to_process'] > 0 and processed_count >= CONFIG['max_files_to_process']:
                print(f"\n⚠️  已达到最大处理文件数量限制: {CONFIG['max_files_to_process']}")
                return success_count, skip_count, error_count

            if should_process_file(filename):
                old_path = os.path.join(root, filename)
                new_filename = replace_chars_in_name(filename)
                new_path = os.path.join(root, new_filename)

                # 检查新文件名是否已存在
                if os.path.exists(new_path):
                    if CONFIG['verbose_logging']:
                        print(f"  ⚠️  跳过: {filename} (目标文件名已存在)")
                    skip_count += 1
                    continue

                if preview_mode:
                    if CONFIG['verbose_logging']:
                        print(f"  📝 将重命名: {filename} -> {new_filename}")
                    success_count += 1
                else:
                    try:
                        os.rename(old_path, new_path)
                        if CONFIG['verbose_logging']:
                            print(f"  ✅ 已重命名: {filename} -> {new_filename}")
                        success_count += 1
                    except Exception as e:
                        if CONFIG['verbose_logging']:
                            print(f"  ❌ 重命名失败: {filename} - 错误: {str(e)}")
                        error_count += 1

                processed_count += 1
            elif CONFIG['show_unchanged_files'] and CONFIG['verbose_logging']:
                print(f"  ⏭️  跳过: {filename} (无需处理)")

        # 处理文件夹名（如果启用）
        if process_folders:
            for dirname in dirs[:]:  # 使用切片复制，避免在迭代时修改列表
                if should_process_folder(dirname):
                    old_dir_path = os.path.join(root, dirname)
                    new_dirname = replace_chars_in_name(dirname)
                    new_dir_path = os.path.join(root, new_dirname)

                    if os.path.exists(new_dir_path):
                        if CONFIG['verbose_logging']:
                            print(f"  ⚠️  跳过文件夹: {dirname} (目标文件夹名已存在)")
                        skip_count += 1
                        continue

                    if preview_mode:
                        if CONFIG['verbose_logging']:
                            print(f"  📁 将重命名文件夹: {dirname} -> {new_dirname}")
                        success_count += 1
                    else:
                        try:
                            os.rename(old_dir_path, new_dir_path)
                            if CONFIG['verbose_logging']:
                                print(f"  ✅ 已重命名文件夹: {dirname} -> {new_dirname}")
                            # 更新dirs列表中的文件夹名，以便后续遍历使用新名称
                            dirs[dirs.index(dirname)] = new_dirname
                            success_count += 1
                        except Exception as e:
                            if CONFIG['verbose_logging']:
                                print(f"  ❌ 重命名文件夹失败: {dirname} - 错误: {str(e)}")
                            error_count += 1

    return success_count, skip_count, error_count

def print_config_info():
    """显示当前配置信息"""
    chars_display = "、".join([f"'{char}'" for char in CONFIG['chars_to_replace']])
    print(f"📋 当前配置:")
    print(f"  - 默认目录: {CONFIG['default_directory']}")
    print(f"  - 替换字符: {chars_display} -> '{CONFIG['replacement_char']}'")
    print(f"  - 默认处理文件夹: {'是' if CONFIG['process_folders_default'] else '否'}")
    if CONFIG['file_extensions_filter']:
        print(f"  - 文件类型过滤: {', '.join(CONFIG['file_extensions_filter'])}")
    else:
        print(f"  - 文件类型过滤: 处理所有文件")
    if CONFIG['excluded_folders']:
        print(f"  - 排除文件夹: {', '.join(CONFIG['excluded_folders'])}")
    if CONFIG['max_files_to_process'] > 0:
        print(f"  - 最大处理文件数: {CONFIG['max_files_to_process']}")

def main():
    """主函数"""
    chars_display = "、".join([f"'{char}'" for char in CONFIG['chars_to_replace']])
    print("批量文件重命名工具")
    print(f"功能：将文件名中的 {chars_display} 替换为 '{CONFIG['replacement_char']}'")
    print("-" * 50)

    # 显示配置信息
    print_config_info()
    print("-" * 50)

    # 获取目标目录
    if len(sys.argv) > 1:
        target_directory = sys.argv[1]
    else:
        default_hint = f"（回车使用配置的默认目录: {CONFIG['default_directory']}）"
        target_directory = input(f"请输入要处理的目录路径{default_hint}: ").strip()
        if not target_directory:
            target_directory = CONFIG['default_directory']

    # 验证目录是否存在
    if not os.path.exists(target_directory):
        print(f"❌ 错误: 目录 '{target_directory}' 不存在！")
        return

    if not os.path.isdir(target_directory):
        print(f"❌ 错误: '{target_directory}' 不是一个目录！")
        return

    # 获取绝对路径
    target_directory = os.path.abspath(target_directory)

    # 询问是否包含文件夹重命名
    default_folders = "y" if CONFIG['process_folders_default'] else "N"
    include_folders = input(f"是否同时重命名文件夹？(y/N，默认: {default_folders}): ").strip().lower()
    if not include_folders:
        process_folders = CONFIG['process_folders_default']
    else:
        process_folders = include_folders in ['y', 'yes', '是']

    if not process_folders:
        print("注意：将只重命名文件，不处理文件夹名")

    # 预览模式
    print("\n🔍 预览模式 - 显示将要进行的更改:")
    success, skip, error = rename_files_in_directory(target_directory, preview_mode=True, process_folders=process_folders)

    print(f"\n📊 预览结果统计:")
    print(f"  - 将要处理的项目: {success}")
    print(f"  - 将要跳过的项目: {skip}")
    print(f"  - 预计错误项目: {error}")

    if success == 0:
        print("\n✨ 没有需要重命名的文件或文件夹！")
        return

    # 确认执行
    if CONFIG['require_confirmation']:
        confirm = input(f"\n❓ 确认执行重命名操作？(y/N): ").strip().lower()
        if confirm not in ['y', 'yes', '是']:
            print("❌ 操作已取消")
            return

    # 执行重命名
    print("\n🚀 开始执行重命名操作...")
    success, skip, error = rename_files_in_directory(target_directory, preview_mode=False, process_folders=process_folders)

    print(f"\n📊 执行结果统计:")
    print(f"  - 成功处理: {success}")
    print(f"  - 跳过项目: {skip}")
    print(f"  - 失败项目: {error}")

    if error == 0:
        print("\n🎉 所有操作完成！")
    else:
        print(f"\n⚠️  完成，但有 {error} 个项目处理失败")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n❌ 用户中断操作")
    except Exception as e:
        print(f"\n❌ 程序执行出错: {str(e)}")
