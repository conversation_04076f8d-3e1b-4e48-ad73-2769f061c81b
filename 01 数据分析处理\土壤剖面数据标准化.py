#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
土壤属性数据标准化脚本

将不同剖面的土壤属性数据按照发生层采样的数据标准化为固定深度区间(0-10、10-30、30-60、60-100)的数据。
根据原始点数选择不同的插值方法:
    - 一个点：判断目标区间是否在原始区间内，是则直接赋值
    - 两个点：使用线性插值
    - 三个点以上：使用PCHIP插值（避免样条插值产生负值的过冲问题）
特别处理跨分层情况。
"""

# --- 导入区 ---
import os
import numpy as np
import pandas as pd
import openpyxl  # 直接导入 openpyxl 以更精确控制 Excel 输出
from scipy.interpolate import interp1d, PchipInterpolator
import warnings

# 忽略 pandas 将来版本中的警告
warnings.simplefilter(action='ignore', category=FutureWarning)

# ======================================================================
# --- 配置区 ---
# ======================================================================

# 1. 文件路径配置
INPUT_FILE_PATH = r"E:\05 Python\Devway\01 硕士论文\00 原始数据\raw data\整理剖面数据完整版.xlsx"
OUTPUT_FILE_PATH = r"E:\05 Python\Devway\01 硕士论文\00 原始数据\标准化-整理剖面数据完整版.xlsx"
# 新增：合并两期数据的输出路径
MERGED_OUTPUT_PATH = r"E:\05 Python\Devway\01 硕士论文\00 原始数据\标准化-两期数据合并.xlsx"

# 2. 需要进行插值标准化的目标变量列表
#    如果保持为空列表 `[]`，脚本将自动处理文件中所有的数值型列。
TARGET_VARS = [
    'pH',  'SOM', 'TN', 'TP', '物理粘粒'
]

# 3. 土壤剖面元数据列名 (根据您的Excel表头填写)
COL_DEPTH_MIN = 'Low_Depth'
COL_DEPTH_MAX = 'Up_Depth'
COL_PROFILE_ID = 'ProfileID'    # 剖面ID列名
COL_MIDPOINT = '土层中点'        # 中点深度列名

# 4. 标准深度区间 (一般无需修改)
STANDARD_DEPTHS = [(0, 10), (10, 30), (30, 60), (60, 100)]

# ======================================================================
# --- 核心代码区 ---
# (通常无需修改以下内容)
# ======================================================================

# --- 工具函数区 ---
def load_data(file_path):
    """
    加载Excel文件中的所有sheet页
    """
    try:
        # sheet_name=None 表示读取所有sheet
        return pd.read_excel(file_path, sheet_name=None)
    except FileNotFoundError:
        print(f"错误: 输入文件未找到 -> {file_path}")
        return None
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return None

def is_point_in_range(point, range_min, range_max):
    """
    判断点是否在区间内（包括端点）
    """
    return range_min <= point <= range_max
    
def format_depth_range(lower, upper):
    """
    格式化深度范围，如 "0-10cm"
    """
    return f"{int(lower)}-{int(upper)}"

# --- 核心功能区 ---
def process_profile_data(df, target_vars):
    """
    将土壤属性数据标准化为固定深度区间
    
    参数:
        df: 包含土壤属性数据的DataFrame
        target_vars: 需要处理的目标变量列名列表
        
    返回:
        标准化后的DataFrame, 以及处理过的变量列表
    """
    if df is None or df.empty:
        print("没有数据可处理")
        return None, []
    
    print("正在处理数据...")
    
    # ----------------- 1. 新的列定义与排序逻辑 (v2) -----------------
    original_cols = df.columns.tolist()
    
    # 检查必须的列
    required_cols = [COL_PROFILE_ID, COL_DEPTH_MIN, COL_DEPTH_MAX, 'year']
    missing_cols = [col for col in required_cols if col not in original_cols]
    if missing_cols:
        print(f"错误: 缺少必要的列: {missing_cols}。请检查输入文件。")
        return None, []

    # 验证 target_vars 是否为空
    if not target_vars:
        print("错误: `TARGET_VARS` 不能为空，请在配置区指定需要处理的属性列。")
        return None, []

    # 按 'year' 列分割基本列和属性列
    year_index = original_cols.index('year')
    base_cols_original = original_cols[:year_index + 1]
    attribute_cols_original = original_cols[year_index + 1:]

    # 确定要保留的基本列 (meta_vars) 和要处理的属性列 (value_vars)
    base_cols_to_remove = [COL_DEPTH_MIN, COL_DEPTH_MAX, COL_MIDPOINT, 'area', 'Layer']
    meta_vars = [col for col in base_cols_original if col not in base_cols_to_remove]
    value_vars = [col for col in attribute_cols_original if col in target_vars]
    
    # 验证指定的属性列是否存在
    missing_vars = [var for var in target_vars if var not in df.columns]
    if missing_vars:
        print(f"警告: 以下指定的目标变量在数据中不存在，将被忽略: {missing_vars}")
    
    # 构建最终的列顺序: 用`深度范围`和`深度中点`替换原始`area`列的位置
    final_col_order = []
    new_depth_cols = ['深度范围', '深度中点']
    
    for col in original_cols:
        if col == 'area':
            final_col_order.extend(new_depth_cols)
        elif col in meta_vars or col in value_vars:
            final_col_order.append(col)
            
    # 如果原始表中没有 'area' 列, 则将新深度列插入到 'ProfileID' 之后作为备选方案
    if 'area' not in original_cols:
        if COL_PROFILE_ID in final_col_order:
            insert_pos = final_col_order.index(COL_PROFILE_ID) + 1
            final_col_order[insert_pos:insert_pos] = new_depth_cols
        else: # 如果连 ProfileID 都没有, 就放最前面
            final_col_order = new_depth_cols + final_col_order

    print(f"将要保留的基本列: {meta_vars}")
    print(f"将要处理的属性列: {value_vars}")
    print(f"最终输出列顺序: {final_col_order}")
    # -----------------------------------------------------------

    # 获取所有剖面编号
    profile_ids = df[COL_PROFILE_ID].unique()
    print(f"共找到{len(profile_ids)}个剖面")
    
    # 创建结果DataFrame
    result_data = []
    
    # --- 处理每个剖面 ---
    for profile_id in profile_ids:
        profile_data = df[df[COL_PROFILE_ID] == profile_id].copy()
        
        # 获取该剖面的土层最小/最大深度
        layer_min_depths = profile_data[COL_DEPTH_MIN].values
        layer_max_depths = profile_data[COL_DEPTH_MAX].values
        midpoints = profile_data[COL_MIDPOINT].values
        
        if len(layer_min_depths) == 0:
            continue
            
        # 计算该剖面的总深度范围
        min_depth = min(layer_min_depths)
        max_depth = max(layer_max_depths)
        
        # 获取该剖面的其他元数据列值（使用第一行数据）
        meta_values = {col: profile_data.iloc[0][col] for col in meta_vars}
        
        # 为每个标准深度区间创建记录
        for lower, upper in STANDARD_DEPTHS:
            mid = (lower + upper) / 2
            
            # 初始化记录数据 (已移除 Layer)
            record = {
                '深度范围': format_depth_range(lower, upper),
                '深度中点': mid,
            }
            record.update(meta_values)
            
            # 特别处理：如果标准区间未完全包含在剖面范围内，则不进行插值，赋值为空
            if lower < min_depth or upper > max_depth:
                for var in value_vars:
                    record[var] = None
                result_data.append(record)
                continue
            
            # 检查标准区间是否完全在某个土层内
            inside_layer = False
            layer_idx = -1
            for i, (min_d, max_d) in enumerate(zip(layer_min_depths, layer_max_depths)):
                if min_d <= lower and upper <= max_d:
                    # 标准区间完全包含在这个土层内
                    inside_layer = True
                    layer_idx = i
                    break
            
            if inside_layer:
                # 标准区间完全在某个土层内，直接使用该土层的值
                for var in value_vars:
                    if pd.api.types.is_numeric_dtype(profile_data[var]):
                        value = profile_data.iloc[layer_idx][var]
                        record[var] = float(f'{value:.4g}') if pd.notna(value) else None
                    else:
                        record[var] = profile_data.iloc[layer_idx][var]
            else:
                # 标准区间跨越多个土层，需要插值
                for var in value_vars:
                    property_values = profile_data[var].values
                    
                    if pd.api.types.is_numeric_dtype(profile_data[var]):
                        valid_indices = ~pd.isna(property_values)
                        valid_midpoints = midpoints[valid_indices]
                        valid_values = property_values[valid_indices]

                        if len(valid_midpoints) == 1:
                            single_point_index = np.where(valid_indices)[0][0]
                            single_layer_min = layer_min_depths[single_point_index]
                            single_layer_max = layer_max_depths[single_point_index]

                            if single_layer_min <= lower and upper <= single_layer_max:
                                value = valid_values[0]
                                record[var] = float(f'{value:.4g}') if pd.notna(value) else None
                            else:
                                record[var] = None
                        elif len(valid_midpoints) == 2:
                            try:
                                sort_idx = np.argsort(valid_midpoints)
                                f = interp1d(valid_midpoints[sort_idx], valid_values[sort_idx], 
                                                        bounds_error=False, fill_value=None)
                                interp_value = f(mid)
                                record[var] = float(f'{interp_value:.4g}') if pd.notna(interp_value) else None
                            except Exception as e:
                                print(f"线性插值错误 ({profile_id}, {var}): {e}")
                                record[var] = None
                        elif len(valid_midpoints) >= 3:
                            try:
                                # 使用 PCHIP (分段三次Hermite插值) 代替样条插值
                                # PCHIP 可以保留数据的单调性，避免产生不符合物理意义的负值或过冲现象
                                sort_idx = np.argsort(valid_midpoints)
                                f = PchipInterpolator(valid_midpoints[sort_idx], valid_values[sort_idx])
                                interp_value = f(mid)
                                record[var] = float(f'{interp_value:.4g}') if pd.notna(interp_value) else None
                            except Exception as e:
                                print(f"PCHIP插值错误 ({profile_id}, {var}): {e}")
                                record[var] = None
                        else:
                            record[var] = None
                    else:
                        # 对于非数值型属性列（虽然不常见），保留第一个值
                        record[var] = property_values[0] if len(property_values) > 0 else None
            
            result_data.append(record)
    
    if not result_data:
        print("处理结果为空，请检查数据格式")
        return None, []

    result_df = pd.DataFrame(result_data)
    
    # 按照预先定义的最终顺序排列列
    # 确保所有在final_col_order中的列都存在于DataFrame中，以避免KeyError
    final_cols_exist = [col for col in final_col_order if col in result_df.columns]
    result_df = result_df[final_cols_exist]
    
    return result_df, value_vars

# --- 直接使用 openpyxl 写入 Excel ---
def write_to_excel(results_dict, output_path):
    """
    使用 openpyxl 直接写入 Excel 文件，保证空值为真正的空
    
    参数:
        results_dict: {sheet_name: (dataframe, processed_vars)} 的字典
        output_path: 输出文件路径
    """
    try:
        # 创建一个新的工作簿
        wb = openpyxl.Workbook()
        # 删除默认的 Sheet
        if "Sheet" in wb.sheetnames:
            del wb["Sheet"]
        
        for sheet_name, (df, processed_vars) in results_dict.items():
            # 创建新工作表
            ws = wb.create_sheet(title=sheet_name)
            
            # 写入表头
            for col_idx, col_name in enumerate(df.columns, start=1):
                ws.cell(row=1, column=col_idx, value=col_name)
            
            # 写入数据
            for row_idx, row in enumerate(df.itertuples(index=False), start=2):
                for col_idx, value in enumerate(row, start=1):
                    # 这里是关键：只有非 None 和非 NaN 的值才写入
                    # 留空的单元格将是真正的空（不是空字符串或其他）
                    if value is not None and not (isinstance(value, float) and np.isnan(value)):
                        ws.cell(row=row_idx, column=col_idx, value=value)
            
            print(f"Sheet '{sheet_name}' 处理完成，结果已写入。")
            print("\n结果概况:")
            print(f"总剖面数: {df[COL_PROFILE_ID].nunique()}")
            print(f"总记录数: {df.shape[0]}")
            
            if processed_vars:
                print(f"\n各指标缺失值比例:")
                for col in processed_vars:
                    if col in df.columns:
                        null_count = df[col].isnull().sum()
                        null_percent = null_count / len(df) * 100
                        print(f"{col}: {null_percent:.1f}%")
        
        # 保存工作簿
        wb.save(output_path)
        print(f"\n所有Sheet处理完毕，标准化结果已统一保存到: {output_path}")
    
    except Exception as e:
        print(f"错误: 无法保存文件到 {output_path}。")
        print(f"原因: {e}")

# 新增：将两期数据合并为一个表格
def create_merged_excel(results_dict, output_path):
    """
    创建一个新的Excel文件，将两期数据合并到一个sheet中
    
    参数:
        results_dict: {sheet_name: (dataframe, processed_vars)} 的字典
        output_path: 输出文件路径
    """
    if len(results_dict) < 2:
        print("警告: 合并表格需要至少两个sheet数据，但只找到了 {len(results_dict)} 个，跳过合并操作。")
        return
        
    try:
        # 提取所有的DataFrame
        all_dfs = [(name, df) for name, (df, _) in results_dict.items()]
        
        # 确认所有DataFrame有相同的列
        columns_sets = [set(df.columns) for _, df in all_dfs]
        if not all(cols == columns_sets[0] for cols in columns_sets):
            print("警告: 不同sheet的列不一致，可能导致数据合并后错位。继续合并...")
        
        # 创建一个新的工作簿
        wb = openpyxl.Workbook()
        # 使用默认Sheet并重命名
        ws = wb.active
        ws.title = "两期数据合并"
        
        # 获取第一个DataFrame
        first_sheet_name, first_df = all_dfs[0]
        
        # 写入表头（只写一次）
        for col_idx, col_name in enumerate(first_df.columns, start=1):
            ws.cell(row=1, column=col_idx, value=col_name)
        
        # 写入所有数据，第一个DataFrame从第2行开始
        current_row = 2
        
        # 写入所有DataFrame的数据
        for sheet_name, df in all_dfs:
            print(f"正在合并 '{sheet_name}' 的数据...")
            for row in df.itertuples(index=False):
                for col_idx, value in enumerate(row, start=1):
                    if value is not None and not (isinstance(value, float) and np.isnan(value)):
                        ws.cell(row=current_row, column=col_idx, value=value)
                current_row += 1
        
        # 保存工作簿
        wb.save(output_path)
        print(f"合并数据已保存到: {output_path}")
        print(f"总行数: {current_row - 1}")
    
    except Exception as e:
        print(f"错误: 合并表格失败。")
        print(f"原因: {e}")

# --- 主程序区 ---
def main():
    """
    主函数
    """
    print(f"准备读取文件: {INPUT_FILE_PATH}")
    all_sheets = load_data(INPUT_FILE_PATH)
    
    if all_sheets:
        print(f"文件读取成功，共找到 {len(all_sheets)} 个sheet页: {list(all_sheets.keys())}")
        
        # 确保输出目录存在
        for path in [OUTPUT_FILE_PATH, MERGED_OUTPUT_PATH]:
            output_dir = os.path.dirname(path)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir)
                print(f"已创建输出目录: {output_dir}")

        # 存储处理结果的字典 {sheet_name: (dataframe, processed_vars)}
        results_dict = {}
        
        for sheet_name, df in all_sheets.items():
            print(f"\n--- 开始处理Sheet: '{sheet_name}' ---")
            
            if df.empty:
                print(f"Sheet '{sheet_name}' 为空，跳过处理。")
                continue
                
            print(f"原始数据: {df.shape[0]} 行, {df.shape[1]} 列")
            
            result_df, processed_vars = process_profile_data(df, TARGET_VARS)
            
            if result_df is not None and not result_df.empty:
                results_dict[sheet_name] = (result_df, processed_vars)
            else:
                print(f"Sheet '{sheet_name}' 处理后无有效结果，不写入文件。")
        
        # 使用 openpyxl 直接写入 Excel
        if results_dict:
            # 1. 输出原始的标准化结果
            write_to_excel(results_dict, OUTPUT_FILE_PATH)
            
            # 2. 创建合并的Excel表格
            create_merged_excel(results_dict, MERGED_OUTPUT_PATH)
        else:
            print("没有有效结果可写入，请检查数据和处理逻辑。")

# --- 程序入口 ---
if __name__ == "__main__":
    main()