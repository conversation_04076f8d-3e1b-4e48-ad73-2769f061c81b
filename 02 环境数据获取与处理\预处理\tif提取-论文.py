import rasterio
from rasterio.warp import calculate_default_transform, reproject, Resampling
import pandas as pd
import geopandas as gpd
from shapely.geometry import Point
import numpy as np
import os
import re
from tqdm import tqdm

# ---- 配置 ----
# 在这里配置你的文件路径和列名
CONFIG = {
    # 输入点数据文件 (Excel)
    "points_file": r"E:\05 Python\Devway\01 硕士论文\00 原始数据\标准化-两期数据合并.xlsx",
    
    # 使用的工作表。可以是工作表名称（如 'Sheet1'）或索引（0代表第一个表, 1代表第二个表）
    "sheet_name": 0,
    
    # 包含TIF栅格文件夹的基础路径 (例如, 如果你的TIF在 'D:\TIFs\1980' 和 'D:\TIFs\2023'，这里就填 'D:\TIFs')
    "raster_base_folder": r"E:\05 Python\Devway\01 硕士论文\00 原始数据\raw data\环境辅助变量",
    
    # 输出Excel文件的路径
    "output_excel": r"E:\05 Python\Devway\01 硕士论文\00 原始数据\辅助变量-两期数据.xlsx",
    
    # 点数据的坐标系 (例如 WGS84)
    "points_crs": 'EPSG:4326',
    
    # 列名配置
    "lon_col": 'Longitude',      # 经度列名
    "lat_col": 'Latitude',       # 纬度列名
    "year_col": 'year',          # 年份列名 (重要：你的Excel中必须有此列)
    "depth_col": '深度范围',    # 深度列名，用于多波段栅格

    # --- 多波段处理策略 (新功能) ---
    # 对于多波段TIF，当Excel中的深度在TIF中无直接对应波段时，是否启用最近深度匹配。
    # True: 启用。会自动查找并使用最接近的可用深度波段的值。
    # False: 禁用。未直接匹配的深度将填充为空值(NaN)。
    "use_nearest_depth": True
}

def _generate_column_name(raster_path):
    """从栅格路径生成一致的列名，去除年份等后缀。"""
    base_name = os.path.splitext(os.path.basename(raster_path))[0]
    # 移除文件名末尾的四位数字年份，例如 bio1_1980, bio1-2023, bio11850
    # 使用 \d{4}$ 匹配任意四位数字年份，使其更通用
    column_name = re.sub(r'[_\s-]?(\d{4})$', '', base_name)
    return column_name.strip()

def _parse_start_depth(depth_str):
    """从 '0-10' 或 '10' 这样的字符串中提取起始深度作为整数。"""
    try:
        # 使用正则表达式查找开头的数字
        match = re.match(r'^\s*(\d+)', str(depth_str))
        if match:
            return int(match.group(1))
        return float('inf') # 如果没有找到数字，返回无穷大以便排序
    except (ValueError, IndexError):
        return float('inf') # 出错时也返回无穷大

def _find_best_band_for_depth(point_depth_str, depth_to_band_map, use_nearest):
    """
    为给定的点深度字符串找到最佳的波段索引和对应的波段深度。
    1. 尝试直接匹配。
    2. 如果失败且 use_nearest 为 True，则寻找最近的波段。
    """
    # 1. 尝试直接匹配
    point_depth_str = str(point_depth_str)
    if point_depth_str in depth_to_band_map:
        return depth_to_band_map[point_depth_str], point_depth_str

    # 2. 如果不使用最近值匹配，或没有可用的波段，则返回None
    if not use_nearest or not depth_to_band_map:
        return None, None

    # 3. 寻找最近的波段
    point_start_depth = _parse_start_depth(point_depth_str)
    
    # 如果无法解析点的深度，则无法匹配
    if point_start_depth == float('inf'):
        return None, None

    available_bands = []
    for band_depth_str, band_index in depth_to_band_map.items():
        band_start_depth = _parse_start_depth(band_depth_str)
        if band_start_depth != float('inf'):
            available_bands.append({
                "str": band_depth_str,
                "index": band_index,
                "distance": abs(point_start_depth - band_start_depth)
            })

    if not available_bands:
        return None, None

    # 找到距离最小的那个波段
    closest_band = min(available_bands, key=lambda x: x['distance'])
    
    return closest_band['index'], closest_band['str']


def extract_values_from_raster(raster_path, points_df, config):
    """
    从栅格文件提取指定点位置的值。
    - 对单波段TIF：直接提取第一个波段的值。
    - 对多波段TIF：优先直接匹配深度，若失败且'use_nearest_depth'为True，则匹配最近深度。
    """
    lon_col = config['lon_col']
    lat_col = config['lat_col']
    depth_col = config.get('depth_col')
    points_crs = config['points_crs']
    filename = os.path.basename(raster_path)

    try:
        with rasterio.open(raster_path) as src:
            # --- 1. 初始化和坐标转换 ---
            raster_crs = src.crs
            band_count = src.count
            nodata_value = src.nodata

            geometry = [Point(xy) for xy in zip(points_df[lon_col], points_df[lat_col])]
            gdf = gpd.GeoDataFrame(points_df, geometry=geometry, crs=points_crs)

            if gdf.crs != raster_crs:
                gdf = gdf.to_crs(raster_crs)

            values = []
            is_multiband = band_count > 1

            # --- 2. 核心逻辑：区分单/多波段 ---
            if not is_multiband:
                # --- 单波段文件处理路径 ---
                band1_data = src.read(1)
                for point in gdf.geometry:
                    try:
                        row, col = src.index(point.x, point.y)
                        value = band1_data[row, col]
                        if nodata_value is not None and value == nodata_value:
                            values.append(np.nan)
                        else:
                            values.append(float(value))
                    except IndexError:
                        values.append(np.nan)
            else:
                # --- 多波段文件处理路径 ---
                if not depth_col or depth_col not in gdf.columns:
                    print(f"错误: 多波段文件 '{filename}' 需要一个有效的深度列 (在CONFIG中配置的'{depth_col}')。")
                    return [np.nan] * len(points_df), is_multiband

                unique_excel_depths = points_df[depth_col].astype(str).unique()
                sorted_excel_depths = sorted(unique_excel_depths, key=_parse_start_depth)
                
                # 建立TIF文件自身的深度-波段映射
                tif_depth_map = {
                    depth: i + 1 
                    for i, depth in enumerate(sorted_excel_depths) 
                    if _parse_start_depth(depth) != float('inf') and (i + 1) <= band_count
                }

                # 为Excel中的每个深度找到最佳匹配波段（直接或最近）
                point_depth_to_band_map = {}
                remapped_depths_log = {}
                use_nearest = config.get("use_nearest_depth", False)
                for point_depth in unique_excel_depths:
                    band_index, matched_band_depth = _find_best_band_for_depth(point_depth, tif_depth_map, use_nearest)
                    point_depth_to_band_map[point_depth] = band_index
                    if band_index is not None and point_depth != matched_band_depth:
                        if matched_band_depth not in remapped_depths_log:
                            remapped_depths_log[matched_band_depth] = []
                        remapped_depths_log[matched_band_depth].append(point_depth)

                # 预加载数据
                band_data_cache = {}
                required_bands = set(idx for idx in point_depth_to_band_map.values() if idx is not None)
                for band_idx in sorted(list(required_bands)):
                    try:
                        band_data_cache[band_idx] = src.read(band_idx)
                    except IndexError:
                        print(f"警告: 尝试读取文件 '{filename}' 的波段 {band_idx} 失败，该波段不存在。")

                unmapped_depths = set()
                for index, point in gdf.geometry.items():
                    try:
                        depth_str = str(points_df.loc[index, depth_col])
                        band_index = point_depth_to_band_map.get(depth_str)

                        if band_index and band_index in band_data_cache:
                            row, col = src.index(point.x, point.y)
                            value = band_data_cache[band_index][row, col]
                        else:
                            unmapped_depths.add(depth_str)
                            values.append(np.nan)
                            continue

                        if nodata_value is not None and value == nodata_value:
                            values.append(np.nan)
                        else:
                            values.append(float(value))
                    except IndexError:
                        values.append(np.nan)
                
                if remapped_depths_log:
                    print(f"信息: 在 {filename} 中，部分深度被自动映射到最近的可用波段:")
                    for band_depth, point_depths in remapped_depths_log.items():
                        print(f"  - 波段 '{band_depth}' 的值被用于以下Excel深度: {sorted(list(set(point_depths)))}")
                if unmapped_depths:
                    print(f"警告: 在文件 {filename} 中, 以下Excel深度最终未找到任何可用波段: {sorted(list(unmapped_depths))}")

        return values, is_multiband

    except Exception as e:
        print(f"处理文件 {os.path.basename(raster_path)} 时出错: {str(e)}")
        return [np.nan] * len(points_df), False


def process_multiple_rasters(config):
    """
    处理文件夹中的多个TIF文件并将结果保存到Excel。
    - 新逻辑: 根据Excel中的年份列，从对应年份的文件夹中提取数据。
    """
    # 从配置中获取路径和列名
    raster_base_folder = config['raster_base_folder']
    points_file = config['points_file']
    output_excel = config['output_excel']
    lon_col = config['lon_col']
    lat_col = config['lat_col']
    year_col = config['year_col']
    sheet_name = config.get('sheet_name', 0)
    
    # --- 1. 读取和验证点数据 ---
    try:
        points_df = pd.read_excel(points_file, sheet_name=sheet_name)
        print(f"从工作表 '{sheet_name}' 读取点数据完成，共 {len(points_df)} 个点")
    except FileNotFoundError:
        print(f"错误: 找不到点文件 {points_file}")
        return
    except Exception as e:
        print(f"读取点文件时出错: {str(e)}")
        return

    required_cols = [lon_col, lat_col, year_col]
    if not all(col in points_df.columns for col in required_cols):
        print(f"错误：输入的DataFrame必须包含以下列: {required_cols}")
        return

    # --- 2. 按年份对点数据分组 ---
    points_df[year_col] = points_df[year_col].astype(str)
    grouped_points = points_df.groupby(year_col)
    unique_years = points_df[year_col].unique()
    print(f"在数据中找到以下年份: {list(unique_years)}")

    # --- 3. 预先确定所有可能的输出列名 ---
    all_column_names = set()
    for year in unique_years:
        year_raster_folder = os.path.join(raster_base_folder, year)
        if not os.path.isdir(year_raster_folder):
            continue
        for root, _, files in os.walk(year_raster_folder):
            for file in files:
                if file.lower().endswith(('.tif', '.tiff')):
                    raster_path = os.path.join(root, file)
                    all_column_names.add(_generate_column_name(raster_path))
    
    if not all_column_names:
        print("错误: 在所有年份的栅格文件夹中均未找到任何TIF文件。请检查 'raster_base_folder' 配置。")
        return
    
    print(f"将为以下变量提取值: {sorted(list(all_column_names))}")

    # --- 4. 初始化结果DataFrame ---
    results_df = pd.DataFrame(index=points_df.index, columns=sorted(list(all_column_names)))
    processed_multiband_files = set() # 用于记录处理过的多波段文件名

    # --- 5. 按年份分组处理 ---
    for year, group_df in grouped_points:
        print(f"\n--- 正在处理年份: {year} ({len(group_df)}个点) ---")
        year_raster_folder = os.path.join(raster_base_folder, year)

        if not os.path.isdir(year_raster_folder):
            print(f"警告: 找不到年份 {year} 对应的栅格文件夹: {year_raster_folder}。跳过此年份。")
            continue

        # 获取该年份的所有tif文件
        tif_paths = []
        for root, _, files in os.walk(year_raster_folder):
            for file in files:
                if file.lower().endswith(('.tif', '.tiff')):
                    tif_paths.append(os.path.join(root, file))
        
        if not tif_paths:
            print(f"警告: 在文件夹 '{year_raster_folder}' 中没有找到TIF文件。")
            continue

        print(f"找到 {len(tif_paths)} 个TIF文件，开始为年份 {year} 提取数据...")

        # 使用tqdm显示进度条
        for raster_path in tqdm(tif_paths, desc=f"提取 {year} 年TIF数据"):
            column_name = _generate_column_name(raster_path)
            
            # 提取值 (只对当前年份的点进行操作)
            values, was_multiband = extract_values_from_raster(raster_path, group_df, config)
            if was_multiband:
                processed_multiband_files.add(os.path.basename(raster_path))

            # 将结果填充到主结果DataFrame的相应位置
            if values and len(values) == len(group_df):
                results_df.loc[group_df.index, column_name] = values

    # --- 6. 合并和保存结果 ---
    print("\n所有年份值提取完毕，正在合并到最终表格中...")
    try:
        final_df = pd.concat([points_df, results_df], axis=1)
    except Exception as e:
        print(f"合并数据时出错: {e}")
        return

    # 确保输出目录存在
    output_dir = os.path.dirname(output_excel)
    if output_dir and not os.path.exists(output_dir):
        try:
            os.makedirs(output_dir)
            print(f"已创建输出目录: {output_dir}")
        except Exception as e:
            print(f"创建输出目录时出错: {str(e)}")
            output_excel = os.path.join(os.getcwd(), os.path.basename(output_excel))
            print(f"已更改输出路径为: {output_excel}")

    # 保存到Excel
    try:
        final_df.to_excel(output_excel, index=False)
        print(f"处理完成！结果已保存到: {output_excel}")
    except Exception as e:
        print(f"保存Excel文件时出错: {str(e)}")
        try:
            fallback_path = os.path.join(os.getcwd(), f"points_with_values_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}.xlsx")
            final_df.to_excel(fallback_path, index=False)
            print(f"已保存结果到备用位置: {fallback_path}")
        except Exception as e2:
            print(f"保存到备用位置时也出错: {str(e2)}")

    # --- 7. 打印执行摘要 ---
    print("\n" + "="*50)
    print("脚本执行摘要")
    print("="*50)
    if processed_multiband_files:
        print(f"本次运行共处理了 {len(processed_multiband_files)} 个多波段栅格文件:")
        for fname in sorted(list(processed_multiband_files)):
            print(f"  - {fname}")
    else:
        print("本次运行未处理任何多波段栅格文件。")
    print("="*50)


if __name__ == "__main__":
    # 处理所有TIF文件并保存结果
    process_multiple_rasters(CONFIG)