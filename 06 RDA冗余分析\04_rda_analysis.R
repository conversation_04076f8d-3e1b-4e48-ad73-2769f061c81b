# =============================================================================
# RDA分析核心函数
# =============================================================================

# RDA分析函数
perform_rda_analysis <- function(response_data, explanatory_data, condition_data = NULL, 
                                layer_name = "全部数据", permutations = 999) {
  cat("\n📊 执行RDA分析:", layer_name, "\n")
  
  # 检查数据完整性
  if(nrow(response_data) != nrow(explanatory_data)) {
    stop("响应变量和解释变量的行数不匹配")
  }
  
  # 移除常量列
  constant_cols <- sapply(explanatory_data, function(x) length(unique(x[!is.na(x)])) <= 1)
  if(any(constant_cols)) {
    cat("🗑️ 移除常量列:", paste(names(explanatory_data)[constant_cols], collapse = ", "), "\n")
    explanatory_data <- explanatory_data[, !constant_cols, drop = FALSE]
  }
  
  if(ncol(explanatory_data) == 0) {
    cat("⚠️ 没有有效的解释变量\n")
    return(NULL)
  }
  
  # 执行RDA（使用前向选择优化）
  cat("🔍 使用前向选择优化变量组合...\n")

  if(is.null(condition_data) || ncol(condition_data) == 0) {
    # 无条件RDA + 前向选择
    rda_full <- rda(response_data ~ ., data = explanatory_data)
    rda_result <- ordistep(rda(response_data ~ 1, data = explanatory_data),
                          scope = formula(rda_full),
                          direction = "forward",
                          permutations = 199)
    cat("✅ 无条件RDA + 前向选择完成\n")
  } else {
    # 条件RDA（暂不使用前向选择，避免复杂性）
    rda_result <- rda(response_data, explanatory_data, condition_data)
    cat("✅ 条件RDA完成，条件变量数:", ncol(condition_data), "\n")
  }
  
  # 显著性检验
  cat("🔬 进行显著性检验...\n")
  overall_test <- anova(rda_result, permutations = permutations)
  axis_test <- anova(rda_result, by = "axis", permutations = permutations)
  terms_test <- anova(rda_result, by = "terms", permutations = permutations)
  
  # 计算解释度
  if(length(rda_result$CCA$eig) > 0) {
    total_var <- sum(rda_result$CA$eig) + sum(rda_result$CCA$eig)
    constrained_var <- sum(rda_result$CCA$eig)
    explained_variance <- constrained_var / total_var * 100
  } else {
    explained_variance <- 0
  }
  
  cat("📈 总解释度:", round(explained_variance, 2), "%\n")
  cat("📊 约束轴数:", length(rda_result$CCA$eig), "\n")
  cat("📊 非约束轴数:", length(rda_result$CA$eig), "\n")
  
  # 计算各轴解释度
  if(length(rda_result$CCA$eig) > 0) {
    axis_variance <- rda_result$CCA$eig / sum(rda_result$CCA$eig) * explained_variance
    names(axis_variance) <- paste0("RDA", 1:length(axis_variance))
  } else {
    axis_variance <- numeric(0)
  }
  
  return(list(
    rda_model = rda_result,
    overall_test = overall_test,
    axis_test = axis_test,
    terms_test = terms_test,
    explained_variance = explained_variance,
    axis_variance = axis_variance,
    layer_name = layer_name,
    sample_size = nrow(response_data),
    variable_count = ncol(explanatory_data),
    explanatory_data = explanatory_data  # 添加环境变量数据用于envfit
  ))
}

# 分层RDA分析函数
perform_stratified_rda <- function(processed_data, depth_layers, use_county = TRUE) {
  cat("\n=== 分层RDA分析 ===\n")
  
  results <- list()
  
  for(layer_name in names(depth_layers)) {
    depth_range <- depth_layers[[layer_name]]
    cat("\n🔍 分析深度层:", layer_name, "(", depth_range[1], "-", depth_range[2], "cm)\n")
    
    # 检查深度中点列是否存在
    if(!"深度中点" %in% colnames(processed_data$data)) {
      cat("⚠️ 数据中没有找到'深度中点'列，跳过分层分析\n")
      cat("可用列名:", paste(colnames(processed_data$data), collapse = ", "), "\n")
      return(NULL)
    }

    # 筛选当前深度层的数据（基于精确的深度中点值）
    depth_values <- processed_data$data$深度中点

    # 使用精确匹配深度中点值
    if(length(depth_range) == 1) {
      # 新的精确匹配方式
      depth_filter <- depth_values == depth_range[1]
    } else {
      # 兼容旧的范围匹配方式
      if(layer_name == "60-100cm") {
        depth_filter <- depth_values >= depth_range[1] & depth_values <= depth_range[2]
      } else {
        depth_filter <- depth_values >= depth_range[1] & depth_values < depth_range[2]
      }
    }

    # 处理缺失值
    depth_filter[is.na(depth_filter)] <- FALSE

    # 检查筛选结果
    if(length(depth_filter) == 0 || sum(depth_filter, na.rm = TRUE) == 0) {
      cat("⚠️ 深度层", layer_name, "没有匹配的数据\n")
      cat("深度范围:", depth_range[1], "-", depth_range[2], "cm\n")
      cat("实际深度值:", paste(unique(depth_values[!is.na(depth_values)]), collapse = ", "), "\n")
      next
    }

    layer_data <- processed_data$data[depth_filter, ]

    if(is.null(layer_data) || nrow(layer_data) == 0) {
      cat("⚠️ 深度层", layer_name, "筛选后没有数据\n")
      next
    }
    
    cat("📊 当前层样本数:", nrow(layer_data), "\n")
    
    # 准备响应变量矩阵
    response_matrix <- layer_data[, processed_data$response_vars, drop = FALSE]
    response_matrix <- response_matrix[complete.cases(response_matrix), , drop = FALSE]
    
    if(nrow(response_matrix) == 0) {
      cat("⚠️ 深度层", layer_name, "没有完整的响应变量数据\n")
      next
    }
    
    # 准备解释变量（使用预处理好的协变量矩阵）
    layer_row_indices <- which(rownames(processed_data$explanatory_matrix) %in% rownames(response_matrix))
    all_explanatory <- processed_data$explanatory_matrix[layer_row_indices, , drop = FALSE]

    # 确保行名匹配
    all_explanatory <- all_explanatory[rownames(response_matrix), , drop = FALSE]

    # 处理数值型变量的缺失值（简单处理）
    for(i in 1:ncol(all_explanatory)) {
      if(is.numeric(all_explanatory[, i])) {
        all_explanatory[is.na(all_explanatory[, i]), i] <- mean(all_explanatory[, i], na.rm = TRUE)
      }
    }
    
    # 准备条件变量（县域）
    condition_vars <- NULL
    if(use_county && county_column_name %in% colnames(layer_data)) {
      county_data <- layer_data[rownames(response_matrix), county_column_name, drop = FALSE]
      unique_counties <- unique(county_data[[county_column_name]])
      
      if(length(unique_counties) > 1) {
        formula_str <- paste("~", county_column_name, "- 1")
        condition_vars <- model.matrix(as.formula(formula_str), data = county_data)
        cat("🏘️ 使用县域作为条件变量，县数:", ncol(condition_vars), "\n")
      } else {
        cat("🏘️ 只有一个县，跳过县域条件变量\n")
      }
    }
    
    # 执行RDA分析
    rda_result <- perform_rda_analysis(
      response_data = response_matrix,
      explanatory_data = all_explanatory,
      condition_data = condition_vars,
      layer_name = layer_name,
      permutations = permutation_tests
    )
    
    if(!is.null(rda_result)) {
      results[[layer_name]] <- rda_result
    }
  }
  
  return(results)
}

# 整体RDA分析函数
perform_overall_rda <- function(processed_data, use_county = TRUE) {
  cat("\n=== 整体RDA分析 ===\n")
  
  # 准备响应变量矩阵
  response_matrix <- processed_data$data[, processed_data$response_vars, drop = FALSE]
  response_matrix <- response_matrix[complete.cases(response_matrix), , drop = FALSE]
  
  cat("📊 整体分析样本数:", nrow(response_matrix), "\n")
  
  # 准备解释变量（使用预处理好的协变量矩阵）
  all_explanatory <- processed_data$explanatory_matrix[rownames(response_matrix), , drop = FALSE]

  # 处理数值型变量的缺失值（简单处理）
  for(i in 1:ncol(all_explanatory)) {
    if(is.numeric(all_explanatory[, i])) {
      all_explanatory[is.na(all_explanatory[, i]), i] <- mean(all_explanatory[, i], na.rm = TRUE)
    }
  }
  
  # 准备条件变量（县域）
  condition_vars <- NULL
  if(use_county && county_column_name %in% colnames(processed_data$data)) {
    county_data <- processed_data$data[rownames(response_matrix), county_column_name, drop = FALSE]
    unique_counties <- unique(county_data[[county_column_name]])
    
    if(length(unique_counties) > 1) {
      formula_str <- paste("~", county_column_name, "- 1")
      condition_vars <- model.matrix(as.formula(formula_str), data = county_data)
      cat("🏘️ 使用县域作为条件变量，县数:", ncol(condition_vars), "\n")
    } else {
      cat("🏘️ 只有一个县，跳过县域条件变量\n")
    }
  }
  
  # 执行RDA分析
  rda_result <- perform_rda_analysis(
    response_data = response_matrix,
    explanatory_data = all_explanatory,
    condition_data = condition_vars,
    layer_name = "整体数据",
    permutations = permutation_tests
  )
  
  return(rda_result)
}
