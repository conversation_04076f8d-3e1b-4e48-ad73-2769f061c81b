# ---- 数据加载与预处理 ----
"""
简化的数据加载和预处理模块
"""

import pandas as pd
import B_config as config
from B_config import DATA_PATH, SHEET_NAME, INDICATORS, DEPTH_ORDER, OUTLIER_CONFIG
        
def load_data():
    """加载原始数据"""
    print(f"加载数据: {DATA_PATH}")
    try:
        df = pd.read_excel(DATA_PATH, sheet_name=SHEET_NAME)
        print(f"成功加载 {len(df)} 行数据")
        return df
    except Exception as e:
        print(f"加载失败: {e}")
        return pd.DataFrame()
    
def validate_data(df):
    """验证数据"""
    if df.empty:
        raise ValueError("数据为空")
    
    required_cols = ['City', 'year']
    missing_cols = [col for col in required_cols if col not in df.columns]
    if missing_cols:
        raise ValueError(f"缺少必要列: {missing_cols}")
    
    df = df.copy()
    df['year'] = df['year'].astype(int)
    df['City'] = df['City'].astype(str)
    
    if '深度范围' in df.columns:
        df['深度范围'] = df['深度范围'].astype(str)
    else:
        print("警告: 缺少深度范围列")
    
    return df
    
def extract_config(df):
    """提取配置信息"""
    print("识别配置...")
    
    counties = sorted(df['City'].dropna().unique().tolist())
    config.COUNTIES = counties
    print(f"县城: {counties}")
    
    years = sorted(df['year'].dropna().unique().astype(int).tolist())
    config.YEARS = years
    print(f"年份: {years}")
    
    if '深度范围' in df.columns:
        depths_in_data = df['深度范围'].dropna().unique().tolist()
        depths = [d for d in DEPTH_ORDER if d in depths_in_data]
        depths.extend([d for d in depths_in_data if d not in depths])
        config.DEPTHS = depths
        print(f"深度: {depths}")
    else:
        config.DEPTHS = []
    
def group_data(df):
    """按年份和县城分组"""
    grouped = {}
    
    for year in config.YEARS:
        grouped[year] = {}
        year_data = df[df['year'] == year]
        print(f"{year}年: {len(year_data)}行")
        
        for county in config.COUNTIES:
            county_data = year_data[year_data['City'] == county].copy()
            grouped[year][county] = county_data
            
            if not county_data.empty:
                print(f"  {county}: {len(county_data)}行")
    
    return grouped
    
def handle_outliers(data, col):
    """处理异常值"""
    if data.empty or col not in data.columns:
        return data, {}
    
    if not OUTLIER_CONFIG.get('enabled', True):
        return data, {'enabled': False}
    
    Q1 = data[col].quantile(0.25)
    Q3 = data[col].quantile(0.75)
    IQR = Q3 - Q1
    
    if IQR <= 0:
        return data.copy(), {'enabled': True, 'outliers': 0}
    
    multiplier = OUTLIER_CONFIG['iqr_multiplier']
    lower = Q1 - multiplier * IQR
    upper = Q3 + multiplier * IQR
    
    processed = data.copy()
    outlier_mask = (data[col] < lower) | (data[col] > upper)
    outlier_count = outlier_mask.sum()
    
    if outlier_count > 0:
        processed.loc[data[col] < lower, col] = lower
        processed.loc[data[col] > upper, col] = upper
    
    stats = {
        'enabled': True,
        'outliers': outlier_count,
        'percentage': (outlier_count / len(data) * 100) if len(data) > 0 else 0
    }
    
    return processed, stats
    
def get_indicators(grouped_data):
    """获取可用指标"""
    all_cols = set()
    for year_data in grouped_data.values():
        for df in year_data.values():
            if not df.empty:
                all_cols.update(df.columns)
    
    indicators = {name: name for name in INDICATORS if name in all_cols}
    missing = [name for name in INDICATORS if name not in all_cols]
    
    if missing:
        print(f"缺失指标: {missing}")
    
    return indicators
    
def preprocess_indicators(grouped_data, indicators):
    """按指标预处理"""
    print("\n预处理数据...")
    processed = {}
    
    for year, year_data in grouped_data.items():
        processed[year] = {}
        
        for county, df in year_data.items():
            if df.empty:
                processed[year][county] = {col: pd.DataFrame() for col in indicators.values()}
                continue
            
            # 深度筛选
            if '深度范围' in df.columns and config.DEPTHS:
                df = df[df['深度范围'].isin(config.DEPTHS)]
            
            print(f"处理 {year}-{county}...")
            processed[year][county] = {}
            
            for name, col in indicators.items():
                if col in df.columns and not df.empty:
                    clean_df, stats = handle_outliers(df.copy(), col)
                    processed[year][county][col] = clean_df
                    
                    if stats.get('outliers', 0) > 0:
                        print(f"  {name}: 处理 {stats['outliers']} 个异常值")
                else:
                    processed[year][county][col] = pd.DataFrame()
    
    return processed
    
def init_config():
    """初始化配置"""
    try:
        df = load_data()
        if df.empty:
            return False
        
        validated_df = validate_data(df)
        extract_config(validated_df)
        return True
    except Exception as e:
        print(f"配置初始化失败: {e}")
        return False

def preprocess_data():
    """数据预处理主入口"""
    try:
        df = load_data()
        if df.empty:
            return {}, {}
        
        validated_df = validate_data(df)
        extract_config(validated_df)
        grouped_data = group_data(validated_df)
        indicators = get_indicators(grouped_data)
        processed_data = preprocess_indicators(grouped_data, indicators)
        
        return processed_data, indicators
        
    except Exception as e:
        print(f"数据预处理失败: {e}")
        return {}, {}