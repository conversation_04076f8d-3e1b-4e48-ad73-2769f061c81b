# ---- 配置文件 ----
"""
土壤数据时空变化分析配置
包含数据路径、分析参数、图表样式等配置
"""

# ================= 数据源配置 =================
DATA_PATH = r"E:\05 Python\Devway\01 硕士论文\00 原始数据\嫩江_模型预测数据\嫩江_完整数据.xlsx"
SHEET_NAME = 0

OUTPUT_DIR = r"E:\05 Python\Devway\01 硕士论文\00 原始数据\嫩江_方差分析"
EXCEL_DIR = r"E:\05 Python\Devway\01 硕士论文\00 原始数据\嫩江_方差分析"
EXCEL_FILENAME = "方差分析结果.xlsx"

# 兼容性配置
plot_types = {
    'spatial': True,
    'temporal': True, 
    'depth_time': True,
    'depth_profile': True
}

# ================= 数据结构配置 =================
# 数据列名
COLUMNS = {
    'county': 'City',
    'year': 'year', 
    'depth': '深度范围'
}

# 分析维度
COUNTIES = ['凤城', '科左', '梨树', '嫩江', '铁岭']
YEARS = [1980, 2023]
DEPTHS = ['0-10', '10-30', '30-60', '60-100']
DEPTH_ORDER = DEPTHS  # 兼容性别名

# 分析指标
INDICATORS = {
    'pH': '',
    'SOM': '(g/kg)',
    'TN': '(g/kg)',
    'TP': '(g/kg)', 
    '物理粘粒': '(%)',
    '黑土层厚度': 'cm'
}

# ================= 分析类型配置 =================
ANALYSIS_TYPES = {
    'spatial': {
        'name': '县城对比',
        'group_by': 'county',
        'split_by': 'year',
        'filter_by': None,
        'key_format': '{year}_{indicator}',
        'output_dir': '县城对比'
    },
    'temporal': {
        'name': '时间对比',
        'group_by': 'year', 
        'split_by': 'county',
        'filter_by': None,
        'key_format': '{county}_{indicator}',
        'output_dir': '时间对比'
    },
    'depth_time': {
        'name': '深度时间对比',
        'group_by': 'year',
        'split_by': 'county', 
        'filter_by': 'depth',
        'key_format': '{county}_{indicator}_{depth}',
        'output_dir': '深度时间对比'
    },
    'depth_profile': {
        'name': '深度剖面对比',
        'group_by': 'depth',
        'split_by': 'county',
        'filter_by': 'year', 
        'key_format': '{year}_{county}_{indicator}_by_depth',
        'output_dir': '深度剖面对比'
    }
}

# ================= 分析控制配置 =================
# 分析范围: "all" 或县城列表
ANALYSIS_SCOPE = ["嫩江"]

# 启用的分析类型
ENABLED_ANALYSES = ['spatial', 'temporal', 'depth_time', 'depth_profile']

# 统计参数
ALPHA = 0.05
P_ADJUST = 'fdr_bh'
GENERATE_PVALUE_SHEET = True

# ================= 图表配置 =================
# 图表尺寸
FIG_SIZES = {
    'spatial': (20, 12),
    'temporal': (18, 12), 
    'depth_time': (24, 14),
    'depth_profile': (18, 12)
}

# 布局
LAYOUT = {
    'dpi': 500,
    'wspace': 0.4,  # 子图间水平间距
    'hspace': 0.5,  # 子图间垂直间距
    'title_y': 0.98,  # 提高总标题位置，避免与图表重叠
    'left': 0.08,
    'right': 0.95,
    'top': 0.92,  # 调整顶部边距
    'bottom': 0.08  # 调整底部边距
}

# 字体
FONTS = {
    'base': 20,
    'title': 28,
    'label': 24,
    'letter': 20
}

# 颜色和样式
STYLE = {
    'palette': 'Set2',  # 更换为Set2配色方案，更美观
    'show_outliers': True,
    'background': 'white',
    'text_color': 'black'
}

# 异常值处理配置
OUTLIER_CONFIG = {
    'enabled': True,  # 是否启用异常值处理（True: 启用, False: 禁用）
    'method': 'iqr',  # 使用IQR方法
    'iqr_multiplier': 1.5,  # IQR方法的倍数
    'action': 'clip',  # 'clip' - 将异常值截断到边界值
}

# 箱线图参数
BOXPLOT_STYLE = {
    'boxprops': {'edgecolor': 'black', 'linewidth': 1.0},
    'medianprops': {'color': 'black', 'linewidth': 1.5},
    'whiskerprops': {'color': 'black', 'linewidth': 1.0},
    'capprops': {'color': 'black', 'linewidth': 1.0},
    'flierprops': {'marker': 'o', 'markerfacecolor': 'gray', 'markersize': 4,
                  'alpha': 1.0 if STYLE['show_outliers'] else 0.0}
}