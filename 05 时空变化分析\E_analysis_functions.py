# ---- 数据分析函数模块 ----
"""
通用数据筛选和分析函数，为每种图生成清晰的表格
"""

import pandas as pd
from B_config import (
    COLUMNS, COUNTIES, YEARS, DEPTHS,
    ANALYSIS_TYPES,ALPHA
)
from D_statistics import analyze, posthoc_analysis
from statsmodels.stats.multitest import multipletests

# ---- 辅助函数 ----

def create_summary_row(result, indicator_name, indicator_col, extra_cols=None):
    """创建汇总行"""
    row = {
        '指标': indicator_name,
        '指标代码': indicator_col,
        '检验方法': result.get('test_method', 'N/A'),
        '统计量': round(result.get('statistic', 0), 4) if result.get('statistic') else 'N/A',
        'P值': round(result.get('p_value', 1), 6) if result.get('p_value') else 'N/A',
        '是否显著': '是' if result.get('p_value', 1) < ALPHA else '否',
        '组数': len(result.get('groups', [])),
        '效应量': result.get('effect_sizes', {}).get('interpretation', 'N/A')
    }
    if extra_cols:
        row.update(extra_cols)
    return row

def add_group_stats(summary_row, result, groups, suffix=''):
    """添加组统计信息"""
    group_data = result.get('group_data', {})
    for group in groups:
        if group in group_data:
            stats = group_data[group]
            prefix = f'{group}{suffix}' if suffix else group
            summary_row[f'{prefix}_样本数'] = stats.get('n', 0)
            summary_row[f'{prefix}_中位数'] = round(stats.get('median', 0), 4) if stats.get('median') else 'N/A'
            summary_row[f'{prefix}_均值'] = round(stats.get('mean', 0), 4) if stats.get('mean') else 'N/A'
            summary_row[f'{prefix}_显著性字母'] = result.get('labels', {}).get(str(group), '-')

def get_temporal_data(processed_data, county, indicator):
    """重构时间分析数据"""
    dfs = []
    for year in YEARS:
        df = processed_data.get(year, {}).get(county, {}).get(indicator)
        if df is not None and not df.empty:
            temp_df = df.copy()
            if COLUMNS['year'] not in temp_df.columns:
                temp_df[COLUMNS['year']] = year
            dfs.append(temp_df)
    return pd.concat(dfs, ignore_index=True) if dfs else pd.DataFrame()

def get_spatial_data(processed_data, year, indicator):
    """重构空间分析数据"""
    dfs = []
    for county in COUNTIES:
        df = processed_data.get(year, {}).get(county, {}).get(indicator)
        if df is not None and not df.empty:
            temp_df = df.copy()
            if COLUMNS['county'] not in temp_df.columns:
                temp_df[COLUMNS['county']] = county
            dfs.append(temp_df)
    return pd.concat(dfs, ignore_index=True) if dfs else pd.DataFrame()

def get_depth_data(processed_data, year, county, indicator):
    """重构深度剖面数据"""
    df = processed_data.get(year, {}).get(county, {}).get(indicator)
    return df.copy() if df is not None and not df.empty else pd.DataFrame()

def get_depth_time_data(processed_data, county, indicator, depth):
    """重构深度时间数据"""
    dfs = []
    for year in YEARS:
        df = processed_data.get(year, {}).get(county, {}).get(indicator)
        if df is not None and not df.empty:
            depth_df = df[df[COLUMNS['depth']] == depth].copy()
            if not depth_df.empty:
                if COLUMNS['year'] not in depth_df.columns:
                    depth_df[COLUMNS['year']] = year
                dfs.append(depth_df)
    return pd.concat(dfs, ignore_index=True) if dfs else pd.DataFrame()

def perform_posthoc(processed_data, result_key, result):
    """执行事后检验并生成显著性字母"""
    try:
        # 解析分析类型
        if result_key.startswith('depth_profile_'):
            analysis_type = 'depth_profile'
        elif result_key.startswith('depth_time_'):
            analysis_type = 'depth_time'
        elif result_key.startswith('temporal_'):
            analysis_type = 'temporal'
        elif result_key.startswith('spatial_'):
            analysis_type = 'spatial'
        else:
            analysis_type = result_key.split('_')[0]
        
        # 根据分析类型获取数据
        if analysis_type == 'temporal':
            parts = result_key.split('_')
            county = parts[1]
            indicator = parts[2]
            combined_data = get_temporal_data(processed_data, county, indicator)
            factor_col = COLUMNS['year']
            
        elif analysis_type == 'spatial':
            parts = result_key.split('_')
            year = int(parts[1])
            indicator = parts[2]
            combined_data = get_spatial_data(processed_data, year, indicator)
            factor_col = COLUMNS['county']
            
        elif analysis_type == 'depth_profile':
            parts = result_key.split('_')
            year = int(parts[2])
            county = parts[3]
            indicator = parts[4]
            combined_data = get_depth_data(processed_data, year, county, indicator)
            factor_col = COLUMNS['depth']
            
        elif analysis_type == 'depth_time':
            parts = result_key.split('_')
            county = parts[2]
            indicator = parts[3]
            depth = parts[4].replace('cm', '')
            combined_data = get_depth_time_data(processed_data, county, indicator, depth)
            factor_col = COLUMNS['year']
        else:
            return {str(g): 'a' for g in result.get('groups', [])}, None
        
        if combined_data.empty:
            return {str(g): 'a' for g in result.get('groups', [])}, None
        
        # 使用综合事后检验函数，接收字母和p值矩阵
        labels, p_matrix = posthoc_analysis(combined_data, factor_col, indicator, alpha=ALPHA)
        return labels, p_matrix
        
    except Exception as e:
        print(f"    警告: 事后检验失败: {e}")
        return {str(g): 'a' for g in result.get('groups', [])}, None

def assign_two_groups_labels(result):
    """为两组分配显著性字母"""
    groups = result.get('groups', [])
    group_data = result.get('group_data', {})
    
    if len(groups) != 2 or not group_data:
        return {str(g): 'a' for g in groups}
    
    # 获取两组的中位数
    medians = {}
    for group in groups:
        if group in group_data:
            median_val = group_data[group].get('median')
            if median_val is not None and not pd.isna(median_val):
                medians[str(group)] = median_val
    
    if len(medians) != 2:
        return {groups[0]: 'a', groups[1]: 'b'}
    
    # 按中位数分配字母
    sorted_groups = sorted(medians.items(), key=lambda x: x[1], reverse=True)
    return {sorted_groups[0][0]: 'a', sorted_groups[1][0]: 'b'}

def apply_correction(processed_data, results, analysis_type):
    """对分析结果应用p值校正和显著性字母分配"""
    if not results:
        return
    
    print(f"    应用 FDR-BH p值校正...")
    
    # 收集有效的p值
    raw_p_values = []
    valid_keys = []
    
    for key, result in results.items():
        if result and result.get('p_value') is not None and not pd.isna(result['p_value']):
            raw_p_values.append(result['p_value'])
            valid_keys.append(key)
    
    if not raw_p_values:
        # 为无效结果设置默认值
        for key, result in results.items():
            result.update({
                'p_value_corrected': result.get('p_value'),
                'significant_corrected': False,
                'labels': {str(g): '-' for g in result.get('groups', [])}
            })
        return
    
    # 应用BH校正
    reject, pvals_corrected, _, _ = multipletests(raw_p_values, alpha=ALPHA, method='fdr_bh')
    
    # 更新结果
    for i, key in enumerate(valid_keys):
        result = results[key]
        result['p_value_corrected'] = pvals_corrected[i]
        result['significant_corrected'] = reject[i]
        
        # 生成显著性字母
        groups = result.get('groups', [])
        result['p_value_matrix'] = None # 初始化
        
        if reject[i] and len(groups) > 2:
            # 多组显著差异，执行事后检验
            result['labels'], result['p_value_matrix'] = perform_posthoc(processed_data, key, result)
        elif reject[i] and len(groups) == 2:
            # 两组显著差异，根据中位数分配a和b
            result['labels'] = assign_two_groups_labels(result)
        else:
            # 不显著，所有组标记为'a'
            result['labels'] = {str(g): 'a' for g in groups}

# ---- 主要分析函数 ----

def spatial_analysis(processed_data, available_indicators, filter_counties):
    """空间分析：不同县城在相同年份的比较"""
    print("  正在进行空间差异分析（县城间比较）...")
    
    results = {}
    summary_data = []
    
    for year in YEARS:
        print(f"    分析 {year} 年...")
        
        for indicator_name, indicator_col in available_indicators.items():
            # 收集该年份所有县城的数据
            indicator_dfs = []
            for county in COUNTIES:
                df = processed_data.get(year, {}).get(county, {}).get(indicator_col)
                if df is not None and not df.empty:
                    temp_df = df.copy()
                    if COLUMNS['county'] not in temp_df.columns:
                        temp_df[COLUMNS['county']] = county
                    indicator_dfs.append(temp_df)
            
            if len(indicator_dfs) >= 2:  # 至少需要2个县城
                combined_df = pd.concat(indicator_dfs, ignore_index=True)
                result = analyze(combined_df, COLUMNS['county'], indicator_col)
                
                if result and result.get('p_value') is not None:
                    result_key = f"spatial_{year}_{indicator_col}"
                    results[result_key] = result
                    
                    # 汇总表格
                    summary_row = create_summary_row(
                        result, indicator_name, indicator_col, 
                        {'年份': year}
                    )
                    
                    # 添加组统计信息
                    add_group_stats(summary_row, result, result.get('groups', []))
                    
                    summary_data.append(summary_row)
    
    # 对结果进行p值校正
    apply_correction(processed_data, results, 'spatial')
    
    # 生成汇总表格
    if summary_data:
        summary_table = pd.DataFrame(summary_data)
        summary_table = summary_table.sort_values(['年份', '指标代码'])
    else:
        summary_table = pd.DataFrame()
    
    return {
        'results': results,
        'summary_table': summary_table,
        'analysis_type': 'spatial',
        'title': '空间差异分析表格（县城间比较）'
    }

def temporal_analysis(processed_data, available_indicators, filter_counties):
    """时间分析：相同县城在不同年份的比较"""
    print(f"  正在进行时间变化分析（{len(filter_counties)}个县城）...")
    
    results = {}
    summary_data = []
    
    for county in filter_counties:
        print(f"    分析 {county} 县...")
        
        for indicator_name, indicator_col in available_indicators.items():
            # 收集该县城所有年份的数据
            indicator_dfs = []
            for year in YEARS:
                df = processed_data.get(year, {}).get(county, {}).get(indicator_col)
                if df is not None and not df.empty:
                    temp_df = df.copy()
                    if COLUMNS['year'] not in temp_df.columns:
                        temp_df[COLUMNS['year']] = year
                    indicator_dfs.append(temp_df)
            
            if len(indicator_dfs) >= 2:  # 至少需要2个年份
                combined_df = pd.concat(indicator_dfs, ignore_index=True)
                result = analyze(combined_df, COLUMNS['year'], indicator_col)
                
                if result and result.get('p_value') is not None:
                    result_key = f"temporal_{county}_{indicator_col}"
                    results[result_key] = result
                    
                    # 汇总表格
                    summary_row = create_summary_row(
                        result, indicator_name, indicator_col,
                        {'县城': county}
                    )
                    
                    # 添加组统计信息
                    groups = sorted(result.get('groups', []), key=lambda x: int(x) if str(x).isdigit() else 0)
                    add_group_stats(summary_row, result, groups, suffix='年')
                    
                    summary_data.append(summary_row)
    
    # 对结果进行p值校正
    apply_correction(processed_data, results, 'temporal')
    
    # 生成汇总表格
    if summary_data:
        summary_table = pd.DataFrame(summary_data)
        summary_table = summary_table.sort_values(['县城', '指标代码'])
    else:
        summary_table = pd.DataFrame()
    
    return {
        'results': results,
        'summary_table': summary_table,
        'analysis_type': 'temporal',
        'title': '时间变化分析表格（年际比较）'
    }

def depth_time_analysis(processed_data, available_indicators, filter_counties):
    """深度时间分析：相同县城相同深度在不同年份的比较"""
    print(f"  正在进行深度时间变化分析（{len(filter_counties)}县城 × {len(DEPTHS)}深度层）...")
    
    results = {}
    summary_data = []
    
    for county in filter_counties:
        for depth in DEPTHS:
            print(f"    分析 {county}县 {depth}cm 深度...")
            
            for indicator_name, indicator_col in available_indicators.items():
                # 收集该县城该深度所有年份的数据
                indicator_dfs = []
                for year in YEARS:
                    df = processed_data.get(year, {}).get(county, {}).get(indicator_col)
                    if df is not None and not df.empty:
                        # 筛选深度
                        depth_df = df[df[COLUMNS['depth']] == depth].copy()
                        if not depth_df.empty:
                            if COLUMNS['year'] not in depth_df.columns:
                                depth_df[COLUMNS['year']] = year
                            indicator_dfs.append(depth_df)
                
                if len(indicator_dfs) >= 2:  # 至少需要2个年份
                    combined_df = pd.concat(indicator_dfs, ignore_index=True)
                    result = analyze(combined_df, COLUMNS['year'], indicator_col)
                    
                    if result and result.get('p_value') is not None:
                        result_key = f"depth_time_{county}_{indicator_col}_{depth}cm"
                        results[result_key] = result
                        
                        # 汇总表格
                        summary_row = create_summary_row(
                            result, indicator_name, indicator_col,
                            {'县城': county, '深度(cm)': depth}
                        )
                        
                        # 添加组统计信息
                        groups = sorted(result.get('groups', []), key=lambda x: int(x) if str(x).isdigit() else 0)
                        add_group_stats(summary_row, result, groups, suffix='年')
                        
                        summary_data.append(summary_row)
    
    # 对结果进行p值校正
    apply_correction(processed_data, results, 'depth_time')
    
    # 生成汇总表格
    if summary_data:
        summary_table = pd.DataFrame(summary_data)
        summary_table = summary_table.sort_values(['县城', '深度(cm)', '指标代码'])
    else:
        summary_table = pd.DataFrame()
    
    return {
        'results': results,
        'summary_table': summary_table,
        'analysis_type': 'depth_time',
        'title': '深度时间变化分析表格（深度-年际比较）'
    }

def depth_profile_analysis(processed_data, available_indicators, filter_counties):
    """深度剖面分析：相同年份相同县城在不同深度的比较"""
    print(f"  正在进行深度剖面分析（{len(YEARS)}年份 × {len(filter_counties)}县城）...")
    
    results = {}
    summary_data = []
    
    for year in YEARS:
        for county in filter_counties:
            print(f"    分析 {year}年 {county}县...")
            
            for indicator_name, indicator_col in available_indicators.items():
                # 获取该年份该县城所有深度的数据
                df = processed_data.get(year, {}).get(county, {}).get(indicator_col)
                
                if df is not None and not df.empty and COLUMNS['depth'] in df.columns:
                    # 检查是否有足够的深度层数据
                    available_depths = df[COLUMNS['depth']].nunique()
                    if available_depths >= 2:
                        result = analyze(df, COLUMNS['depth'], indicator_col)
                        
                        if result and result.get('p_value') is not None:
                            result_key = f"depth_profile_{year}_{county}_{indicator_col}"
                            results[result_key] = result
                            
                            # 汇总表格
                            summary_row = create_summary_row(
                                result, indicator_name, indicator_col,
                                {'年份': year, '县城': county}
                            )
                            
                            # 添加组统计信息
                            groups = sorted(result.get('groups', []), key=lambda x: int(str(x).split('-')[0]) if str(x).split('-')[0].isdigit() else 0)
                            add_group_stats(summary_row, result, groups, suffix='cm')
                            
                            summary_data.append(summary_row)
    
    # 对结果进行p值校正
    apply_correction(processed_data, results, 'depth_profile')
    
    # 生成汇总表格
    if summary_data:
        summary_table = pd.DataFrame(summary_data)
        summary_table = summary_table.sort_values(['年份', '县城', '指标代码'])
    else:
        summary_table = pd.DataFrame()
    
    return {
        'results': results,
        'summary_table': summary_table,
        'analysis_type': 'depth_profile',
        'title': '深度剖面分析表格（深度比较）'
    }

# ---- 主入口函数 ----

def run_analysis(processed_data, available_indicators, analysis_type, filter_counties):
    """
    执行指定类型的分析并生成对应的表格
    
    Args:
        processed_data: 处理后的数据
        available_indicators: 可用指标
        analysis_type (str): 分析类型键
        filter_counties: 筛选的县城列表
    
    Returns:
        dict: 包含分析结果和汇总表格的字典
    """
    if analysis_type not in ANALYSIS_TYPES:
        print(f"未知分析类型: {analysis_type}")
        return {}
    
    print(f"正在进行 {analysis_type} 分析...")
    
    # 根据分析类型调用对应的函数
    if analysis_type == 'spatial':
        return spatial_analysis(processed_data, available_indicators, filter_counties)
    elif analysis_type == 'temporal':
        return temporal_analysis(processed_data, available_indicators, filter_counties)
    elif analysis_type == 'depth_time':
        return depth_time_analysis(processed_data, available_indicators, filter_counties)
    elif analysis_type == 'depth_profile':
        return depth_profile_analysis(processed_data, available_indicators, filter_counties)
    
    return {}