# ---- 主程序 ----
"""
土壤数据时空变化分析主程序
集成数据加载、统计分析和可视化功能
"""

import os
import B_config as config
from C_data_loader import preprocess_data, init_config
from E_analysis_functions import run_analysis
from F_visualization import generate_all_plots
from G_excel_export import export_results

def main():
    """主函数，执行整个分析流程"""
    print("开始方差分析...")

    # 初始化数据配置
    print("\n初始化数据配置...")
    if not init_config():
        print("数据配置初始化失败，程序退出")
        return

    # 确定分析范围
    if config.ANALYSIS_SCOPE == "all":
        target_counties = config.COUNTIES
        print(f"分析模式: 全面分析 - 包含所有县城 {target_counties}")
    else:
        target_counties = config.ANALYSIS_SCOPE
        print(f"分析模式: 指定县城分析 - 仅包含 {target_counties}")

    # 确保输出目录存在（文件会自动覆盖）
    os.makedirs(config.OUTPUT_DIR, exist_ok=True)

    # 加载和预处理数据
    print("\n加载和预处理数据...\n")
    processed_data, available_indicators = preprocess_data()
    
    # 根据分析范围配置运行分析
    analysis_results = {}
    
    for analysis_type, analysis_config in config.ANALYSIS_TYPES.items():
        if config.plot_types.get(analysis_type, True):
            # 检查是否是空间对比且分析范围不是全部（跳过空间对比）
            if analysis_type == 'spatial' and config.ANALYSIS_SCOPE != "all":
                print(f"\n跳过 {analysis_config['name']} 分析（仅在全面分析模式下执行）")
                continue
                
            print(f"\n执行 {analysis_config['name']} 分析...")
            
            # 对目标县城应用筛选
            filter_counties = target_counties if analysis_type != 'spatial' else config.COUNTIES
            
            # 运行分析
            type_results = run_analysis(
                processed_data, 
                available_indicators, 
                analysis_type, 
                filter_counties
            )
            
            # 合并结果
            if isinstance(type_results, dict) and 'results' in type_results:
                analysis_results.update(type_results['results'])
            else:
                analysis_results.update(type_results)
        else:
            print(f"\n跳过 {config.ANALYSIS_TYPES[analysis_type]['name']} 分析（根据配置）")
    
    # 生成所有图表
    print("\n生成图表...")
    generate_all_plots(processed_data, available_indicators, analysis_results)

    # 导出结果到Excel
    export_results(analysis_results, config.EXCEL_DIR, config.plot_types, available_indicators, config.GENERATE_PVALUE_SHEET, target_counties)
    
    print("\n分析完成！")

if __name__ == ('__main__'
                ''):
    main()