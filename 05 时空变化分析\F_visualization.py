# ---- 简化可视化模块 ----
"""
统一的可视化接口，支持四种分析类型
"""

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import math # Added for math.ceil

from B_config import (
    COLUMNS, COUNTIES, YEARS, DEPTHS, INDICATORS,
    ANALYSIS_TYPES, ANALYSIS_SCOPE, FIG_SIZES, LAYOUT, FONTS, STYLE, BOXPLOT_STYLE,
    OUTPUT_DIR, OUTLIER_CONFIG
)
from C_data_loader import handle_outliers

def setup_plot_style():
    """设置统一的图表样式"""
    plt.style.use('seaborn-v0_8')
    plt.rcParams.update({
        'font.sans-serif': ['SimSun'],
        'font.serif': ['Times New Roman'],
        'axes.unicode_minus': False,
        'figure.autolayout': True,
        'font.size': FONTS['base'],
        'axes.edgecolor': 'black',
        'axes.linewidth': 1.5,
        'xtick.direction': 'out',
        'ytick.direction': 'out',
        'axes.facecolor': STYLE['background'],
        'figure.facecolor': STYLE['background'],
        'savefig.facecolor': STYLE['background'],
        'savefig.transparent': False,
        'text.color': STYLE['text_color'],
        'axes.labelcolor': STYLE['text_color'],
        'xtick.color': STYLE['text_color'],
        'ytick.color': STYLE['text_color'],
        'axes.labelsize': FONTS['label'],
        'xtick.labelsize': FONTS['base'],
        'ytick.labelsize': FONTS['base'],
        'legend.fontsize': FONTS['base']
    })

def setup_axis(ax):
    """设置坐标轴样式"""
    ax.spines['right'].set_visible(True)
    ax.spines['top'].set_visible(True)
    ax.spines['left'].set_visible(True)
    ax.spines['bottom'].set_visible(True)
    
    ax.tick_params(axis='both', which='both', right=False, top=False, direction='out')
    ax.yaxis.set_major_locator(plt.MaxNLocator(6))
    ax.grid(False)


def add_significance_labels(ax, x_positions, group_names, labels_dict):
    """添加显著性字母标签"""
    if not labels_dict:
        return
    
    y_min, y_max = ax.get_ylim()
    y_range = y_max - y_min
    label_area_height = y_range * 0.25
    new_y_max = y_max + label_area_height
    ax.set_ylim(y_min, new_y_max)
    
    # 将分隔线上移一些，增加与主图的距离
    separator_offset = y_range * 0.06  # 上移6%的y轴范围
    separator_y = y_max + separator_offset
    label_y = separator_y + (label_area_height - separator_offset) * 0.5  # 在剩余区域中垂直居中
    
    ax.axhline(y=separator_y, color='lightgray', linestyle='-', linewidth=0.5)
    
    for idx, group_name in enumerate(group_names):
        if group_name in labels_dict:
            label = labels_dict[group_name]
            if label:  # 总是显示字母标签，即使都相同
                x_center = x_positions[idx]
                ax.text(x_center, label_y, label,
                       ha='center', va='center', 
                       fontsize=26, fontweight='bold', color='black')

def plot_comparison(data, analysis_results, analysis_type, specific_filter=None):
    """绘制比较图表"""
    
    # 获取分析配置
    config = ANALYSIS_TYPES.get(analysis_type, {})
    if not config:
        print(f"未知的分析类型: {analysis_type}")
        return
    
    # 创建输出目录
    output_dir = os.path.join(OUTPUT_DIR, config['output_dir'])
    os.makedirs(output_dir, exist_ok=True)
    
    # 筛选数据
    plot_data = data.copy()
    
    # 应用特定筛选条件
    if specific_filter:
        for key, value in specific_filter.items():
            col_name = COLUMNS.get(key, key)
            if col_name in plot_data.columns:
                plot_data = plot_data[plot_data[col_name] == value]
    
    # 根据分析类型过滤指标
    filtered_indicators = INDICATORS.copy()
    
    # 对于剖面相关的分析，排除黑土层厚度
    if analysis_type in ['depth_profile', 'depth_time'] and '黑土层厚度' in filtered_indicators:
        filtered_indicators.pop('黑土层厚度')
    
    # 设置图表样式
    setup_plot_style()
    
    # 计算子图布局
    num_indicators = len(filtered_indicators)
    if num_indicators == 0:
        print(f"  警告: 没有适用于{analysis_type}分析的指标")
        return
        
    # 计算子图布局 - 改回两行三列
    ncols = min(3, num_indicators)
    nrows = (num_indicators + ncols - 1) // ncols
    
    # 创建图表
    fig_size = FIG_SIZES.get(analysis_type, (18, 12))
    fig, axes = plt.subplots(nrows, ncols, figsize=fig_size, 
                            facecolor=STYLE['background'])
    
    # 确保axes是二维数组
    if num_indicators == 1:
        axes = np.array([axes])
    axes = axes.flatten()
    
    plt.subplots_adjust(
        wspace=LAYOUT['wspace'], 
        hspace=LAYOUT['hspace'],
        left=LAYOUT['left'],
        right=LAYOUT['right'],
        top=LAYOUT['top'],
        bottom=LAYOUT['bottom']
    )
    
    # 确定绘图参数
    group_col = COLUMNS[config['group_by']]
    
    if analysis_type == 'spatial':
        x_col = COLUMNS['county']
        hue_col = None
        order = COUNTIES
        palette = sns.color_palette(STYLE['palette'], n_colors=len(COUNTIES))
        
    elif analysis_type == 'temporal':
        x_col = COLUMNS['year']
        hue_col = None
        order = [str(y) for y in YEARS]
        palette = sns.color_palette(STYLE['palette'], n_colors=len(YEARS))
        
    elif analysis_type == 'depth_time':
        x_col = COLUMNS['depth']
        hue_col = COLUMNS['year']
        order = DEPTHS
        palette = sns.color_palette(STYLE['palette'], n_colors=len(YEARS))
        
    elif analysis_type == 'depth_profile':
        x_col = COLUMNS['year']
        hue_col = COLUMNS['depth']
        order = [str(y) for y in YEARS]
        palette = sns.color_palette(STYLE['palette'], n_colors=len(DEPTHS))
    
    # 绘制子图
    legend_handles = []
    legend_labels = []
    
    for i, (indicator_name, unit) in enumerate(filtered_indicators.items()):
        if i >= len(axes):
            break
            
        ax = axes[i]
        
        # 筛选当前指标的数据
        indicator_data = plot_data.dropna(subset=[indicator_name])
        
        if indicator_data.empty:
            ax.text(0.5, 0.5, f'{indicator_name}\n无数据',
                   ha='center', va='center', transform=ax.transAxes, 
                   fontsize=FONTS['base'])
            ax.set_title(f'({chr(97+i)}) {indicator_name}', 
                        loc='left', fontsize=FONTS['title'])
            continue
        
        # 处理异常值
        processed_data, outlier_stats = handle_outliers(indicator_data, indicator_name)
        show_outliers = True

        # 只在有异常值时简要打印信息
        if outlier_stats.get('outliers', 0) > 0:
            print(f"    {indicator_name}: 处理了 {outlier_stats['outliers']} 个异常值 "
                  f"({outlier_stats['percentage']:.1f}%)")
        
        # 绘制箱线图
        boxplot_params = BOXPLOT_STYLE.copy()
        
        # 修复seaborn警告：只在有hue时传递palette
        if hue_col:
            box_plot = sns.boxplot(
                x=x_col, y=indicator_name, hue=hue_col, data=processed_data,
                order=order, palette=palette, ax=ax, **boxplot_params
            )
        else:
            box_plot = sns.boxplot(
                x=x_col, y=indicator_name, data=processed_data, hue=x_col,
                order=order, palette=palette, ax=ax, legend=False, **boxplot_params
            )
        
        # 保存图例信息
        if i == 0 and hue_col:
            legend_handles, legend_labels = ax.get_legend_handles_labels()
        
        # 移除子图图例
        if ax.get_legend():
            ax.get_legend().remove()
        
        # 添加显著性标签
        result_key = None
        
        # 根据分析类型构建结果键并添加显著性标签
        if analysis_type == 'spatial' and specific_filter and 'year' in specific_filter:
            result_key = f"spatial_{specific_filter['year']}_{indicator_name}"
        elif analysis_type == 'temporal' and specific_filter and 'county' in specific_filter:
            result_key = f"temporal_{specific_filter['county']}_{indicator_name}"
        elif analysis_type == 'depth_time':
            # 深度时间对比：x轴是深度，hue是年份，需要为每个深度-年份组合添加标签
            if specific_filter and 'county' in specific_filter:
                county = specific_filter['county']
                
                # 收集每个深度的年份标签
                depth_year_labels = {}
                for depth in DEPTHS:
                    # 使用正确的键格式：depth_time_{county}_{indicator}_{depth}cm
                    depth_result_key = f"depth_time_{county}_{indicator_name}_{depth}cm"
                    if depth_result_key in analysis_results:
                        depth_result = analysis_results[depth_result_key]
                        if depth_result and depth_result.get('labels'):
                            # 将年份标签映射到深度-年份键
                            for year, label in depth_result['labels'].items():
                                depth_year_key = f"{depth}_{year}"
                                depth_year_labels[depth_year_key] = label

                if depth_year_labels:
                    # 为深度时间对比图添加显著性标签
                    # 对于grouped boxplot，需要计算每个子组的位置
                    n_depths = len(order)  # order是DEPTHS
                    n_years = len(YEARS)
                    
                    # 计算每个年份子组在每个深度内的位置
                    x_positions = []
                    group_labels = []
                    
                    for depth_idx, depth in enumerate(order):
                        for year_idx, year in enumerate(YEARS):
                            # 计算子组的x位置（grouped boxplot的位置计算）
                            width = 0.8  # seaborn默认的boxplot宽度
                            sub_width = width / n_years
                            offset = (year_idx - (n_years - 1) / 2) * sub_width
                            x_pos = depth_idx + offset
                            x_positions.append(x_pos)
                            
                            # 构建标签键
                            depth_year_key = f"{depth}_{year}"
                            label = depth_year_labels.get(depth_year_key, '')
                            group_labels.append(label)
                    
                    # 添加标签（只为有标签的位置添加）
                    y_min, y_max = ax.get_ylim()
                    y_range = y_max - y_min
                    label_area_height = y_range * 0.25
                    new_y_max = y_max + label_area_height
                    ax.set_ylim(y_min, new_y_max)
                    
                    # 将分隔线上移一些，增加与主图的距离
                    separator_offset = y_range * 0.06  # 上移6%的y轴范围
                    separator_y = y_max + separator_offset
                    label_y = separator_y + (label_area_height - separator_offset) * 0.5  # 在剩余区域中垂直居中
                    
                    ax.axhline(y=separator_y, color='lightgray', linestyle='-', linewidth=0.5)
                    
                    for x_pos, label in zip(x_positions, group_labels):
                        if label and label.strip():  # 只显示非空标签
                            ax.text(x_pos, label_y, label,
                                   ha='center', va='center', 
                                   fontsize=26, fontweight='bold', color='black')

        elif analysis_type == 'depth_profile':
            # 深度剖面对比：x轴是年份，hue是深度，需要为每个年份-深度组合添加标签
            if specific_filter and 'county' in specific_filter:
                county = specific_filter['county']
                
                # 收集每年的深度标签
                year_depth_labels = {}
                for year in YEARS:
                    # 使用正确的键格式：depth_profile_{year}_{county}_{indicator}
                    profile_result_key = f"depth_profile_{year}_{county}_{indicator_name}"
                    if profile_result_key in analysis_results:
                        profile_result = analysis_results[profile_result_key]
                        if profile_result and profile_result.get('labels'):
                            # 将深度标签映射到年份-深度键
                            for depth, label in profile_result['labels'].items():
                                year_depth_key = f"{year}_{depth}"
                                year_depth_labels[year_depth_key] = label

                if year_depth_labels:
                    # 为深度剖面对比图添加显著性标签
                    # 对于grouped boxplot，需要计算每个子组的位置
                    n_years = len(order)  # order是YEARS (转换为字符串)
                    n_depths = len(DEPTHS)
                    
                    # 计算每个深度子组在每个年份内的位置
                    x_positions = []
                    group_labels = []
                    
                    for year_idx, year in enumerate(order):
                        for depth_idx, depth in enumerate(DEPTHS):
                            # 计算子组的x位置（grouped boxplot的位置计算）
                            width = 0.8  # seaborn默认的boxplot宽度
                            sub_width = width / n_depths
                            offset = (depth_idx - (n_depths - 1) / 2) * sub_width
                            x_pos = year_idx + offset
                            x_positions.append(x_pos)
                            
                            # 构建标签键
                            year_depth_key = f"{year}_{depth}"
                            label = year_depth_labels.get(year_depth_key, '')
                            group_labels.append(label)
                    
                    # 添加标签（只为有标签的位置添加）
                    y_min, y_max = ax.get_ylim()
                    y_range = y_max - y_min
                    label_area_height = y_range * 0.25
                    new_y_max = y_max + label_area_height
                    ax.set_ylim(y_min, new_y_max)
                    
                    # 将分隔线上移一些，增加与主图的距离
                    separator_offset = y_range * 0.06  # 上移6%的y轴范围
                    separator_y = y_max + separator_offset
                    label_y = separator_y + (label_area_height - separator_offset) * 0.5  # 在剩余区域中垂直居中
                    
                    ax.axhline(y=separator_y, color='lightgray', linestyle='-', linewidth=0.5)
                    
                    for x_pos, label in zip(x_positions, group_labels):
                        if label and label.strip():  # 只显示非空标签
                            ax.text(x_pos, label_y, label,
                                   ha='center', va='center', 
                                   fontsize=26, fontweight='bold', color='black')

        # 添加显著性标签（空间和时间对比）
        if result_key and result_key in analysis_results:
            result = analysis_results[result_key]
            if result and result.get('labels'):
                x_positions = np.arange(len(order))
                add_significance_labels(ax, x_positions, order, result['labels'])
        
        # 设置子图属性
        ax.set_title(f'({chr(97+i)}) {indicator_name}', 
                    loc='left', fontsize=FONTS['title'])
        
        # 根据分析类型设置正确的横坐标标签
        if analysis_type == 'spatial':
            xlabel = '县城'
        elif analysis_type == 'temporal':
            xlabel = '年份'
        elif analysis_type == 'depth_time':
            xlabel = '深度层 (cm)'
        elif analysis_type == 'depth_profile':
            xlabel = '年份'
        else:
            xlabel = config['name'].split('对比')[0]  # 默认回退
            
        ax.set_xlabel(xlabel, fontsize=FONTS['label'])
        ax.set_ylabel(f'{indicator_name} {unit}', fontsize=FONTS['label'])
        
        setup_axis(ax)
    
    # 隐藏多余子图
    for j in range(num_indicators, len(axes)):
        axes[j].set_visible(False)
    
    # 设置总标题
    title_suffix = ""
    if specific_filter:
        if 'year' in specific_filter:
            title_suffix = f" - {specific_filter['year']}年"
        if 'county' in specific_filter:
            title_suffix = f" - {specific_filter['county']}县"
    
    fig.suptitle(f'{config["name"]}{title_suffix}', 
                fontsize=FONTS['title'], y=LAYOUT['title_y'])
    
    # 添加统一图例（对于depth_time和depth_profile类型）
    if legend_handles and legend_labels and hue_col:
        # 所有类型的图例都放在底部，保持一致性
        fig.legend(legend_handles, legend_labels,
                  loc='lower center', borderaxespad=0.5,
                  ncol=len(legend_labels), frameon=True,
                  fontsize=FONTS['base'], bbox_to_anchor=(0.5, -0.05))
    
    plt.tight_layout()
    
    # 保存图表
    filename_parts = [config['output_dir']]
    if specific_filter:
        for key, value in specific_filter.items():
            filename_parts.append(f"{value}")
    
    filename = "_".join(filename_parts) + ".png"
    save_path = os.path.join(output_dir, filename)
    
    plt.savefig(save_path, dpi=LAYOUT['dpi'], bbox_inches='tight', 
                facecolor=STYLE['background'])
    print(f"  图表已保存: {save_path}")
    plt.close(fig)

def generate_all_plots(processed_data, available_indicators, analysis_results):
    """生成所有类型的图表"""
    
    # 合并所有数据
    all_data = []
    for year in YEARS:
        for county in COUNTIES:
            for indicator_name in INDICATORS.keys():
                df = processed_data.get(year, {}).get(county, {}).get(indicator_name)
                if df is not None and not df.empty:
                    temp_df = df.copy()
                    if COLUMNS['county'] not in temp_df.columns:
                        temp_df[COLUMNS['county']] = county
                    if COLUMNS['year'] not in temp_df.columns:
                        temp_df[COLUMNS['year']] = year
                    all_data.append(temp_df)
    
    if not all_data:
        print("无数据可用于绘图")
        return
    
    combined_data = pd.concat(all_data, ignore_index=True).drop_duplicates()
    
    # 只有在全面分析模式下才生成空间对比图
    if ANALYSIS_SCOPE == "all":
        # 生成空间对比图（按年份）
        for year in YEARS:
            plot_comparison(combined_data, analysis_results, 'spatial', {'year': year})
    
    # 确定目标县城
    target_counties = COUNTIES if ANALYSIS_SCOPE == "all" else ANALYSIS_SCOPE
    
    # 生成时间对比图（按县城）
    for county in target_counties:
        plot_comparison(combined_data, analysis_results, 'temporal', {'county': county})
    
    # 生成深度时间对比图（按县城）
    for county in target_counties:
        plot_comparison(combined_data, analysis_results, 'depth_time', {'county': county})
    
    # 生成深度剖面对比图（按县城，合并显示所有年份）
    for county in target_counties:
        plot_comparison(combined_data, analysis_results, 'depth_profile', {'county': county})