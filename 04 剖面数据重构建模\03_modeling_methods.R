# =============================================================================
# 三种建模方法统一模块
# 
# 功能：
# 1. 普通克里金建模
# 2. 回归克里金建模  
# 3. 随机森林建模
# 4. 统一的建模接口和错误处理
# =============================================================================

# 加载必要的包
library(randomForest)
library(dplyr)

# =============================================================================
# 建模函数
# =============================================================================

# --- 普通克里金建模函数 ---
ordinary_kriging_modeling <- function(layer_data, soil_property, layer_name, config) {

  cat("\n=== 普通克里金建模 ===\n")
  cat("深度层:", layer_name, "| 土壤属性:", soil_property, "\n")

  # 基本检查
  if(nrow(layer_data) == 0 || !soil_property %in% names(layer_data)) {
    cat("数据检查失败，跳过建模\n")
    return(NULL)
  }

  # 使用统一的数据准备函数（普通克里金不使用环境变量）
  cat("普通克里金：只使用坐标和目标变量，不使用协变量\n")
  data_prep <- prepare_data(layer_data, soil_property, config, "ordinary_kriging", NULL, silent = TRUE)

  if(is.null(data_prep)) {
    return(NULL)
  }

  modeling_data <- data_prep$modeling_data
  available_env_vars <- data_prep$env_vars
  available_neighbor_vars <- data_prep$neighbor_vars

  # 普通克里金只使用坐标，不使用环境变量和邻近层变量

  # 自动调优普通克里金参数
  cat("自动调优普通克里金参数...\n")
  best_params <- tune_model_parameters(modeling_data, soil_property, c(), config, "ordinary_kriging")

  # 检查参数是否有效
  if(is.null(best_params) || length(best_params) == 0) {
    cat("调优失败，普通克里金建模失败\n")
    return(NULL)
  }

  cat("普通克里金参数: nmax =", best_params$nmax, ", nmin =", best_params$nmin,
      ", vgm_model =", best_params$vgm_model, "\n")

  # 直接使用调优结果中的评估指标（避免重复交叉验证）
  cat("普通克里金评估结果（来自调优）:\n")
  cat("  R² =", round(best_params$r_squared, 4), "\n")
  cat("  RMSE =", round(best_params$rmse, 4), "\n")
  cat("  MAE =", round(best_params$mae, 4), "\n")

  # 简化模型保存：只保存参数，预测时重新建模
  result <- list(
    method = "ordinary_kriging",
    layer_name = layer_name,
    soil_property = soil_property,
    sample_size = nrow(modeling_data),
    tuned_params = best_params,  # 调优参数信息
    scaler_info = data_prep$scaler_info  # 保存标准化信息用于预测时的反标准化
  )

  # 合并评估指标
  result <- merge_evaluation_metrics(result, best_params, best_params$r_squared)

  return(result)
}

# --- 回归克里金建模函数 ---
regression_kriging_modeling <- function(layer_data, soil_property, layer_name, config, env_variables = NULL) {

  cat("\n=== 回归克里金建模 ===\n")
  cat("深度层:", layer_name, "| 土壤属性:", soil_property, "\n")

  # 基本检查
  if(nrow(layer_data) == 0 || !soil_property %in% names(layer_data)) {
    cat("数据检查失败，跳过建模\n")
    return(NULL)
  }

  # 使用数据准备函数
  data_prep <- prepare_data(layer_data, soil_property, config, "regression_kriging", env_variables, silent = TRUE)

  if(is.null(data_prep)) {
    return(NULL)
  }

  modeling_data <- data_prep$modeling_data
  available_env_vars <- data_prep$env_vars
  available_neighbor_vars <- data_prep$neighbor_vars

  # 回归克里金使用所有可用协变量（环境变量 + 邻近层变量）
  all_covariates <- c(available_env_vars, available_neighbor_vars)

  # 检查协变量数量是否足够进行回归克里金
  if(length(all_covariates) < 1) {
    cat("可用协变量不足（", length(all_covariates), "< 1），回归克里金不适用\n")
    return(NULL)
  }

  cat("回归克里金使用协变量:", length(all_covariates), "个:", paste(all_covariates, collapse = ", "), "\n")
  cat("建模样本数:", nrow(modeling_data), "| 环境变量数:", length(available_env_vars), "\n")

  # 构建回归公式（使用所有协变量）
  formula_str <- paste(soil_property, "~", paste(all_covariates, collapse = " + "))
  formula_obj <- as.formula(formula_str)

  # 自动调优回归克里金参数
  cat("自动调优回归克里金参数...\n")
  best_params <- tune_model_parameters(modeling_data, soil_property, all_covariates, config, "regression_kriging")

  # 检查参数是否有效
  if(is.null(best_params) || length(best_params) == 0) {
    cat("调优失败，回归克里金建模失败\n")
    return(NULL)
  }

  cat("回归克里金参数: nmax =", best_params$nmax, ", nmin =", best_params$nmin, ", vgm_model =", best_params$vgm_model, "\n")

  # 直接使用调优结果中的评估指标（避免重复交叉验证）
  cat("回归克里金评估结果（来自调优）:\n")
  cat("  R² =", round(best_params$r_squared, 4), "\n")
  cat("  RMSE =", round(best_params$rmse, 4), "\n")
  cat("  MAE =", round(best_params$mae, 4), "\n")

  # 简化模型保存：只保存参数，预测时重新建模
  result <- list(
    method = "regression_kriging",
    layer_name = layer_name,
    soil_property = soil_property,
    sample_size = nrow(modeling_data),
    env_variables = all_covariates,  # 保存所有协变量（环境变量+邻近层变量）
    tuned_params = best_params,  # 调优参数信息
    formula = formula_obj,  # 保存回归公式
    scaler_info = data_prep$scaler_info  # 保存标准化信息用于预测时的反标准化
  )

  # 合并评估指标
  result <- merge_evaluation_metrics(result, best_params, best_params$r_squared)

  return(result)
}

# --- 随机森林建模主函数 ---
random_forest_modeling <- function(modeling_data, soil_property, layer_name, config, env_variables = NULL) {

  cat("\n=== 随机森林建模 ===\n")
  cat("深度层:", layer_name, "| 土壤属性:", soil_property, "\n")

  # 准备建模数据
  data_prep <- prepare_data(modeling_data, soil_property, config, "random_forest", env_variables, silent = TRUE)

  if(is.null(data_prep)) {
    return(NULL)
  }

  rf_data <- data_prep$modeling_data
  feature_cols <- data_prep$feature_names

  tryCatch({

    # 自动调优随机森林参数
    rf_config <- config$random_forest
    n_samples <- nrow(rf_data)

    # 自动调优随机森林参数
    cat("自动调优随机森林参数...\n")
    best_params <- tune_model_parameters(rf_data, soil_property, feature_cols, config, "random_forest")

    # 检查参数是否有效
    if(is.null(best_params) || length(best_params) == 0) {
      cat("调优失败，随机森林建模失败\n")
      return(NULL)
    }

    ntree <- best_params$ntree
    nodesize <- best_params$nodesize
    mtry_factor <- best_params$mtry_factor

    # 计算mtry
    mtry <- max(1, floor(mtry_factor * length(feature_cols)))
    cat("随机森林参数: ntree =", ntree, ", mtry =", mtry, ", nodesize =", nodesize, "\n")

    # 保存特征统计信息（用于预测时的缺失值处理）
    feature_stats <- list()
    for(col in feature_cols) {
      feature_stats[[col]] <- list(
        mean = mean(rf_data[[col]], na.rm = TRUE),
        median = median(rf_data[[col]], na.rm = TRUE),
        sd = sd(rf_data[[col]], na.rm = TRUE)
      )
    }

    # 构建随机森林模型
    rf_formula <- as.formula(paste(soil_property, "~", paste(feature_cols, collapse = " + ")))
    rf_model <- randomForest(rf_formula, data = rf_data,
                            ntree = ntree, mtry = mtry, nodesize = nodesize,
                            importance = rf_config$importance)

    # 直接使用调优阶段的评估结果（避免重复交叉验证）
    cat("随机森林评估结果（来自调优）:\n")
    cat("  R² =", round(best_params$r_squared, 4), "\n")
    cat("  RMSE =", round(best_params$rmse, 4), "\n")
    cat("  MAE =", round(best_params$mae, 4), "\n")

    # 构建返回结果，使用统一函数提取评估指标
    result <- list(
      method = "random_forest",
      layer_name = layer_name,
      soil_property = soil_property,
      sample_size = nrow(rf_data),
      features = feature_cols,
      feature_stats = feature_stats,  # 特征统计信息（用于预测）
      tuned_params = best_params,  # 调优参数信息
      model = rf_model,
      scaler_info = data_prep$scaler_info  # 保存标准化信息用于预测时的反标准化
    )

    # 合并评估指标
    result <- merge_evaluation_metrics(result, best_params, best_params$r_squared)

    return(result)

  }, error = function(e) {
    cat("随机森林建模失败:", e$message, "\n")
    return(NULL)
  })
}

# --- 三种方法对比函数 ---
compare_three_methods <- function(modeling_data, soil_property, layer_name, env_variables, config) {
  cat("开始三种方法自动调优对比...\n")

  results <- list()
  best_r2 <- -Inf
  best_rmse <- Inf
  best_method <- NULL
  best_result <- NULL



  # R²差异阈值：小于此值认为R²接近，需要比较RMSE
  r2_tolerance <- config$method_comparison$r2_tolerance

  # 方法1：回归克里金（总是尝试，让函数内部判断环境变量可用性）
  cat("\n--- 测试回归克里金 ---\n")
  rk_result <- tryCatch({
    regression_kriging_modeling(modeling_data, soil_property, layer_name, config, env_variables)
  }, error = function(e) {
    cat("回归克里金失败:", e$message, "\n")
    NULL
  })

  if(!is.null(rk_result) && !is.null(rk_result$r_squared)) {
    results$regression_kriging <- rk_result
    cat("回归克里金 R² =", round(rk_result$r_squared, 3), ", RMSE =", round(rk_result$rmse, 3), "\n")
  }

  # 方法2：普通克里金
  cat("\n--- 测试普通克里金 ---\n")
  ok_result <- tryCatch({
    ordinary_kriging_modeling(modeling_data, soil_property, layer_name, config)
  }, error = function(e) {
    cat("普通克里金失败:", e$message, "\n")
    NULL
  })

  if(!is.null(ok_result) && !is.null(ok_result$r_squared)) {
    results$ordinary_kriging <- ok_result
    cat("普通克里金 R² =", round(ok_result$r_squared, 3), ", RMSE =", round(ok_result$rmse, 3), "\n")
  }

  # 方法3：随机森林
  cat("\n--- 测试随机森林 ---\n")
  rf_result <- tryCatch({
    random_forest_modeling(modeling_data, soil_property, layer_name, config, env_variables)
  }, error = function(e) {
    cat("随机森林失败:", e$message, "\n")
    NULL
  })

  if(!is.null(rf_result) && !is.null(rf_result$r_squared)) {
    results$random_forest <- rf_result
    cat("随机森林 R² =", round(rf_result$r_squared, 3), ", RMSE =", round(rf_result$rmse, 3), "\n")
  }

  # 智能选择最佳模型：优先比较R²，接近时再比较RMSE
  cat("\n=== 模型选择 ===\n")

  if(length(results) == 0) {
    cat("没有有效的建模结果\n")
    return(list(
      best_method = NULL,
      best_r2 = NA,
      best_result = NULL,
      all_results = results
    ))
  }

  # 找到最高R²
  r2_values <- sapply(results, function(x) x$r_squared)
  max_r2 <- max(r2_values)

  # 找到R²接近最高值的方法
  close_methods <- names(r2_values)[r2_values >= (max_r2 - r2_tolerance)]

  if(length(close_methods) == 1) {
    # 只有一个方法R²最高
    best_method <- close_methods[1]
    best_result <- results[[best_method]]
    cat("选择", best_method, "（R²最高：", round(best_result$r_squared, 3), "）\n")
  } else {
    # 多个方法R²接近，比较RMSE
    cat("多个方法R²接近（差异<", r2_tolerance, "），比较RMSE选择最佳方法\n")

    rmse_values <- sapply(results[close_methods], function(x) x$rmse)
    best_method <- close_methods[which.min(rmse_values)]

    best_result <- results[[best_method]]

    cat("选择", best_method, "（R²=", round(best_result$r_squared, 3),
        ", RMSE最小：", round(best_result$rmse, 3), "）\n")
  }

  return(list(
    best_method = best_method,
    best_r2 = best_result$r_squared,
    best_rmse = best_result$rmse,
    best_result = best_result,
    all_results = results
  ))
}

cat("三种建模方法模块加载完成\n")
