# ---- 方差分析前提检验模块 ----
"""
方差分析前提检验模块，用于检验正态性和方差齐性
判断是否需要使用非参数检验方法
"""

import pandas as pd
from scipy.stats import shapiro, levene
import warnings
warnings.filterwarnings('ignore')

try:
    from config import ALPHA
except ImportError:
    ALPHA = 0.05
    print("警告: 无法导入config模块，使用默认值 ALPHA=0.05")

# ---- 正态性检验函数 ----

def shapiro_test(data, value_col, factor_col=None, group=None):
    """
Shapiro-Wilk正态性检验
    """
    if factor_col and group:
        test_data = data[data[factor_col] == group][value_col].dropna()
    else:
        test_data = data[value_col].dropna()
    
    if len(test_data) < 3 or len(test_data) > 5000:
        return {'statistic': None, 'p_value': None, 'is_normal': None}
    
    try:
        stat, p_val = shapiro(test_data)
        return {
            'statistic': stat,
            'p_value': p_val,
            'is_normal': p_val > ALPHA
        }
    except:
        return {'statistic': None, 'p_value': None, 'is_normal': None}







# ---- 方差齐性检验函数 ----

def levene_test(data, value_col, factor_col):
    """
Levene方差齐性检验
    """
    groups = data[factor_col].unique()
    if len(groups) < 2:
        return {'statistic': None, 'p_value': None, 'equal_variances': None}
    
    group_data = []
    for group in groups:
        group_values = data[data[factor_col] == group][value_col].dropna()
        if len(group_values) >= 2:
            group_data.append(group_values)
    
    if len(group_data) < 2:
        return {'statistic': None, 'p_value': None, 'equal_variances': None}
    
    try:
        stat, p_val = levene(*group_data, center='median')
        return {
            'statistic': stat,
            'p_value': p_val,
            'equal_variances': p_val > ALPHA
        }
    except:
        return {'statistic': None, 'p_value': None, 'equal_variances': None}





# ---- 综合前提检验函数 ----

def test_assumptions(data, value_col, factor_col, alpha=None):
    """
    综合进行正态性和方差齐性检验
    
    Args:
        data (pd.DataFrame): 数据框
        value_col (str): 数值列名
        factor_col (str): 分组列名
        alpha (float, optional): 显著性水平，默认使用config中的ALPHA
        
    Returns:
        dict: 包含所有检验结果和建议的字典
    """
    if alpha is None:
        alpha = ALPHA
    
    # 数据预处理
    clean_data = data.dropna(subset=[value_col, factor_col])
    if clean_data.empty:
        return {
            'error': '数据清理后为空',
            'recommendation': '无法进行分析'
        }

    groups = clean_data[factor_col].unique()
    total_n = len(clean_data)

    results = {
        'data_summary': {
            'total_n': total_n,
            'n_groups': len(groups),
            'groups': list(groups),
            'alpha': alpha
        },
        'normality_tests': {},
        'homogeneity_tests': {},
        'group_summaries': {},
        'recommendation': {}
    }

    # 1. 各组描述性统计（简化版）
    for group in groups:
        group_data = clean_data[clean_data[factor_col] == group][value_col]
        group_summary = {
            'n': len(group_data),
            'mean': group_data.mean(),
            'median': group_data.median(),
            'std': group_data.std(),
            'var': group_data.var(),
            'min': group_data.min(),
            'max': group_data.max(),
            'skewness': group_data.skew(),
            'kurtosis': group_data.kurtosis()
        }
        results['group_summaries'][str(group)] = group_summary

    # 2. 正态性检验
    sw_result = shapiro_test(clean_data, value_col)
    results['normality_tests']['overall'] = {'shapiro_wilk': sw_result}

    # 各组正态性检验
    group_normality = {}
    for group in groups:
        sw_group = shapiro_test(clean_data, value_col, factor_col, group)
        group_normality[str(group)] = {'shapiro_wilk': sw_group}
    results['normality_tests']['by_group'] = group_normality

    # 3. 方差齐性检验（Levene）
    levene_result = levene_test(clean_data, value_col, factor_col)
    results['homogeneity_tests']['levene'] = levene_result

    # 4. 生成建议
    recommendation = recommend_test(results, alpha)
    results['recommendation'] = recommendation
    
    return results

def recommend_test(results, alpha):
    """
    基于检验结果生成统计方法建议

    Args:
        results (dict): 检验结果字典
        alpha (float): 显著性水平

    Returns:
        dict: 包含建议的字典
    """
    recommendation = {
        'use_parametric': False,
        'use_nonparametric': True,
        'reasons': [],
        'suggested_tests': [],
        'summary': []
    }

    # 检查正态性（Shapiro-Wilk检验）
    normality_ok = True
    normality_reasons = []

    # 整体正态性检验结果
    overall_normal = results['normality_tests']['overall']['shapiro_wilk']
    if overall_normal['p_value'] is not None and not overall_normal['is_normal']:
        normality_ok = False
        normality_reasons.append(f"整体数据Shapiro-Wilk检验不通过(p={overall_normal['p_value']:.4f})")

    # 各组正态性检验结果
    group_normal = results['normality_tests']['by_group']
    group_normal_failed = []

    for group_name, group_tests in group_normal.items():
        sw_result = group_tests['shapiro_wilk']
        if sw_result['p_value'] is not None and not sw_result['is_normal']:
            group_normal_failed.append(f"{group_name}组")

    if group_normal_failed:
        normality_ok = False
        normality_reasons.append(f"以下组正态性检验不通过: {', '.join(group_normal_failed)}")

    # 检查方差齐性（Levene检验）
    homogeneity_ok = True
    homogeneity_reasons = []

    levene_result = results['homogeneity_tests']['levene']
    if levene_result['p_value'] is not None and not levene_result['equal_variances']:
        homogeneity_ok = False
        homogeneity_reasons.append(f"Levene检验不通过(p={levene_result['p_value']:.4f})")
    
    # 生成建议
    if normality_ok and homogeneity_ok:
        recommendation['use_parametric'] = True
        recommendation['use_nonparametric'] = False
        recommendation['suggested_tests'] = ['ANOVA (方差分析)', 'Tukey HSD (事后检验)']
        recommendation['summary'] = [
            "✓ 建议使用参数检验方法",
            "  - 数据基本满足正态性假设",
            "  - 数据基本满足方差齐性假设",
            "  - 可使用单因素方差分析(ANOVA)进行主检验",
            "  - 可使用Tukey HSD进行事后多重比较"
        ]
    else:
        recommendation['use_parametric'] = False
        recommendation['use_nonparametric'] = True
        recommendation['reasons'] = normality_reasons + homogeneity_reasons
        
        n_groups = results['data_summary']['n_groups']
        if n_groups == 2:
            recommendation['suggested_tests'] = ['Mann-Whitney U检验']
        else:
            recommendation['suggested_tests'] = ['Kruskal-Wallis H检验', 'Dunn事后检验']
        
        summary_lines = ["✗ 建议使用非参数检验方法"]
        summary_lines.append("  违反假设:")
        for reason in recommendation['reasons'][:5]:  # 只显示前5个原因
            summary_lines.append(f"    - {reason}")
        if len(recommendation['reasons']) > 5:
            summary_lines.append(f"    - ... (共{len(recommendation['reasons'])}个问题)")
        
        summary_lines.append("  推荐方法:")
        for test in recommendation['suggested_tests']:
            summary_lines.append(f"    - {test}")
        
        recommendation['summary'] = summary_lines
    
    return recommendation

def batch_test(data, value_cols, factor_col, alpha=None, output_file=None):
    """
    批量进行多个指标的前提检验
    
    Args:
        data (pd.DataFrame): 数据框
        value_cols (list): 数值列名列表
        factor_col (str): 分组列名
        alpha (float, optional): 显著性水平
        output_file (str, optional): 输出文件路径
        
    Returns:
        dict: 所有指标的检验结果
    """
    if alpha is None:
        alpha = ALPHA
    
    all_results = {}
    summary_table = []
    
    print(f"\n{'='*60}")
    print(f"批量前提检验: {len(value_cols)}个指标")
    print(f"分组变量: {factor_col}")
    print(f"显著性水平: {alpha}")
    print(f"{'='*60}")
    
    for i, value_col in enumerate(value_cols, 1):
        print(f"\n[{i}/{len(value_cols)}] 检验指标: {value_col}")
        print("-" * 50)
        
        try:
            result = test_assumptions(data, value_col, factor_col, alpha)
            all_results[value_col] = result
            
            # 添加到汇总表
            if 'recommendation' in result:
                rec = result['recommendation']
                summary_table.append({
                    '指标': value_col,
                    '样本量': result['data_summary']['total_n'],
                    '分组数': result['data_summary']['n_groups'],
                    '建议方法': '参数检验' if rec['use_parametric'] else '非参数检验',
                    '推荐检验': ', '.join(rec['suggested_tests'])
                })
            else:
                summary_table.append({
                    '指标': value_col,
                    '样本量': 'Error',
                    '分组数': 'Error',
                    '建议方法': 'Error',
                    '推荐检验': 'Error'
                })
                
        except Exception as e:
            print(f"检验 {value_col} 时出错: {str(e)}")
            all_results[value_col] = {'error': str(e)}
            summary_table.append({
                '指标': value_col,
                '样本量': 'Error',
                '分组数': 'Error',
                '建议方法': 'Error',
                '推荐检验': str(e)
            })
    
    # 打印汇总表
    print(f"\n{'='*60}")
    print("检验结果汇总")
    print(f"{'='*60}")
    
    summary_df = pd.DataFrame(summary_table)
    print(summary_df.to_string(index=False))
    
    # 统计建议
    parametric_count = sum(1 for row in summary_table if row['建议方法'] == '参数检验')
    nonparametric_count = sum(1 for row in summary_table if row['建议方法'] == '非参数检验')
    
    print(f"\n总结:")
    print(f"  参数检验适用: {parametric_count}个指标")
    print(f"  非参数检验适用: {nonparametric_count}个指标")
    print(f"  非参数检验比例: {nonparametric_count/len(value_cols)*100:.1f}%")
    
    if nonparametric_count > parametric_count:
        print(f"\n建议: 由于多数指标不满足参数检验假设，")
        print(f"      建议统一使用非参数检验方法以保持分析的一致性。")
    
    # 保存结果到文件
    if output_file:
        try:
            summary_df.to_excel(output_file, index=False, sheet_name='前提检验汇总')
            print(f"\n结果已保存到: {output_file}")
        except Exception as e:
            print(f"\n保存文件失败: {str(e)}")
    
    all_results['summary'] = {
        'summary_table': summary_df,
        'parametric_count': parametric_count,
        'nonparametric_count': nonparametric_count,
        'total_indicators': len(value_cols),
        'recommendation': '非参数检验' if nonparametric_count > parametric_count else '参数检验'
    }
    
    return all_results

if __name__ == "__main__":
    print("方差分析前提检验模块")
    print("使用示例:")
    print("1. 单个指标检验:")
    print("   result = comprehensive_assumption_test(data, 'indicator_col', 'group_col')")
    print("2. 批量指标检验:")
    print("   results = batch_assumption_test(data, ['ind1', 'ind2'], 'group_col')")