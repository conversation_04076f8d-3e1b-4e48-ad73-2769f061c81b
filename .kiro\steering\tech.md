# Technology Stack

## Programming Languages
- **Python 3.7+**: Primary language for data processing, statistical analysis, and visualization
- **R**: Specialized for spatial interpolation, kriging methods, and geostatistical modeling
- **YAML**: Configuration files for R-based modeling pipelines

## Core Libraries & Frameworks

### Python Stack
- **Data Processing**: pandas, numpy, openpyxl
- **Statistical Analysis**: scipy, scikit-learn
- **Spatial Interpolation**: scipy.interpolate (PCHIP, linear interpolation)
- **Visualization**: matplotlib, seaborn
- **Excel Operations**: xlsxwriter, openpyxl

### R Stack
- **Spatial Analysis**: sp, gstat, automap
- **Machine Learning**: randomForest
- **Kriging Methods**: Ordinary Kriging, Regression Kriging
- **Configuration**: yaml package for config management

## Development Environment
- **Platform**: Windows (win32)
- **Shell**: CMD/PowerShell
- **IDE Support**: RStudio for R development, Python IDEs for Python scripts

## Common Commands

### Python Environment
```bash
# Install dependencies
pip install -r requirements.txt

# Run data standardization
python "01 数据分析处理/土壤剖面数据标准化.py"

# Run spatio-temporal analysis
python "05 时空变化分析/A_main.py"
```

### R Environment
```r
# Install required packages
install.packages(c("sp", "gstat", "randomForest", "yaml"))

# Run modeling pipeline
source('04 剖面数据重构建模/01_main.R')
```

## Data Formats
- **Input**: Excel files (.xlsx) with standardized column structures
- **Output**: Excel reports, PNG visualizations, statistical summaries
- **Configuration**: YAML files for R pipelines, Python config modules

## Coordinate Systems
- **Target CRS**: EPSG:4550 (China Geodetic Coordinate System 2000)
- **Spatial Operations**: County-level analysis with geographic coordinates

## File Encoding
- **Python Scripts**: UTF-8 with BOM for Chinese character support
- **Data Files**: Excel format to preserve Chinese column names and metadata