# 土壤剖面数据缺失值填补系统

## 项目概述

本项目是一个完整的土壤剖面数据缺失值填补解决方案，基于三种先进的空间插值和机器学习方法（普通克里金、回归克里金、随机森林）进行自动化缺失值填补。系统采用统一的数据源（辅助变量-两期数据.xlsx），实现从缺失值分析、建模、预测到结果输出的全流程自动化处理。

## 核心特性

### 🎯 智能建模策略
- ✅ **三种方法自动对比**：普通克里金、回归克里金、随机森林
- ✅ **自适应样本量策略**：根据数据量自动调整建模复杂度
- ✅ **智能变量选择**：基于相关性分析自动选择最优协变量
- ✅ **分层建模**：只对有缺失值的深度层进行建模，提高效率

### 🔧 自动化优化
- ✅ **参数自动调优**：基于交叉验证的网格搜索优化
- ✅ **自适应交叉验证**：根据样本量选择最优验证策略
- ✅ **邻近层协变量**：自动使用邻近深度层作为辅助变量
- ✅ **分层保底策略**：多级回退机制确保填补成功

### 📊 完整评估体系
- ✅ **多维评估指标**：R²、RMSE、MAE、CCC、RPD
- ✅ **方法性能对比**：自动选择最优方法
- ✅ **详细性能报告**：Excel格式多工作表报告
- ✅ **填补质量追踪**：完整的填补过程记录

### 💾 统一数据管理
- ✅ **单一数据源**：基于辅助变量-两期数据.xlsx统一处理
- ✅ **完整数据输出**：保留所有原始信息和环境变量
- ✅ **灵活配置**：支持县城合并、年份选择、属性筛选
- ✅ **批量处理**：支持多县城、多年份、多属性并行处理

## 系统架构

### 📁 文件结构
```
04 剖面数据重构建模/
├── 01_main.R                    # 主程序入口
├── 02_config.yml               # 配置文件
├── 03_modeling_methods.R       # 建模方法实现
├── 04_data.R                   # 数据加载与预处理
├── 05_modeling.R               # 建模数据准备
├── 06_tuning.R                 # 参数调优
├── 07_evaluation.R             # 模型评估
├── 08_prediction.R             # 预测与填补
├── 09_report.R                 # 报告生成
└── README.md                   # 项目文档
```

### 🔄 处理流程
1. **配置加载** → 读取配置文件，验证参数完整性
2. **数据加载** → 加载辅助变量数据，进行坐标投影转换
3. **缺失值分析** → 按深度层分析缺失情况，确定建模任务
4. **建模数据准备** → 准备训练数据，选择协变量
5. **参数调优** → 三种方法并行调优，寻找最优参数
6. **模型评估** → 交叉验证评估，选择最佳方法
7. **缺失值填补** → 使用最优模型进行预测填补
8. **结果输出** → 保存填补结果和性能报告

## 快速开始

### 📋 环境要求
- R >= 4.0.0
- RStudio (推荐)
- 必需R包：`yaml`, `readxl`, `openxlsx`, `sf`, `gstat`, `randomForest`, `dplyr`

### 📂 数据准备
确保以下文件存在：
```
../00 原始数据/
├── 辅助变量-两期数据.xlsx           # 主数据文件（必需）
└── 典型县/
    ├── 相关性分析结果/             # 相关性分析结果（可选）
    └── 模型预测结果/               # 输出目录（自动创建）
```

### 🚀 运行系统
```r
# 在RStudio中运行
source('01_main.R')
```

### 📊 查看结果
输出文件位于 `../00 原始数据/典型县/模型预测结果/`：
- `东北分_填补后完整数据.xlsx` - 填补后的完整数据
- `东北分_性能报告.xlsx` - 详细性能分析报告

## 配置说明

### 🎛️ 主要配置项
```yaml
# 数据路径
data_paths:
  full_data_path: "../00 原始数据/辅助变量-两期数据.xlsx"
  
# 分析范围
combine_counties: false          # 是否合并县城分析
counties: ["嫩江", "梨树", ...]  # 目标县城列表
years: [1980, 2023]             # 目标年份列表
target_soil_properties: [...]    # 目标土壤属性

# 建模参数
general_options:
  min_samples_for_modeling: 10   # 最少建模样本数
  max_variable_missing_rate: 0.3 # 变量最大缺失率
```

### 🔧 高级配置
- **自适应变量选择**：根据样本量自动调整协变量数量
- **交叉验证策略**：样本量自适应的验证方法选择
- **调优参数范围**：各方法的参数搜索空间
- **保底策略配置**：多级回退机制参数

## 技术特点

### 🧠 智能算法
- **空间插值**：普通克里金和回归克里金处理空间相关性
- **机器学习**：随机森林处理复杂非线性关系
- **集成策略**：自动选择最优方法，避免单一方法局限性

### 📈 性能优化
- **样本量自适应**：根据数据量调整模型复杂度
- **并行处理**：多方法同时调优，提高计算效率
- **内存优化**：分层处理，避免大数据内存溢出

### 🛡️ 稳健性保证
- **多级回退**：剖面插值 → 邻近层均值 → 深度层均值
- **数据验证**：完整的输入数据质量检查
- **错误处理**：详细的异常捕获和处理机制

## 评估指标

### 📊 统计指标
- **R²** (决定系数): 模型解释方差比例，越接近1越好
- **RMSE** (均方根误差): 预测误差大小，越小越好  
- **MAE** (平均绝对误差): 预测偏差程度，越小越好
- **CCC** (一致性相关系数): 预测一致性，越接近1越好
- **RPD** (性能偏差比): >2优秀，1.4-2良好，<1.4较差

### 📈 性能标准
- **优秀**: R² > 0.8, RPD > 2.0
- **良好**: R² > 0.6, RPD > 1.4  
- **可接受**: R² > 0.4, RPD > 1.0
- **较差**: R² < 0.4, RPD < 1.0

## 应用场景

### 🌾 土壤科学研究
- 土壤剖面数据质量控制
- 历史数据缺失值修复
- 多源数据融合分析

### 🗺️ 空间制图
- 土壤属性空间分布制图
- 多深度层土壤制图
- 时空变化分析

### 📊 数据分析
- 大规模土壤数据预处理
- 统计分析前的数据完整化
- 机器学习模型输入准备

## 最佳实践

### 💡 使用建议
1. **样本量要求**：每深度层建议≥20个样本
2. **变量完整性**：环境变量缺失率控制在30%以内
3. **空间分布**：确保样本空间分布相对均匀
4. **质量控制**：填补前进行异常值检测和处理

### ⚡ 性能优化
1. **合并县城**：启用combine_counties增加样本量
2. **并行处理**：利用多核CPU加速计算
3. **内存管理**：大数据集时分批处理
4. **缓存机制**：重复分析时利用中间结果

### 🔍 质量评估
1. **交叉验证**：关注CV结果的稳定性
2. **空间分布**：检查填补值的空间合理性
3. **时间一致性**：多年份数据的时间连续性
4. **专业知识**：结合土壤学知识验证结果

## 故障排除

### ❗ 常见问题
| 问题 | 原因 | 解决方案 |
|------|------|----------|
| 数据加载失败 | 文件路径错误 | 检查配置文件中的路径设置 |
| 建模样本不足 | 缺失值过多 | 降低min_samples_for_modeling或合并县城 |
| 坐标投影错误 | 经纬度数据问题 | 检查坐标数据完整性和格式 |
| 内存不足 | 数据量过大 | 分批处理或增加系统内存 |
| 模型收敛失败 | 数据质量问题 | 进行数据预处理和异常值处理 |

### 🔧 调试技巧
1. **启用测试模式**：设置test_mode=true进行小规模测试
2. **查看日志**：关注控制台输出的详细信息
3. **检查中间结果**：验证数据加载和预处理结果
4. **逐步调试**：分模块运行，定位问题所在

## 更新历史

### 🆕 v2.0 (当前版本) - 2024年
**重大更新：统一数据源架构**
- ✨ 完全基于辅助变量-两期数据.xlsx进行分析
- 🗑️ 移除basic_data_path依赖，简化配置
- 📊 直接输出完整填补数据，保留所有信息
- 📚 全面更新文档，反映最新架构变化
- 🔧 优化代码结构，提高维护性

### 📜 v1.0 - 2023年
**初始版本发布**
- 🎯 三种方法对比建模框架
- 📈 基础缺失值分析和填补功能  
- 📊 性能评估和报告生成
- ⚙️ 参数调优和交叉验证
- 📖 基础文档和使用说明

---

## 📞 技术支持

如有问题或建议，请通过以下方式联系：
- 📧 提交Issue到项目仓库
- 📝 查看详细的代码注释
- 📊 参考性能报告中的诊断信息
- 🔍 使用测试模式进行问题定位

---

*本项目致力于为土壤科学研究提供高质量的数据处理工具，持续改进以满足科研需求。*
