# 土壤剖面数据缺失值填补系统

## 项目概述

本项目实现了基于三种方法（普通克里金、回归克里金、随机森林）的土壤剖面数据缺失值填补系统。系统自动选择最优方法进行缺失值填补，并生成详细的性能报告。

## 核心特性

- ✅ **三种方法自动对比**：普通克里金、回归克里金、随机森林
- ✅ **自动参数调优**：基于交叉验证的参数优化
- ✅ **智能缺失值填补**：只对有缺失值的深度层建模
- ✅ **邻近层协变量**：使用邻近深度层作为协变量
- ✅ **完整评估指标**：R²、RMSE、MAE、CCC、RPD
- ✅ **完整数据输出**：基于辅助变量数据的完整填补结果
- ✅ **详细性能报告**：Excel格式的多工作表报告

## 文件结构

### 主程序
- `01_main.R` - 主程序入口，全自动运行
- `02_config.yml` - 配置文件，所有参数设置

### 建模方法
- `03_modeling_methods.R` - 三种建模方法统一实现（普通克里金、回归克里金、随机森林）

### 工具模块
- `04_common.R` - 通用工具函数
- `05_data.R` - 数据加载和预处理
- `06_modeling.R` - 建模数据准备
- `07_tuning.R` - 参数调优
- `08_evaluation.R` - 交叉验证和评估
- `09_prediction.R` - 预测和缺失值填补
- `10_report.R` - 报告生成

## 使用方法

### 1. 配置设置
编辑 `02_config.yml` 文件：
```yaml
# 测试模式（建议先用测试模式验证）
experiment:
  test_mode: true
  test_counties: ["梨树"]
  test_properties: ["pH"]
  test_years: [1980]

# 输出文件名
gap_filling:
  filled_excel_filename: "缺失值填补-完整数据.xlsx"
  report_filename: "性能报告.xlsx"
```

### 2. 运行系统
在RStudio中运行：
```r
source('01_main.R')
```

系统将自动：
1. 加载数据和配置
2. 分析缺失值情况
3. 对有缺失值的深度层进行三种方法建模
4. 自动选择最优方法填补缺失值
5. 生成填补结果和性能报告

### 3. 查看结果
输出文件位于 `../00 原始数据/three_methods_results/` 目录：
- 填补结果：基于basic_data结构的Excel文件
- 性能报告：包含详细统计和方法对比的Excel报告

## 输出说明

### 填补结果文件
- **结构**：基于 `标准化-两期完整数据.xlsx` 的列结构
- **内容**：只包含基础信息和土壤属性，不含环境变量
- **格式**：Excel格式，可直接用于后续分析

### 性能报告文件
包含4个工作表：
1. **详细填补记录**：每个任务的最佳方法和评估指标
2. **汇总统计**：总体性能统计
3. **方法使用统计**：各方法被选为最佳的频率
4. **深度层统计**：按深度层的性能分析

## 评估指标说明

- **R²**：决定系数，越接近1越好
- **RMSE**：均方根误差，越小越好
- **MAE**：平均绝对误差，越小越好
- **CCC**：Lin's一致性相关系数，越接近1越好
- **RPD**：性能与偏差比，>2为优秀，1.4-2为良好

## 技术特点

### 数据处理
- 使用完整数据（含环境变量）进行建模
- 输出基础数据（只含基础信息）保持纯净
- 自动处理邻近深度层协变量

### 方法选择
- 基于R²优先的自动选择策略
- 支持参数调优和交叉验证
- 智能回退策略确保填补成功

### 质量保证
- 完整的评估指标体系
- 详细的日志和报告
- 可追溯的填补记录

## 注意事项

1. **数据要求**：需要相关性分析结果文件
2. **内存使用**：大数据集建议分批处理
3. **运行时间**：完整分析可能需要较长时间
4. **结果验证**：建议检查填补结果的合理性

## 更新日志

- **v1.0**：基础三种方法实现
- **v1.1**：增加CCC和RPD评估指标
- **v1.2**：优化输出为基础数据结构
- **v1.3**：清理冗余代码，优化项目结构
