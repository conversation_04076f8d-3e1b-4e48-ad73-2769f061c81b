# coding: utf-8
"""
该脚本用于从Google Earth Engine的SoilGrids 2.0数据集中提取土壤环境变量。
功能:
1. 采用统一的深度处理逻辑，对所有变量使用加权平均法计算目标深度分层
2. 高效处理GEE资源，避免重复计算几何体边界
3. 优化客户端-服务器交互，提高处理效率
"""

# ---- 1. 依赖区 ----
import ee
import re

# ---- 2. 配置区 ----

# Google Drive中用于存储结果的文件夹名称
DRIVE_EXPORT_FOLDER = 'GEE_SoilGrids_250m'
# 目标投影坐标系
TARGET_CRS = 'EPSG:4550'
# 导出影像的分辨率（米）
EXPORT_SCALE = 250
# GEE项目ID
GEE_PROJECT_ID = 'ee-aethxz247'

# 目标深度(cm)
TARGET_DEPTHS = [(0, 10), (10, 30), (30, 60), (60, 100)]

# SoilGrids数据集
SOILGRIDS_ASSETS = {
    'bdod': {
        'path': 'projects/soilgrids-isric/bdod_mean',
        'mapped_unit': 'cg/cm³', 'conversion_factor': 100, 'conventional_unit': 'kg-dm-3', 'description': '土壤容重'
    },
    'cec': {
        'path': 'projects/soilgrids-isric/cec_mean',
        'mapped_unit': 'mmol(c)/kg', 'conversion_factor': 10, 'conventional_unit': 'cmol(c)-kg-1', 'description': '阳离子交换量'
    },
    'cfvo': {
        'path': 'projects/soilgrids-isric/cfvo_mean',
        'mapped_unit': 'cm³/dm³', 'conversion_factor': 10, 'conventional_unit': 'vol-percent', 'description': '粗碎片体积分数'
    },
    'clay': {
        'path': 'projects/soilgrids-isric/clay_mean',
        'mapped_unit': 'g/kg', 'conversion_factor': 10, 'conventional_unit': 'percent', 'description': '粘粒含量'
    },
    'nitrogen': {
        'path': 'projects/soilgrids-isric/nitrogen_mean',
        'mapped_unit': 'cg/kg', 'conversion_factor': 100, 'conventional_unit': 'g-kg-1', 'description': '全氮含量'
    },
    'phh2o': {
        'path': 'projects/soilgrids-isric/phh2o_mean',
        'mapped_unit': 'pHx10', 'conversion_factor': 10, 'conventional_unit': 'pH', 'description': '土壤pH值'
    },
    'sand': {
        'path': 'projects/soilgrids-isric/sand_mean',
        'mapped_unit': 'g/kg', 'conversion_factor': 10, 'conventional_unit': 'percent', 'description': '砂粒含量'
    },
    'silt': {
        'path': 'projects/soilgrids-isric/silt_mean',
        'mapped_unit': 'g/kg', 'conversion_factor': 10, 'conventional_unit': 'percent', 'description': '粉粒含量'
    },
    'soc': {
        'path': 'projects/soilgrids-isric/soc_mean',
        'mapped_unit': 'dg/kg', 'conversion_factor': 10, 'conventional_unit': 'g-kg-1', 'description': '土壤有机碳含量'
    },
    'ocd': {
        'path': 'projects/soilgrids-isric/ocd_mean',
        'mapped_unit': 'hg/m³', 'conversion_factor': 10, 'conventional_unit': 'kg-m-3', 'description': '有机碳密度'
    },
    'ocs': {
        'path': 'projects/soilgrids-isric/ocs_mean',
        'mapped_unit': 't/ha', 'conversion_factor': 10, 'conventional_unit': 'kg-m-2', 'description': '有机碳储量'
    }
}

# ---- 3. 工具函数区 ----

def get_source_depths(band_names):
    """从影像的波段名中解析出原始深度 (客户端函数)"""
    depths = []
    # 更新正则表达式以匹配更多深度格式
    pattern = re.compile(r'_?(\d+)[-_](\d+)cm')
    for band_name in band_names:
        match = pattern.search(band_name)
        if match:
            depths.append({
                'band': band_name,
                'start': int(match.group(1)),
                'end': int(match.group(2))
            })
    return sorted(depths, key=lambda x: x['start'])

def is_target_depth_covered(source_depths, target_start, target_end):
    """简单检查目标深度是否有足够数据覆盖"""
    # 检查源深度是否覆盖目标深度的起点和终点
    min_depth = min(d['start'] for d in source_depths) if source_depths else float('inf')
    max_depth = max(d['end'] for d in source_depths) if source_depths else 0
    
    # 如果目标深度完全在源深度范围内，则认为有足够覆盖
    return min_depth <= target_start and target_end <= max_depth

def resample_by_weighted_average(image, source_depths, target_start_cm, target_end_cm, var_name):
    """计算目标深度区间的加权平均值 (服务器端函数)"""
    target_start = ee.Number(target_start_cm)
    target_end = ee.Number(target_end_cm)
    
    weighted_sum = ee.Image(0)
    total_weight = ee.Number(0)

    for depth_info in source_depths:
        source_start = ee.Number(depth_info['start'])
        source_end = ee.Number(depth_info['end'])
        
        overlap_start = source_start.max(target_start)
        overlap_end = source_end.min(target_end)
        overlap_length = overlap_end.subtract(overlap_start).max(0)
        
        weight = overlap_length
        weighted_value = image.select(depth_info['band']).multiply(weight)
        
        weighted_sum = weighted_sum.add(weighted_value)
        total_weight = total_weight.add(weight)

    # 归一化处理
    final_image = weighted_sum.divide(total_weight.max(1e-9))
    band_name = ee.String(f'{var_name}_{target_start_cm}-{target_end_cm}cm')
    return final_image.rename(band_name)

def process_variable(var_name, var_info, aoi_geometry):
    """统一处理变量的函数，简化版本"""
    try:
        print(f"\n-> 正在处理变量: '{var_info['description']}' ({var_name})")
        
        # 加载原始影像
        source_image = ee.Image(var_info['path'])
        
        # 获取并解析源深度
        band_names = source_image.bandNames().getInfo()
        source_depths = get_source_depths(band_names)
        
        # 如果有深度信息，尝试计算目标深度
        if source_depths:
            print(f"  变量具有深度分层，源深度: {[(d['start'], d['end']) for d in source_depths]}")
            bands_to_export = []
            
            # 尝试处理每个目标深度区间
            for start_cm, end_cm in TARGET_DEPTHS:
                # 简单检查是否有足够数据
                if is_target_depth_covered(source_depths, start_cm, end_cm):
                    print(f"    - 计算深度: {start_cm}-{end_cm}cm")
                    # 计算加权平均
                    resampled_band = resample_by_weighted_average(source_image, source_depths, start_cm, end_cm, var_name)
                    bands_to_export.append(resampled_band)
                else:
                    print(f"    - 跳过深度: {start_cm}-{end_cm}cm (数据不足)")

            # 如果创建了任何深度波段，则将它们合并并导出
            if bands_to_export:
                print("  合并所有计算的深度层到一个多波段文件中。")
                multiband_image = ee.Image.cat(bands_to_export)
                
                # 应用单位转换
                final_image = multiband_image.divide(var_info['conversion_factor'])
                
                # 文件命名
                file_description = var_name

                # 配置并启动导出任务
                task_config = {
                    'image': final_image.double(),
                    'description': file_description,
                    'folder': DRIVE_EXPORT_FOLDER,
                    'fileNamePrefix': file_description,
                    'region': aoi_geometry,
                    'scale': EXPORT_SCALE,
                    'crs': TARGET_CRS,
                    'maxPixels': 1e13,
                    'skipEmptyTiles': True
                }
                task = ee.batch.Export.image.toDrive(**task_config)
                task.start()
                print(f"      多波段任务已启动: '{file_description}.tif'")
            else:
                # 如果没有处理任何深度，导出原始数据
                print("  没有可计算的目标深度区间，将直接导出原始深度层。")
                
                # 应用单位转换
                final_image = source_image.divide(var_info['conversion_factor'])

                # 重命名波段以符合 "变量名_深度" 格式
                old_names = [d['band'] for d in source_depths]
                new_names = [f"{var_name}_{d['start']}-{d['end']}cm" for d in source_depths]
                final_image = final_image.select(old_names).rename(new_names)
                
                # 文件命名
                file_description = f'{var_name}_original'
                
                # 配置并启动导出任务
                task_config = {
                    'image': final_image.double(),
                    'description': file_description,
                    'folder': DRIVE_EXPORT_FOLDER,
                    'fileNamePrefix': file_description,
                    'region': aoi_geometry,
                    'scale': EXPORT_SCALE,
                    'crs': TARGET_CRS,
                    'maxPixels': 1e13,
                    'skipEmptyTiles': True
                }
                
                task = ee.batch.Export.image.toDrive(**task_config)
                task.start()
                print(f"  任务已启动: '{file_description}.tif'")
        else:
            # 没有深度分层的变量 - 仍然需要进行单位转换
            print(f"  识别为无深度分层变量，直接导出。")
            
            # 应用单位转换
            final_image = source_image.divide(var_info['conversion_factor'])
            
            # 如果只有一个波段，则重命名为变量名，否则保留原名
            band_names_server = final_image.bandNames()
            final_image = ee.Image(ee.Algorithms.If(
                band_names_server.size().eq(1),
                final_image.rename(var_name),
                final_image
            ))
            
            # 文件命名
            file_description = var_name
            
            # 配置并启动导出任务
            task_config = {
                'image': final_image.double(),
                'description': file_description,
                'folder': DRIVE_EXPORT_FOLDER,
                'fileNamePrefix': file_description,
                'region': aoi_geometry,
                'scale': EXPORT_SCALE,
                'crs': TARGET_CRS,
                'maxPixels': 1e13,
                'skipEmptyTiles': True
            }
            
            task = ee.batch.Export.image.toDrive(**task_config)
            task.start()
            print(f"  任务已启动: '{file_description}.tif'")

        return True
    except Exception as e:
        print(f"  处理变量 '{var_name}' 时出错，已跳过。错误: {e}")
        return False

# ---- 4. 主函数区 ----

def main():
    """主函数：统一高效处理所有变量"""
    # GEE初始化
    try:
        ee.Initialize(project=GEE_PROJECT_ID)
        print("GEE初始化成功。")
    except Exception as e:
        print(f"GEE初始化失败: {e}，正在尝试重新认证...")
        ee.Authenticate()
        ee.Initialize(project=GEE_PROJECT_ID)
        print("GEE认证并初始化成功。")

    # 在初始化后定义GEE对象 - 只计算一次几何体边界
    aoi = ee.FeatureCollection('projects/ee-aethxz247/assets/DSS')
    aoi_geometry = aoi.geometry()

    print("--- 开始处理SoilGrids变量 ---")
    
    # 处理所有变量
    success_count = 0
    for var_name, var_info in SOILGRIDS_ASSETS.items():
        if process_variable(var_name, var_info, aoi_geometry):
            success_count += 1
    
    print(f"\n--- 所有处理和导出任务均已提交，成功处理 {success_count}/{len(SOILGRIDS_ASSETS)} 个变量 ---")
    print(f"请访问 GEE 任务管理器 (https://code.earthengine.google.com/tasks) 监控进度。")
    print(f"文件将被保存在您的 Google Drive 的 '{DRIVE_EXPORT_FOLDER}' 文件夹内。")

# ---- 5. 执行区 ----
if __name__ == '__main__':
    main() 