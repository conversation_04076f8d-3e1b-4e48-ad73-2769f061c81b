# Project Structure

## Directory Organization

### Data Directories
- **`00 原始数据/`**: Raw and processed data storage
  - `raw data/`: Original soil profile datasets
  - `VIF_筛选结果/`: Variance Inflation Factor analysis results
  - `方差分析/`: ANOVA analysis outputs
  - `模型预测结果/`: Model prediction results
  - `相关性分析结果/`: Correlation analysis results

### Analysis Modules
- **`01 数据分析处理/`**: Data preprocessing and standardization
  - Main script: `土壤剖面数据标准化.py`
  - Converts profile data to standardized depth intervals (0-10, 10-30, 30-60, 60-100 cm)

- **`02 环境数据获取与处理/`**: Environmental data extraction and preprocessing
  - `环境辅助变量提取/`: Environmental covariate extraction
  - `预处理/`: Data preprocessing utilities

- **`03 VIF方差膨胀剔除/`**: Multicollinearity analysis
  - `correlation_analysis.py`: Correlation analysis
  - `vif_analysis.py`: Variance Inflation Factor analysis

- **`04 剖面数据重构建模/`**: Spatial interpolation modeling (R-based)
  - `01_main.R`: Main pipeline entry point
  - `02_config.yml`: Configuration file
  - `03_modeling_methods.R`: Three modeling methods (OK, RK, RF)
  - `04_data.R` to `09_report.R`: Modular components

- **`05 时空变化分析/`**: Spatio-temporal analysis (Python-based)
  - `A_main.py`: Main analysis entry point
  - `B_config.py`: Configuration module
  - `C_data_loader.py` to `G_excel_export.py`: Analysis modules

### Utility Directories
- **`工具箱/`**: Utility tools and configurations
- **`office-editor-mcp/`**: Office document processing MCP server
- **`data/`**: Task management data

## File Naming Conventions

### Python Files
- Use descriptive Chinese names for main analysis scripts
- Module files use alphabetical prefixes (A_main.py, B_config.py, etc.)
- Configuration files end with `_config.py`

### R Files
- Numbered prefixes for pipeline order (01_main.R, 02_config.yml, etc.)
- Descriptive function-based names (modeling_methods.R, evaluation.R)

### Data Files
- Excel files use Chinese descriptive names
- Standardized prefix patterns: `标准化-`, `缺失值填补-`, etc.
- Include processing stage in filename

## Configuration Patterns

### Python Configuration
- Centralized config modules (B_config.py)
- Dictionary-based parameter organization
- Separate sections for data paths, analysis parameters, visualization settings

### R Configuration
- YAML-based configuration files
- Hierarchical parameter structure
- Separate sections for data paths, modeling parameters, experimental settings

## Data Flow Architecture

### Processing Pipeline
1. **Raw Data** → `01 数据分析处理/` → **Standardized Data**
2. **Standardized Data** → `04 剖面数据重构建模/` → **Gap-filled Data**
3. **Gap-filled Data** → `05 时空变化分析/` → **Statistical Analysis**

### Key Data Files
- `标准化-两期数据合并.xlsx`: Standardized soil profile data
- `辅助变量-两期数据.xlsx`: Data with environmental covariates
- `嫩江-完整数据.xlsx`: Gap-filled complete dataset

## Module Dependencies

### Cross-Language Dependencies
- R modeling outputs feed into Python analysis modules
- Excel files serve as data exchange format between R and Python
- Configuration files maintain parameter consistency

### Internal Dependencies
- Data standardization must precede spatial modeling
- Correlation analysis informs variable selection for modeling
- Gap-filling results enable comprehensive spatio-temporal analysis

## Output Organization
- Analysis results organized by method and geographic unit
- Visualization outputs saved as high-resolution PNG files
- Statistical reports exported as multi-sheet Excel files
- Consistent naming patterns for traceability