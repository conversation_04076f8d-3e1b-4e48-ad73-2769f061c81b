# Thesis Methodology and Chapter Structure

## Research Framework
This thesis follows a three-stage analytical framework for soil property analysis in Northeast China's black soil region:

### Stage 1: Spatial Interpolation (Chapter 3)
**Objective**: Develop and compare spatial interpolation methods for small sample conditions

### Stage 2: Spatio-temporal Analysis (Chapter 4) 
**Objective**: Analyze temporal changes in soil properties from 1980-2023

### Stage 3: Driver Analysis (Chapter 5)
**Objective**: Identify key driving factors using Redundancy Analysis (RDA)

## Chapter 3: Spatial Interpolation Methods

### Core Methodology
- **Case Study Approach**: Use 嫩江县 (Nenjiang County) as detailed case study
- **Method Comparison**: 4 interpolation methods
  - Ordinary Kriging (OK)
  - Random Forest (RF) 
  - Inverse Distance Weighting (IDW)
  - Regression Kriging (RK)

### Evaluation Framework
- **Metrics**: R², RMSE
- **Validation**: Cross-validation approach
- **Scope**: 5 counties × 6 soil properties × 2 time periods

### Expected Outputs
- Method comparison tables showing interpolation accuracy
- Optimal method selection for each county-property combination
- Complete gap-filled dataset for subsequent analysis
- Spatial reasonableness validation

## Chapter 4: Spatio-temporal Change Analysis

### Analytical Approach
- **Temporal Comparison**: 1980 vs 2023 (43-year span)
- **Statistical Testing**: Paired t-tests for significance
- **Spatial Analysis**: County-level comparison and mapping

### Analysis Dimensions
1. **Overall Trends**: 5 counties × 6 properties statistical comparison
2. **Spatial Patterns**: Change magnitude spatial distribution mapping
3. **Vertical Profiles**: Depth-layer analysis (0-10, 10-30, 30-60, 60-100 cm)
4. **Regional Heterogeneity**: County-specific change patterns

### Expected Outputs
- Statistical comparison tables (1980 vs 2023)
- Spatial distribution maps of property changes
- Depth-profile change analysis
- Regional heterogeneity assessment

## Chapter 5: Driving Force Analysis

### Methodological Framework
- **Primary Method**: Redundancy Analysis (RDA)
- **Data Basis**: Complete interpolated dataset from Chapter 3
- **Scope**: Multi-county integrated analysis

### Driver Classification System
1. **Climate Factors**: Temperature change, precipitation change
2. **Topographic Factors**: Elevation, slope, aspect
3. **Land Use Change**: Cropland change, urban expansion, NDVI change
4. **Socioeconomic Factors**: Population change, GDP change, nighttime light change

### Analysis Workflow
1. **Variable Screening**: VIF analysis (remove variables with VIF > 10)
2. **RDA Modeling**: Build comprehensive RDA model using 5-county data
3. **Model Validation**: Significance testing and variance explanation assessment
4. **Interpretation**: Biplot analysis and ecological gradient interpretation
5. **Quantification**: Driver contribution quantification

### Expected Outputs
- VIF screening results and final driver variable list
- RDA model results with significance tests
- Driver contribution quantification table
- Biplot visualization and interpretation
- County-specific vs. regional driver analysis

## Methodological Principles

### Data Integration Strategy
- Chapter 3 provides complete dataset foundation
- Chapter 4 uses gap-filled data for comprehensive temporal analysis
- Chapter 5 integrates soil changes with environmental drivers

### Quality Assurance
- Cross-validation for interpolation methods
- Statistical significance testing for temporal changes
- Model validation for driver analysis
- Spatial reasonableness checks throughout

### Scale Considerations
- **Spatial Scale**: County-level analysis (not regional mapping)
- **Temporal Scale**: Decadal comparison (1980 vs 2023)
- **Sample Scale**: Small sample adaptation strategies

## Key Constraints and Adaptations

### Small Sample Challenges
- Adapt interpolation methods for limited sample sizes
- Use appropriate cross-validation strategies
- Focus on robust statistical methods

### Data Completeness
- Systematic gap-filling approach in Chapter 3
- Ensure data completeness before temporal and driver analysis
- Validate interpolation results for spatial reasonableness

### Multi-scale Integration
- County-level focus with regional context
- Depth-profile consideration in all analyses
- Multi-temporal integration across 43-year span