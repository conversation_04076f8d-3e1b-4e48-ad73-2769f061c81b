import os
import rasterio
import numpy as np

# --- 配置区 ---
# 在这里配置你希望查看的固定行号和列的切片范围
ROWS_TO_DISPLAY = [100, 500, 1000]  # 要显示的固定行号列表
COL_SLICE = slice(50, 60)         # 要显示的列切片范围, e.g., slice(50, 60) 表示第50到59列

def analyze_tif_files_final(folder_path):
    """
    以精简、确定性的方式分析TIF文件，重点关注NoData比例和固定行的数据对比。
    """
    tif_files = [f for f in os.listdir(folder_path) if f.lower().endswith(('.tif', '.tiff'))]
    if not tif_files:
        print(f"在目录 '{folder_path}' 中未找到TIF文件。")
        return

    print(f"\n===== TIF文件分析报告 (共 {len(tif_files)} 个) =====")

    for tif_file in tif_files:
        tif_path = os.path.join(folder_path, tif_file)
        try:
            with rasterio.open(tif_path) as src:
                print(f"\n--- 文件: {tif_file} ---")
                print(f"  CRS: {src.crs} | 分辨率: {src.res} | 波段数: {src.count}")

                nodata_val = src.nodata

                for i, band_index in enumerate(src.indexes):
                    band_descriptions = src.descriptions
                    band_name = band_descriptions[i] if band_descriptions and i < len(band_descriptions) and band_descriptions[i] else f"波段 {band_index}"
                    
                    band_array = src.read(band_index)
                    
                    mask = np.zeros(band_array.shape, dtype=bool)
                    if nodata_val is not None:
                        # 处理非NaN的NoData值
                        if not np.isnan(nodata_val):
                            mask |= (band_array == nodata_val)
                    # 总是检查浮点数的NaN
                    if np.issubdtype(band_array.dtype, np.floating):
                        mask |= np.isnan(band_array)

                    band_data = np.ma.masked_array(band_array, mask=mask)
                    valid_pixels = np.ma.count(band_data)
                    total_pixels = band_data.size
                    nodata_pixels = total_pixels - valid_pixels

                    print(f"  - {band_name} (类型: {band_array.dtype}):")

                    if total_pixels > 0:
                        valid_perc = (valid_pixels / total_pixels) * 100
                        nodata_perc = (nodata_pixels / total_pixels) * 100
                        print(f"    有效/NoData比例: {valid_perc:.2f}% / {nodata_perc:.2f}%")

                    if valid_pixels > 0:
                        min_val, max_val = np.ma.min(band_data), np.ma.max(band_data)
                        print(f"    有效数据范围: [{min_val:.4f}, {max_val:.4f}]")

                        print(f"    固定位置数据样本 (列 {COL_SLICE.start}-{COL_SLICE.stop-1}):")
                        for row_idx in ROWS_TO_DISPLAY:
                            if row_idx < src.height:
                                data_slice = band_data[row_idx, COL_SLICE]
                                # 格式化输出，NoData显示为 '---'
                                slice_str = " ".join([f"{x:.2f}" if x is not np.ma.masked else "---" for x in data_slice])
                                print(f"      行 {row_idx:<4}: [ {slice_str} ]")
                            
                    else:
                        print("    此波段不包含有效数据。")

        except Exception as e:
            print(f"\n处理文件 {tif_file} 时出错: {e}")
        
    print("\n" + "="*50)


if __name__ == "__main__":
    # 设置包含TIF文件的文件夹路径
    tif_folder = r"D:\Desktop\辅助变量\1980\三维"  # <--- 请将此路径替换为你的文件夹路径

    analyze_tif_files_final(tif_folder)