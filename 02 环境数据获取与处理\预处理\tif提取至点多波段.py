import rasterio
from rasterio.warp import calculate_default_transform, reproject, Resampling
import pandas as pd
import geopandas as gpd
from shapely.geometry import Point
import numpy as np
import os
import re
from tqdm import tqdm

# ---- 配置 ----
# 在这里配置你的文件路径和列名
CONFIG = {
    # 输入点数据文件 (Excel)
    "points_file": r"D:\Desktop\小论文\嫩江-整理剖面数据完整版 - VIF.xlsx",
    
    # 使用的工作表。可以是工作表名称（如 'Sheet1'）或索引（0代表第一个表, 1代表第二个表）
    "sheet_name": 0,
    
    # 包含TIF栅格文件的文件夹
    "raster_folder": r"D:\Desktop\辅助变量\2023",
    
    # 输出Excel文件的路径
    "output_excel": r"D:\Desktop\2023.xlsx",
    
    # 点数据的坐标系 (例如 WGS84)
    "points_crs": 'EPSG:4326',
    
    # 列名配置
    "lon_col": 'Longitude',      # 经度列名
    "lat_col": 'Latitude',      # 纬度列名
    "depth_col": '深度范围',   # 深度列名，用于多波段栅格
}

def _parse_start_depth(depth_str):
    """从 '0-10' 或 '10' 这样的字符串中提取起始深度作为整数。"""
    try:
        # 使用正则表达式查找开头的数字
        match = re.match(r'^\s*(\d+)', str(depth_str))
        if match:
            return int(match.group(1))
        return float('inf') # 如果没有找到数字，返回无穷大以便排序
    except (ValueError, IndexError):
        return float('inf') # 出错时也返回无穷大

def extract_values_from_raster(raster_path, points_df, config):
    """
    从栅格文件提取指定点位置的值。
    此版本经过简化，严格遵循唯一策略：
    1. 从Excel文件中获取所有唯一的深度范围。
    2. 对这些深度范围进行排序。
    3. 假设TIF文件的波段顺序与排序后的深度范围顺序完全对应。
       （例如，排序后的第一个深度对应波段1，第二个深度对应波段2，以此类推）
    此方法不再尝试从TIF的元数据或文件名中解析信息。
    """
    lon_col = config['lon_col']
    lat_col = config['lat_col']
    depth_col = config.get('depth_col')
    points_crs = config['points_crs']
    filename = os.path.basename(raster_path)

    try:
        with rasterio.open(raster_path) as src:
            # --- 1. 初始化和坐标转换 ---
            raster_crs = src.crs
            band_count = src.count
            nodata_value = src.nodata

            geometry = [Point(xy) for xy in zip(points_df[lon_col], points_df[lat_col])]
            gdf = gpd.GeoDataFrame(points_df, geometry=geometry, crs=points_crs)

            if gdf.crs != raster_crs:
                gdf = gdf.to_crs(raster_crs)
            
            # --- 2. 构建深度到波段的映射 (强制按Excel深度顺序) ---
            if not depth_col or depth_col not in gdf.columns:
                print(f"错误: 文件 '{filename}' 需要一个有效的深度列 (在CONFIG中配置的'{depth_col}')来进行波段匹配。")
                return [np.nan] * len(points_df)

            unique_depths = points_df[depth_col].astype(str).unique()
            sorted_unique_depths = sorted(unique_depths, key=_parse_start_depth)
            
            depth_to_band_map = {
                depth: i + 1 
                for i, depth in enumerate(sorted_unique_depths) 
                if _parse_start_depth(depth) != float('inf') and (i + 1) <= band_count
            }

            if not depth_to_band_map:
                print(f"错误: 文件 '{filename}' 未能根据Excel深度列构建任何有效的波段映射。")
                return [np.nan] * len(points_df)

            # --- 3. 性能优化：预加载所有需要的波段到内存 ---
            band_data_cache = {}
            required_bands = set(depth_to_band_map.values())
            
            for band_idx in sorted(list(required_bands)):
                try:
                    band_data_cache[band_idx] = src.read(band_idx)
                except IndexError:
                    print(f"警告: 尝试读取文件 '{filename}' 的波段 {band_idx} 失败，该波段不存在。")


            # --- 4. 提取值 (从内存中快速查找) ---
            values = []
            unmapped_depths = set()
            for index, point in gdf.geometry.items():
                try:
                    row, col = src.index(point.x, point.y)
                    
                    depth_str = str(points_df.loc[index, depth_col])
                    band_index = depth_to_band_map.get(depth_str)

                    if band_index and band_index in band_data_cache:
                        value = band_data_cache[band_index][row, col]
                    else:
                        unmapped_depths.add(depth_str)
                        values.append(np.nan)
                        continue

                    if nodata_value is not None and value == nodata_value:
                        values.append(np.nan)
                    else:
                        values.append(float(value))

                except IndexError:
                    values.append(np.nan) # 点在栅格范围之外
                except Exception as point_error:
                    print(f"警告: 处理点索引 {index} 时出错: {point_error}。该点值为NaN。")
                    values.append(np.nan)

            # --- 5. 报告 ---
            if unmapped_depths:
                print(f"警告: 在文件 {filename} 中, Excel中的以下深度范围未找到对应波段: {sorted(list(unmapped_depths))}")

        return values

    except Exception as e:
        print(f"处理文件 {os.path.basename(raster_path)} 时出错: {str(e)}")
        return [np.nan] * len(points_df)


def process_multiple_rasters(config):
    """
    处理文件夹中的多个TIF文件并将结果保存到Excel
    """
    # 从配置中获取路径和列名
    raster_folder = config['raster_folder']
    points_file = config['points_file']
    output_excel = config['output_excel']
    lon_col = config['lon_col']
    lat_col = config['lat_col']
    sheet_name = config.get('sheet_name', 0)
    
    # 读取点数据
    try:
        points_df = pd.read_excel(points_file, sheet_name=sheet_name)
        print(f"从工作表 '{sheet_name}' 读取点数据完成，共 {len(points_df)} 个点")
    except FileNotFoundError:
        print(f"错误: 找不到点文件 {points_file}")
        return
    except Exception as e:
        print(f"读取点文件时出错: {str(e)}")
        return

    # 检查输入数据
    if lon_col not in points_df.columns or lat_col not in points_df.columns:
        print(f"错误：输入的DataFrame必须包含经度列'{lon_col}'和纬度列'{lat_col}'")
        return

    # 获取所有tif文件 (递归搜索)
    tif_paths = []
    try:
        print(f"正在递归搜索 '{raster_folder}' 文件夹中的TIF文件...")
        for root, _, files in os.walk(raster_folder):
            for file in files:
                if file.lower().endswith(('.tif', '.tiff')):
                    tif_paths.append(os.path.join(root, file))
        
        if not tif_paths:
            print(f"错误：在文件夹 '{raster_folder}' 及其子文件夹中没有找到TIF文件")
            return
    except FileNotFoundError:
        print(f"错误: 找不到栅格文件夹 {raster_folder}")
        return

    print(f"找到 {len(tif_paths)} 个TIF文件，开始处理...")

    # 性能优化：先将所有结果收集到字典中，而不是逐列添加到DataFrame
    results_data = {}

    # 使用tqdm显示进度条，并静默处理每个文件
    for raster_path in tqdm(tif_paths, desc="提取TIF数据"):
        # 提取值
        values = extract_values_from_raster(raster_path, points_df, config)

        # 将结果添加到字典中
        base_name = os.path.splitext(os.path.basename(raster_path))[0]
        # 去除文件名末尾的年份标识 (例如, "-2023" 或 "_2023")
        column_name = re.sub(r'[-_]\d{4}$', '', base_name)
        results_data[column_name] = values

    # 所有文件处理完毕后，一次性将结果合并到DataFrame
    print("\n所有文件值提取完毕，正在合并到最终表格中...")
    try:
        results_df = pd.DataFrame(results_data)
        final_df = pd.concat([points_df, results_df], axis=1)
    except Exception as e:
        print(f"合并数据时出错: {e}")
        print("将尝试逐列合并作为备用方案...")
        final_df = points_df.copy()
        for name, val_list in results_data.items():
            final_df[name] = val_list

    # 确保输出目录存在
    output_dir = os.path.dirname(output_excel)
    if output_dir and not os.path.exists(output_dir):
        try:
            os.makedirs(output_dir)
            print(f"已创建输出目录: {output_dir}")
        except Exception as e:
            print(f"创建输出目录时出错: {str(e)}")
            output_excel = os.path.join(os.getcwd(), os.path.basename(output_excel))
            print(f"已更改输出路径为: {output_excel}")

    # 保存到Excel
    try:
        final_df.to_excel(output_excel, index=False)
        print(f"处理完成！结果已保存到: {output_excel}")
    except Exception as e:
        print(f"保存Excel文件时出错: {str(e)}")
        try:
            fallback_path = os.path.join(os.getcwd(), f"points_with_values_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}.xlsx")
            final_df.to_excel(fallback_path, index=False)
            print(f"已保存结果到备用位置: {fallback_path}")
        except Exception as e2:
            print(f"保存到备用位置时也出错: {str(e2)}")


if __name__ == "__main__":
    # 处理所有TIF文件并保存结果
    process_multiple_rasters(CONFIG)