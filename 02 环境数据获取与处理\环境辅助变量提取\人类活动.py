# -*- coding: utf-8 -*-
"""
================================================================================
长时间序列人类活动相关地理空间数据获取脚本 (Google Earth Engine)
================================================================================

**脚本目的:**
本脚本旨在通过 Google Earth Engine (GEE) 平台，获取两个关键时间节点（约1980年
和约2023年）与人类活动强度相关的地理空间数据。

**获取数据项及来源:**
脚本将处理并导出以下三种核心数据：

1.  **人口格网 (Population Grid):**
    -   **来源数据集:** JRC/GHSL/P2023A/GHS_POP
    -   **发布机构:** 欧盟委员会联合研究中心 (EC JRC)
    -   **描述:** 反映了每个格网单元内的人口估算数量。

2.  **建成区地表 (Built-up Surface):**
    -   **来源数据集:** JRC/GHSL/P2023A/GHS_BUILT_S
    -   **发布机构:** 欧盟委员会联合研究中心 (EC JRC)
    -   **描述:** 反映了每个格网单元内被建筑物覆盖的地表面积。

3.  **协调后夜间灯光 (Harmonized Nighttime Lights):**
    -   **来源数据集:** projects/sat-io/open-datasets/Harmonized_NTL
    -   **发布机构:** Li, X. et al. (2020)
    -   **描述:** 经过交叉校正的DMSP和VIIRS灯光数据，可用于直接进行长时间序列对比。

**重要说明:**
-   **代理年份:** 由于历史数据存档的限制，脚本会自动为目标年份选取最接近的
    可用数据年份作为代理，并在运行时打印相关信息。
-   **输出:** 脚本将为每个目标年份的每一种数据，分别导出一个独立的、经过
    投影和重采样的单波段 GeoTIFF 文件到您的 Google Drive。

================================================================================
"""

import ee
import time

# GEE初始化
try:
    project_id = 'ee-aethxz247'
    ee.Initialize(project=project_id)
    print(f"GEE初始化成功！使用项目: {project_id}")
except Exception as e:
    print(f"GEE初始化失败，即使在强制指定项目后也是如此。这可能是网络问题或账户权限问题。")
    print(f"错误信息: {e}")
    exit()

# --- 核心参数配置 ---

# 定义研究区域 (Region of Interest, ROI)
try:
    ROI = ee.FeatureCollection('projects/ee-aethxz247/assets/DSS')
    # 进行一次有效性检查
    roi_size = ROI.size().getInfo()
    if roi_size == 0:
        print("错误: 研究区域(ROI)为空，请检查GEE资产路径 'projects/ee-aethxz247/assets/DSS' 或其权限设置。")
        exit()
    print(f"研究区域(ROI)加载成功，源: 'projects/ee-aethxz247/assets/DSS'，包含 {roi_size} 个要素。")
except Exception as e:
    print(f"错误: 无法加载研究区域(ROI)。请确认路径 'projects/ee-aethxz247/assets/DSS' 是否正确，以及您是否有权访问。")
    print(f"GEE返回的错误信息: {e}")
    exit()

# 定义目标年份
# 请注意：由于历史数据源的限制，部分数据会使用最接近的年份作为代理
YEARS = [1980, 2023]

# Google Drive 中用于保存结果的文件夹名称
OUTPUT_FOLDER = 'GEE_Human_Activity_Data'

# --- 数据获取函数 ---

def get_population(year, region):
    """
    获取 GHS-POP 全球人口格网数据。
    使用最接近年份的数据作为代理。
    原始分辨率: 约 100米 (3 arc-seconds)
    数据可用年份: 1975, 1980, 1985, 1990, 1995, 2000, 2005, 2010, 2015, 2020
    """
    pop_collection = ee.ImageCollection('JRC/GHSL/P2023A/GHS_POP')
    
    # 根据目标年份选择最接近的代理年份
    if year == 1980:
        proxy_year = 1980
    else: # for 2023
        proxy_year = 2020
    
    if year != proxy_year:
        print(f"信息: {year}年人口数据，使用最接近的 {proxy_year} 年数据作为代理。")
        
    pop_image = pop_collection.filter(ee.Filter.date(f'{proxy_year}-01-01', f'{proxy_year}-12-31')).first()
    return pop_image.select('population_count').clip(region).rename(f'population_{year}')


def get_built_up(year, region):
    """
    获取 GHS-BUILT-S 全球建成区格网数据。
    该数据集反映了建筑物的存在情况。
    原始分辨率: 约 100米 (3 arc-seconds)
    数据可用年份: 1975, 1980, 1985, 1990, 1995, 2000, 2005, 2010, 2015, 2020, 2025, 2030
    """
    # 使用官方最新的正确ID
    built_collection = ee.ImageCollection('JRC/GHSL/P2023A/GHS_BUILT_S')

    # 根据目标年份选择最接近的代理年份
    if year == 1980:
        proxy_year = 1980
    else: # for 2023
        # 为保证数据是基于真实观测而非模型预测，选用最新的观测年份2020年
        proxy_year = 2020

    if year != proxy_year:
        print(f"信息: {year}年建成区数据，使用最新的真实观测年份 {proxy_year} 的数据作为代理。")
        
    built_image = built_collection.filter(ee.Filter.date(f'{proxy_year}-01-01', f'{proxy_year}-12-31')).first()
    return built_image.select('built_surface').clip(region).rename(f'built_up_{year}')


def get_night_lights(year, region):
    """
    获取协调后的夜间灯光数据 (Harmonized DMSP-VIIRS)。
    这是一个经过交叉校正的融合数据集，解决了DMSP和VIIRS两个不同传感器之间的不一致问题，
    使得长时间的灯光数据可以直接对比，是长时序分析的最佳选择。
    原始分辨率: 约 1公里 (30 arc-seconds)
    数据可用年份: 1992-2021
    """
    # 根据目标年份选择最接近的代理年份
    if year == 1980:
        proxy_year = 1992 # 最早可用
    else: # for 2023
        proxy_year = 2021 # 最新可用
    
    print(f"信息: {year}年夜间灯光数据，使用最接近的 {proxy_year} 年协调数据作为代理。")

    # 根据代理年份选择正确的集合 (DMSP或VIIRS)
    if proxy_year < 2014:
        collection = ee.ImageCollection("projects/sat-io/open-datasets/Harmonized_NTL/dmsp")
    else:
        collection = ee.ImageCollection("projects/sat-io/open-datasets/Harmonized_NTL/viirs")
    
    image = collection.filter(ee.Filter.date(f'{proxy_year}-01-01', f'{proxy_year}-12-31')).first()
    return image.select('b1').clip(region).rename(f'ntl_{year}')


# --- 主执行函数 ---
def export_human_activity_data():
    """
    主函数，用于处理和导出所有数据。
    将为每个年份的每种数据分别创建一个导出任务。
    """
    print(f"开始处理年份: {YEARS}")
  
    for year in YEARS:
        print(f"\n--- 正在处理 {year}年 ---")

        # 获取所有数据层
        pop = get_population(year, ROI.geometry())
        built = get_built_up(year, ROI.geometry())
        ntl = get_night_lights(year, ROI.geometry())
        
        # 1. 导出人口数据
        pop_task = ee.batch.Export.image.toDrive(
            image=pop,
            description=f'Population_{year}',
            folder=f"{OUTPUT_FOLDER}/{year}",
            fileNamePrefix=f'Population_{year}',
            region=ROI.geometry(),
            scale=250,
            crs='EPSG:4550',
            maxPixels=1e13
        )
        pop_task.start()
        print(f"  --> GEE任务 'Population_{year}' 已启动。")

        # 2. 导出建成区数据
        built_task = ee.batch.Export.image.toDrive(
            image=built,
            description=f'Built_Up_{year}',
            folder=f"{OUTPUT_FOLDER}/{year}",
            fileNamePrefix=f'Built_Up_{year}',
            region=ROI.geometry(),
            scale=250,
            crs='EPSG:4550',
            maxPixels=1e13
        )
        built_task.start()
        print(f"  --> GEE任务 'Built_Up_{year}' 已启动。")

        # 3. 导出夜间灯光数据
        ntl_task = ee.batch.Export.image.toDrive(
            image=ntl,
            description=f'NTL_{year}',
            folder=f"{OUTPUT_FOLDER}/{year}",
            fileNamePrefix=f'NTL_{year}',
            region=ROI.geometry(),
            scale=250,
            crs='EPSG:4550',
            maxPixels=1e13
        )
        ntl_task.start()
        print(f"  --> GEE任务 'NTL_{year}' 已启动。")

    print(f"\n所有年份的 {len(YEARS) * 3} 个导出任务均已启动。")
    print("请访问 https://code.earthengine.google.com/tasks 页面查看并手动运行它们。")


if __name__ == '__main__':
    start_time = time.time()
    export_human_activity_data()
    end_time = time.time()
    print(f"\n脚本执行完毕，总耗时: {end_time - start_time:.2f} 秒。") 